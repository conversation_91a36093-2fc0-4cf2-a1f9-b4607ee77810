export class WholeSale {
    _id?: string;
    name: string;
    address: Address;
    associatedDonutAnimator: AssociatedDonutAnimator;
    tel?: number;
}


export class AssociatedDonutAnimator {
    _id: string | { $oid: string };
    lastName: string;
    email: string;
    tel: number
}

declare type Address = {
    region?: string;
    city?: string;
    district?: string;
    commercialRegion: string;
}