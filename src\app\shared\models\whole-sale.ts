export class WholeSale {
    _id?: string;
    name: string;
    address: Address;
    associatedDonutAnimator: string | AssociatedDonutAnimator; // Peut être un ID ou un objet complet
    tel?: number;
}


export class AssociatedDonutAnimator {
    _id: string | { $oid: string };
    lastName: string;
    email: string;
    tel: number
}

declare type Address = {
    region?: string;
    city?: string;
    district?: string;
    commercialRegion: string;
}