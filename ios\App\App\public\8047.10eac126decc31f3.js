"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8047],{40129:(v,O,t)=>{t.d(O,{vi:()=>h});const h=(0,t(22126).F3)("BarcodeScanner",{web:()=>t.e(5499).then(t.bind(t,85499)).then(o=>new o.BarcodeScannerWeb)})},32928:(v,O,t)=>{t.d(O,{Y:()=>n});var e=t(44912),d=t(53229);function n(s=0,p=e.E){return s<0&&(s=0),(0,d.O)(s,s,p)}},82115:(v,O,t)=>{t.d(O,{o:()=>p});var e=t(2978),d=t(77575),n=t(77897),s=t(74657);let p=(()=>{class h{constructor(P,m,M){this.router=P,this.platform=m,this.modalCtrl=M}ngOnInit(){this.sabitouLink=this.platform.is("android")?"https://play.google.com/store/apps/details?id=lnd.cimencam.sabitou_construction&hl=en&gl=US":"https://apps.apple.com/us/app/sabitou-construction/id1453574772"}onNoClick(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(m){return new(m||h)(e.rXU(d.Ix),e.rXU(n.OD),e.rXU(n.W3))}}static{this.\u0275cmp=e.VBU({type:h,selectors:[["app-redirect-to-sabitou"]],decls:17,vars:10,consts:[[1,"mat-dialog"],["src","/assets/images/sabitou.png",1,"img-container"],[1,"text-form"],["src","/assets/icons/institutions-logos-cimencam-binastore.svg",1,"lafrageimg"],[1,"mat-dialogButton"],[1,"button","back",3,"click"],[1,"button","download"],[3,"href"]],template:function(m,M){1&m&&(e.j41(0,"div",0)(1,"div"),e.nrm(2,"ion-img",1),e.j41(3,"ion-title"),e.EFF(4,"Sabitou Construction"),e.k0s(),e.j41(5,"ion-label",2),e.EFF(6),e.nI1(7,"translate"),e.k0s(),e.nrm(8,"ion-img",3),e.k0s(),e.j41(9,"div",4)(10,"div",5),e.bIt("click",function(){return M.onNoClick()}),e.EFF(11),e.nI1(12,"translate"),e.k0s(),e.j41(13,"div",6)(14,"a",7),e.EFF(15),e.nI1(16,"translate"),e.k0s()()()()),2&m&&(e.R7$(6),e.SpI(" ",e.bMT(7,4,"redirect-sabitou-modal.text")," "),e.R7$(5),e.SpI(" ",e.bMT(12,6,"button.cancel"),""),e.R7$(3),e.Y8G("href",M.sabitouLink,e.B4B),e.R7$(1),e.JRh(e.bMT(16,8,"button.download")))},dependencies:[n.KW,n.he,n.BC,s.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.mat-dialog[_ngcontent-%COMP%]{padding:calc(41 * var(--res))}.mat-dialog[_ngcontent-%COMP%]   .img-container[_ngcontent-%COMP%]{width:100%}.mat-dialog[_ngcontent-%COMP%]   .mat-dialogButton[_ngcontent-%COMP%]{font-family:Mont Regular;display:flex;justify-content:space-around}.mat-dialog[_ngcontent-%COMP%]   .text-form[_ngcontent-%COMP%]{text-align:center;letter-spacing:0,5px;font-family:Mont Regular;margin-bottom:16px}.mat-dialog[_ngcontent-%COMP%]   .lafrageimg[_ngcontent-%COMP%]{margin-bottom:20px;width:69%;margin-left:14%}.mat-dialog[_ngcontent-%COMP%]   .button[_ngcontent-%COMP%]{font-size:14px;padding:12px 19px;font-family:Mont Bold;border-radius:4px}.mat-dialog[_ngcontent-%COMP%]   .back[_ngcontent-%COMP%]{background-color:#143c5d;color:#fff;border:#a3a6a8 1px solid}.mat-dialog[_ngcontent-%COMP%]   .download[_ngcontent-%COMP%]{background-color:#ffc500}.mat-dialog[_ngcontent-%COMP%]   .download[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none;color:#fff}"]})}}return h})()},26843:(v,O,t)=>{t.d(O,{v:()=>P});var e=t(73308),d=t(35025),n=t.n(d),s=t(2978),p=t(77897),h=t(14599),o=t(74657);let P=(()=>{class m{constructor(g,l,a){this.platform=g,this.storageSrv=l,this.popOver=a,this.isContentShown=!1}ngOnInit(){this.storeLink=this.platform.is("android")?"https://play.google.com/store/apps/details?id=com.clickcadyst.mobile":"https://apps.apple.com/us/app/clic cadyst/id1467838902"}cancel(){var g=this;return(0,e.A)(function*(){yield g.storageSrv.store("lastDeniedUpdateApp",n()().toDate().getTime()),g.popOver.dismiss()})()}static{this.\u0275fac=function(l){return new(l||m)(s.rXU(p.OD),s.rXU(h.n),s.rXU(p.IE))}}static{this.\u0275cmp=s.VBU({type:m,selectors:[["app-update-app-modal"]],decls:18,vars:10,consts:[[1,"dialog-content"],[1,"phone-logo"],[1,"phone"],[1,"text"],[1,"mcm-btn-navigate-container"],[1,"mc-btn","full-width"],[1,"store-link",3,"href"],[1,"mcm-btn-navigate-container-close"],["color","light",1,"mc-btn","full-width","white-update",3,"click"],[1,"store-link"]],template:function(l,a){1&l&&(s.j41(0,"div",0)(1,"div",1),s.nrm(2,"div",2),s.k0s(),s.j41(3,"h1"),s.EFF(4,"CLICK CADYST"),s.k0s(),s.j41(5,"p",3),s.EFF(6),s.nI1(7,"translate"),s.k0s(),s.j41(8,"div",4)(9,"ion-button",5)(10,"a",6),s.EFF(11),s.nI1(12,"translate"),s.k0s()()(),s.j41(13,"div",7)(14,"ion-button",8),s.bIt("click",function(){return a.cancel()}),s.j41(15,"a",9),s.EFF(16),s.nI1(17,"translate"),s.k0s()()()()),2&l&&(s.R7$(6),s.SpI(" ",s.bMT(7,4,"home-page.update-text")," "),s.R7$(4),s.Y8G("href",a.storeLink,s.B4B),s.R7$(1),s.SpI(" ",s.bMT(12,6,"home-page.uptodate"),""),s.R7$(5),s.SpI(" ",s.bMT(17,8,"home-page.update-cancel")," "))},dependencies:[p.Jm,o.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.dialog-content[_ngcontent-%COMP%]{padding:16px}.dialog-content[_ngcontent-%COMP%]   .phone-logo[_ngcontent-%COMP%]{height:200px;width:100%;height:22vh;display:flex;align-items:center;justify-content:center}.dialog-content[_ngcontent-%COMP%]   .phone-logo[_ngcontent-%COMP%]   .phone[_ngcontent-%COMP%]{height:100%;width:100%;background-image:url(updatepasta-icon.52e001d874e40d5e.png);background-repeat:no-repeat;background-position:center;background-size:contain}.dialog-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:Mont Regular;font-size:.7em;text-align:center;margin:0}.dialog-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:10px 0 25px;font-family:Mont Bold;font-size:12px;text-align:center}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]{bottom:16px;position:unset;width:100%;margin:25px 0 0}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]   .mc-btn[_ngcontent-%COMP%]{width:100%;margin-top:4em}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]   .store-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;text-decoration:none;color:#fff;width:100%;height:100%;text-transform:uppercase;font-family:Mont Regular}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]{bottom:16px;position:unset;width:100%;margin:10px 0 0;color:#ffffffbd}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .mc-btn[_ngcontent-%COMP%]{width:100%}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .store-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;text-decoration:none;color:--ion-color-primary;width:100%;height:100%;text-transform:uppercase;font-family:Mont Regular}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .store-link.cancel[_ngcontent-%COMP%]{color:#143c5d}"]})}}return m})()},39316:(v,O,t)=>{t.d(O,{b:()=>m});var e=t(73308),d=t(26409),n=t(94934),s=t(45312),p=t(2978),h=t(82571),o=t(33607),P=t(77897);let m=(()=>{class M{constructor(l,a,i,c){this.http=l,this.commonSrv=a,this.baseUrlService=i,this.toastController=c,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+s.c.basePath+"products"}getProducts(l){var a=this;return(0,e.A)(function*(){try{let i=new d.Nl;return l?.limit&&(i=i.append("limit",l?.limit)),yield(0,n.s)(a.http.get(a.url,{params:i}))}catch(i){const r={message:a.commonSrv.getError("",i).message,color:"danger"};return yield a.commonSrv.showToast(r),i}})()}getProduct(l){var a=this;return(0,e.A)(function*(){try{return yield(0,n.s)(a.http.get(`${a.url}/${l}`))}catch(i){const r={message:a.commonSrv.getError("",i).message,color:"danger"};return yield a.commonSrv.showToast(r),i}})()}static{this.\u0275fac=function(a){return new(a||M)(p.KVO(d.Qq),p.KVO(h.h),p.KVO(o.K),p.KVO(P.K_))}}static{this.\u0275prov=p.jDH({token:M,factory:M.\u0275fac,providedIn:"root"})}}return M})()},31909:(v,O,t)=>{t.d(O,{B:()=>M});var e=t(73308),d=t(99987),n=t(2978),s=t(77897),p=t(13217),h=t(74657),o=t(82571),P=t(56610);function m(g,l){if(1&g&&(n.j41(0,"span"),n.EFF(1),n.k0s()),2&g){const a=n.XpG();n.R7$(1),n.SpI(" : ",a.sponsorName,"")}}let M=(()=>{class g{constructor(a,i,c,r){this.modalController=a,this.fidelityProgramService=i,this.translateService=c,this.commonSrv=r}ngOnInit(){}closeModal(a){this.modalController.dismiss(a)}acceptSponsor(){var a=this;return(0,e.A)(function*(){(yield a.fidelityProgramService.acceptInvitation(a.invitation._id,a.commonSrv?.user?._id).catch(c=>(console.error("Error accepting sponsor:",c),a.commonSrv.showToast({message:a.translateService.currentLang===d.T.French?"Une erreur est survenue.":"An error occurred.",color:"danger"}),null)))&&(a.commonSrv.showToast({message:a.translateService.currentLang===d.T.French?"Parrainage accept\xe9 avec succ\xe8s!":"Sponsorship successfully accepted!",color:"success"}),a.closeModal({accepted:!0}))})()}static{this.\u0275fac=function(i){return new(i||g)(n.rXU(s.W3),n.rXU(p._),n.rXU(h.c$),n.rXU(o.h))}}static{this.\u0275cmp=n.VBU({type:g,selectors:[["app-modal"]],inputs:{invitation:"invitation",sponsorName:"sponsorName"},decls:13,vars:10,consts:[[1,"sponsorship-container"],[1,"icon-container"],["src","/assets/images/referral.png","alt","Sponsor"],[1,"sponsorship-title"],[1,"sponsorship-text"],[4,"ngIf"],["expand","block",1,"accept-button",3,"click"]],template:function(i,c){1&i&&(n.j41(0,"div",0)(1,"div",1),n.nrm(2,"img",2),n.k0s(),n.j41(3,"h2",3),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"p",4),n.EFF(7),n.nI1(8,"translate"),n.DNE(9,m,2,1,"span",5),n.k0s(),n.j41(10,"ion-button",6),n.bIt("click",function(){return c.acceptSponsor()}),n.EFF(11),n.nI1(12,"translate"),n.k0s()()),2&i&&(n.R7$(4),n.JRh(n.bMT(5,4,"fidelity-page.sponsor-title")),n.R7$(3),n.SpI("",n.bMT(8,6,"fidelity-page.sponsor-desc")," "),n.R7$(2),n.Y8G("ngIf",c.sponsorName),n.R7$(2),n.SpI(" ",n.bMT(12,8,"button.accept")," "))},dependencies:[s.Jm,P.bT,h.D9],styles:['@charset "UTF-8";.sponsorship-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem;text-align:center;height:100%;background-color:#fff;border-radius:10px;box-shadow:0 2px 10px #0000001a}.icon-container[_ngcontent-%COMP%]{width:80px;height:80px;background-color:#e6f0ff;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-bottom:1rem}.sponsorship-icon[_ngcontent-%COMP%]{font-size:40px;color:var(--clr-secondary-400)}.sponsorship-title[_ngcontent-%COMP%]{color:#333;font-size:24px;margin:0;margin-bottom:.5rem}.sponsorship-text[_ngcontent-%COMP%]{color:#666;font-size:16px;margin:0;margin-bottom:2rem}.sponsorship-text[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.accept-button[_ngcontent-%COMP%]{--background: var(--clr-secondary-400);--border-radius: 8px;--padding-top: 1rem;--padding-bottom: 1rem;--padding-start: 2rem;--padding-end: 2rem;font-weight:500;text-transform:uppercase;width:100%;max-width:300px}ion-content[_ngcontent-%COMP%]{--background: transparent}@media (max-width: 320px){.sponsorship-container[_ngcontent-%COMP%]{padding:1rem}.icon-container[_ngcontent-%COMP%]{width:60px;height:60px}.sponsorship-icon[_ngcontent-%COMP%]{font-size:30px}.sponsorship-title[_ngcontent-%COMP%]{font-size:20px}.sponsorship-text[_ngcontent-%COMP%]{font-size:14px}}']})}}return g})()},80153:(v,O,t)=>{t.d(O,{s:()=>d});var d=function(n){return n[n.PENDING=200]="PENDING",n[n.VALIDATED=300]="VALIDATED",n[n.EXPIRED=400]="EXPIRED",n}(d||{})},76060:(v,O,t)=>{t.d(O,{I:()=>e,a:()=>d});var e=function(n){return n[n.CREATE=100]="CREATE",n[n.READ=200]="READ",n[n.DELETE=300]="DELETE",n}(e||{}),d=function(n){return n[n.FEEDBACK=100]="FEEDBACK",n[n.ORDER=200]="ORDER",n[n.GENERAL=300]="GENERAL",n}(d||{})},13217:(v,O,t)=>{t.d(O,{_:()=>l});var e=t(73308),d=t(26409),n=t(45312),s=t(94934),p=t(79801),h=t(80153),o=t(99987),P=t(2978),m=t(33607),M=t(82571),g=t(62049);let l=(()=>{class a{constructor(c,r,_,u){this.baseUrl=c,this.http=r,this.commonSrv=_,this.translateSrv=u,this.ratioOfPointsByVolume=.2,this.base_url=`${this.baseUrl.getOrigin()}${n.c.basePath}`}calculateTotalPointsOrder(c,r){return c.reduce((u,C)=>u+C?.quantity*C.packaging?.unit?.value,0)*(r?.points?.status||p.Th.AMIGO)*this.ratioOfPointsByVolume}getPoints(c){var r=this;return(0,e.A)(function*(){let _=new d.Nl;const{companyId:u}=c;return u&&(_=_.append("companyId",u)),yield(0,s.s)(r.http.get(`${r.base_url}loyalty-program/points`,{params:_}))})()}getBenefitRewards(c){var r=this;return(0,e.A)(function*(){let _=new d.Nl;return c?.monthly&&(_=_.append("monthly",JSON.stringify(c.monthly))),c?.statusValue&&(_=_.append("statusValue",JSON.stringify(c?.statusValue))),c?.annual&&(_=_.append("annual",JSON.stringify(c.annual))),c?.punctual&&(_=_.append("punctual",JSON.stringify(c.punctual))),yield(0,s.s)(r.http.get(`${r.base_url}advantages`,{params:_}))})()}acceptInvitation(c,r){var _=this;return(0,e.A)(function*(){return yield(0,s.s)(_.http.patch(`${_.base_url}invitations/${c}/accept`,{referredUserId:r}))})()}getInvitations(c){var r=this;return(0,e.A)(function*(){let _=new d.Nl;const{limit:u,status:C,prospectTel:f}=c;return u&&(_=_.append("limit",u)),f&&(_=_.append("prospectTel",f)),C&&(_=_.append("status",C)),yield(0,s.s)(r.http.get(`${r.base_url}invitations`,{params:_}))})()}sendReferralInvitation(c,r){var _=this;return(0,e.A)(function*(){try{const u={referrerId:c,prospectTel:r};return yield(0,s.s)(_.http.post(`${_.base_url}invitations/${c}/invite-referral`,u))}catch(u){const C=_.translateSrv.currentLang===o.T.French?"Impossible d'envoyer l'invitation de parrainage":"Failed to send referral invitation";return yield _.commonSrv.showToast({message:u?.error?.message??_.commonSrv.getError(C,u).message,color:"danger"}),u}})()}getPendingInvitationForUser(c){var r=this;return(0,e.A)(function*(){let _=new d.Nl;return _=_.set("prospectTel",+c),_=_.append("status",h.s.PENDING),yield(0,s.s)(r.http.get(`${r.base_url}invitations/pending-invitation`,{params:_}))})()}getDisplayedPoints(c,r){return"pending"===r?c?.unValidated||0:c?.totalPoints||0}getDisplayedPointsLabel(c){return"pending"===c?"fidelity-page.waiting":"fidelity-page.total-points"}static{this.\u0275fac=function(r){return new(r||a)(P.KVO(m.K),P.KVO(d.Qq),P.KVO(M.h),P.KVO(g.E))}}static{this.\u0275prov=P.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})()},96514:(v,O,t)=>{t.d(O,{I:()=>m});var e=t(73308),d=t(26409),n=t(94934),s=t(35025),p=t.n(s),h=t(45312),o=t(2978),P=t(33607);let m=(()=>{class M{constructor(l,a){this.http=l,this.baseUrlService=a,this.url=this.baseUrlService.getOrigin()+h.c.basePath}getMessages(l){var a=this;return(0,e.A)(function*(){try{let i=new d.Nl;const{email:c,date:r,notifications:_,userId:u,enable:C=!0}=l;return r?.startDate&&r?.endDate&&(i=i.append("startDate",p()(r?.startDate).format("YYYY-MM-DD")),i=i.append("endDate",p()(r?.endDate).format("YYYY-MM-DD"))),c&&(i=i.append("feedback.user.email",c)),u&&(i=i.append("userId",u)),_&&(i=i.append("_id",_)),i=i.append("enable",C),yield(0,n.s)(a.http.get(`${a.url}notifications`,{params:i}))}catch(i){return i}})()}makeRead(l){var a=this;return(0,e.A)(function*(){try{return yield(0,n.s)(a.http.patch(`${a.url}notifications/${l}`,{}))}catch(i){return i}})()}deleteNotifications(l){var a=this;return(0,e.A)(function*(){return(0,n.s)(a.http.patch(`${a.url}notifications/${l[0]}/delete`,{ids:l}))})()}static{this.\u0275fac=function(a){return new(a||M)(o.KVO(d.Qq),o.KVO(P.K))}}static{this.\u0275prov=o.jDH({token:M,factory:M.\u0275fac,providedIn:"root"})}}return M})()},68896:(v,O,t)=>{t.d(O,{I:()=>g});var e=t(73308),d=t(2978),n=t(40129),s=t(82571),p=t(45312),h=t(26409),o=t(33607),P=t(14599),m=t(94934),M=t(56610);let g=(()=>{class l{constructor(){this.commonSrv=(0,d.WQX)(s.h),this.http=(0,d.WQX)(h.Qq),this.baseUrl=(0,d.WQX)(o.K),this.storageSrv=(0,d.WQX)(P.n),this.base_url=`${this.baseUrl.getOrigin()}${p.c.basePath}`}validateScanData(i){var c=this;return(0,e.A)(function*(){try{return yield(0,m.s)(c.http.post(`${c.base_url}scanner-data`,i))}catch(r){const u={message:c.commonSrv.getError("",r).message,color:"danger"};return yield c.commonSrv.showToast(u),r}})()}checkPermission(){return(0,e.A)(function*(){try{const{camera:i}=yield n.vi.requestPermissions();return"granted"===i}catch(i){return console.log(i),!1}})()}stopScan(){var i=this;return(0,e.A)(function*(){i.currDisplay=!1,document.querySelector("body").classList.remove("scanner-active")})()}showContent(){document.querySelectorAll(".hide-on-scan").forEach(c=>{c.style.display=""}),document.querySelector("body").classList.remove("scanner-active")}prepareScanner(){return(0,e.A)(function*(){document.body.classList.add("scanner-active")})()}startScan(){var i=this;return(0,e.A)(function*(){try{if(!(yield i.checkPermission()))return void(yield i.commonSrv.showToast({color:"danger",message:"Permission refus\xe9e pour utiliser la cam\xe9ra"}));yield i.prepareScanner(),yield n.vi.installGoogleBarcodeScannerModule(),console.log("\u2705 Module MLKit install\xe9 avec succ\xe8s");const{barcodes:r}=yield n.vi.scan();if(i.restoreUI(),r&&r.length>0)return console.log("\u{1f3af} Scan r\xe9ussi:",r[0]),r[0].rawValue||r[0].displayValue;yield i.commonSrv.showToast({color:"warning",message:"Aucun code-barres d\xe9tect\xe9"})}catch(c){console.error("\u274c Erreur MLKit scan:",c),yield i.commonSrv.showToast({color:"danger",message:"Erreur lors du scan"})}finally{i.stopScan()}})()}restoreUI(){document.body.classList.remove("scanner-active")}getVolumeOrderByParticularClient(i){var c=this;return(0,e.A)(function*(){let r=new h.Nl;const{status:_=300,offset:u,limit:C,enable:f=!0,associatedCommercialId:b,startDate:D,endDate:A,customerName:y}=i;void 0!==u&&(r=r.append("offset",u)),C&&(r=r.append("limit",C)),_&&(r=r.append("status",_)),b&&(r=r.append("user.associatedCommercial._id",b)),r=r.append("enable",f),D&&A&&(r=r.append("startDate",new M.vh("fr").transform(D,"YYYY-MM-dd")),r=r.append("endDate",new M.vh("fr").transform(A,"YYYY-MM-dd"))),y&&(r=r.append("user.firstName",y));try{return yield(0,m.s)(c.http.get(`${c.base_url}scanner-data/volume-order-by-particular-client`,{params:r}))}catch(E){const T={message:c.commonSrv.getError("",E).message,color:"danger"};return yield c.commonSrv.showToast(T),E}})()}static{this.\u0275fac=function(c){return new(c||l)}}static{this.\u0275prov=d.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})()},97130:(v,O,t)=>{t.d(O,{I:()=>P});var e=t(73308),d=t(45312),n=t(2978),s=t(77897),p=t(49957),h=t(82571),o=t(14599);let P=(()=>{class m{constructor(g,l,a,i){this.platform=g,this.appVersion=l,this.commonService=a,this.storageService=i}isUpToDate(){var g=this;return(0,e.A)(function*(){let l;try{l=yield g.getAppVersion()}catch(a){"cordova_not_available"===a&&(l=g.platform.is("android")?d.c?.appVersionAndroid:d.c?.appVersionIos)}finally{return g.isLatestVersion(l)}})()}isLatestVersion(g){var l=this;return(0,e.A)(function*(){let a;console.log("appVersion:",g);try{a=yield l.getMinimalVersion()}catch{a=l.platform.is("android")?d.c?.appVersionAndroid:d.c?.appVersionIos}finally{return console.log(a,g),l.isNewerVersion(a,g)}})()}getMinimalVersion(){var g=this;return(0,e.A)(function*(){return g.currentPlatform=g.platform.is("android")?"android":"ios",yield g.commonService.getMinimalAppVersion(g.currentPlatform)})()}getAppVersion(){var g=this;return(0,e.A)(function*(){return yield g.appVersion.getVersionNumber()})()}isNewerVersion(g,l){const a=g?.split("."),i=l?.split("."),c=Math?.max(a?.length,i?.length);for(let r=0;r<c;r++){let _=parseInt(a[r]||"0",10),u=parseInt(i[r]||"0",10);if(u>_)return!0;if(u<_)return!1}return!0}static{this.\u0275fac=function(l){return new(l||m)(n.KVO(s.OD),n.KVO(p.U),n.KVO(h.h),n.KVO(o.n))}}static{this.\u0275prov=n.jDH({token:m,factory:m.\u0275fac,providedIn:"root"})}}return m})()}}]);