"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1142],{92391:(A,O,r)=>{r.d(O,{Nn:()=>D,gH:()=>i});var i=function(s){return s.CalendarEvent="CALENDAR_EVENT",s.ContactInfo="CONTACT_INFO",s.DriversLicense="DRIVERS_LICENSE",s.Email="EMAIL",s.Geo="GEO",s.Isbn="ISBN",s.Phone="PHONE",s.Product="PRODUCT",s.Sms="SMS",s.Text="TEXT",s.Url="URL",s.Wifi="WIFI",s.Unknown="UNKNOWN",s}(i||{}),D=function(s){return s.Front="FRONT",s.Back="BACK",s}(D||{})},94761:(A,O,r)=>{r.d(O,{vi:()=>c});var a=r(22126);r(92391);const c=(0,a.F3)("BarcodeScanner",{web:()=>r.e(5499).then(r.bind(r,85499)).then(D=>new D.BarcodeScannerWeb)})},39316:(A,O,r)=>{r.d(O,{b:()=>v});var a=r(73308),i=r(26409),c=r(94934),D=r(45312),E=r(2978),h=r(82571),u=r(33607),P=r(77897);let v=(()=>{class s{constructor(t,o,n,_){this.http=t,this.commonSrv=o,this.baseUrlService=n,this.toastController=_,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+D.c.basePath+"products"}getProducts(t){var o=this;return(0,a.A)(function*(){try{let n=new i.Nl;return t?.limit&&(n=n.append("limit",t?.limit)),yield(0,c.s)(o.http.get(o.url,{params:n}))}catch(n){const e={message:o.commonSrv.getError("",n).message,color:"danger"};return yield o.commonSrv.showToast(e),n}})()}getProduct(t){var o=this;return(0,a.A)(function*(){try{return yield(0,c.s)(o.http.get(`${o.url}/${t}`))}catch(n){const e={message:o.commonSrv.getError("",n).message,color:"danger"};return yield o.commonSrv.showToast(e),n}})()}static{this.\u0275fac=function(o){return new(o||s)(E.KVO(i.Qq),E.KVO(h.h),E.KVO(u.K),E.KVO(P.K_))}}static{this.\u0275prov=E.jDH({token:s,factory:s.\u0275fac,providedIn:"root"})}}return s})()},44444:(A,O,r)=>{r.d(O,{PB:()=>v,cs:()=>c,iL:()=>m}),r(68953);class i{constructor(o){this.email="",this.firstName="",this.lastName="",this.tel="",this.password="",this.cni="",this.nui="",this.address={region:"",city:"",district:""},this.category=o}}class c extends i{}var v=function(t){return t[t.NORMAL=100]="NORMAL",t[t.CORDO_RH=101]="CORDO_RH",t[t.DRH=102]="DRH",t}(v||{}),m=function(t){return t[t.BHB=101]="BHB",t[t.BS=102]="BS",t[t.BPI=103]="BPI",t}(m||{})},17709:(A,O,r)=>{r.d(O,{Q:()=>s});var a=r(73308),i=r(26409),c=r(2978),D=r(45312),E=r(94934),h=r(74657),u=r(99987),P=r(82571),v=r(33607);let s=(()=>{class m{constructor(o,n,_){this.http=o,this.commonSrv=n,this.baseUrlService=_,this.translateService=(0,c.WQX)(h.c$),this.url=this.baseUrlService.getOrigin()+D.c.basePath}getQrCodeDataAnUpDateStateToScanned(o){var n=this;return(0,a.A)(function*(){try{let _=new i.Nl;const{code:e,particularUser:d}=o;if(!e)throw yield n.commonSrv.showToast({color:"danger",message:u.T.French===n.translateService.currentLang?"Code absent du QR code":"Code is missing from the QR code"}),new Error("Code is required");return e&&(_=_.append("code",e)),d&&(_=_.append("particularUser",d)),yield(0,E.s)(n.http.get(`${n.url}qr-code/search-update/${e}`,{params:_}))}catch(_){return yield n.commonSrv.showToast({color:"danger",message:""+_?.error?.message}),_}})()}static{this.\u0275fac=function(n){return new(n||m)(c.KVO(i.Qq),c.KVO(P.h),c.KVO(v.K))}}static{this.\u0275prov=c.jDH({token:m,factory:m.\u0275fac,providedIn:"root"})}}return m})()},68896:(A,O,r)=>{r.d(O,{I:()=>m});var a=r(73308),i=r(2978),c=r(94761),D=r(82571),E=r(45312),h=r(26409),u=r(33607),P=r(14599),v=r(94934),s=r(56610);let m=(()=>{class t{constructor(){this.commonSrv=(0,i.WQX)(D.h),this.http=(0,i.WQX)(h.Qq),this.baseUrl=(0,i.WQX)(u.K),this.storageSrv=(0,i.WQX)(P.n),this.base_url=`${this.baseUrl.getOrigin()}${E.c.basePath}`}validateScanData(n){var _=this;return(0,a.A)(function*(){try{return yield(0,v.s)(_.http.post(`${_.base_url}scanner-data`,n))}catch(e){const d={message:_.commonSrv.getError("",e).message,color:"danger"};return yield _.commonSrv.showToast(d),e}})()}checkPermission(){return(0,a.A)(function*(){try{const{camera:n}=yield c.vi.requestPermissions();return"granted"===n}catch(n){return console.log(n),!1}})()}stopScan(){var n=this;return(0,a.A)(function*(){n.currDisplay=!1,document.querySelector("body").classList.remove("scanner-active")})()}showContent(){document.querySelectorAll(".hide-on-scan").forEach(_=>{_.style.display=""}),document.querySelector("body").classList.remove("scanner-active")}prepareScanner(){return(0,a.A)(function*(){document.body.classList.add("scanner-active")})()}startScan(){var n=this;return(0,a.A)(function*(){try{if(!(yield n.checkPermission()))return void n.commonSrv.showToast({color:"danger",message:"Permission refus\xe9e pour utiliser la cam\xe9ra"});yield n.prepareScanner();const{barcodes:e}=yield c.vi.scan();if(n.restoreUI(),e&&e.length>0)return e[0].displayValue;n.commonSrv.showToast({color:"warning",message:"Aucun code-barres d\xe9tect\xe9"})}catch(_){console.error("Erreur lors du scan",_),n.commonSrv.showToast({color:"danger",message:"Erreur lors du scan"})}finally{n.stopScan()}})()}restoreUI(){document.body.classList.remove("scanner-active")}getVolumeOrderByParticularClient(n){var _=this;return(0,a.A)(function*(){let e=new h.Nl;const{status:M=300,offset:d,limit:N,enable:g=!0,associatedCommercialId:L,startDate:U,endDate:l,customerName:f}=n;void 0!==d&&(e=e.append("offset",d)),N&&(e=e.append("limit",N)),M&&(e=e.append("status",M)),L&&(e=e.append("user.associatedCommercial._id",L)),e=e.append("enable",g),U&&l&&(e=e.append("startDate",new s.vh("fr").transform(U,"YYYY-MM-dd")),e=e.append("endDate",new s.vh("fr").transform(l,"YYYY-MM-dd"))),f&&(e=e.append("user.firstName",f));try{return yield(0,v.s)(_.http.get(`${_.base_url}scanner-data/volume-order-by-particular-client`,{params:e}))}catch(C){const K={message:_.commonSrv.getError("",C).message,color:"danger"};return yield _.commonSrv.showToast(K),C}})()}static{this.\u0275fac=function(_){return new(_||t)}}static{this.\u0275prov=i.jDH({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})()}}]);