import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { InfiniteScrollCustomEvent, IonPopover, ModalController } from '@ionic/angular';
import { Particular, BaseUser, FilterData } from 'src/app/shared/models/user.models';
import { CommonService } from 'src/app/shared/services/common.service';
import { UserService } from 'src/app/shared/services/user.service';
import { Location } from '@angular/common';
import { NavigationEnd, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseModalComponent } from 'src/app/shared/components/base-modal/base-modal.component';
import { Language } from 'src/app/shared/enum/language.enum';
import { FilterUserComponent } from './filter-user/filter-user.component';
import { UserCategory } from 'src/app/shared/enum/user-category.enum';
import { WholeSaleService } from 'src/app/shared/services/whole-sale.service';
import { WholeSale } from 'src/app/shared/models/whole-sale';


@Component({
  selector: 'app-indirect-user',
  templateUrl: './indirect-user.page.html',
  styleUrls: ['./indirect-user.page.scss'],
})
export class IndirectUserPage implements OnInit {

  @ViewChild('popover', { static: false }) popover: IonPopover | undefined;
  location = inject(Location);
  translateService = inject(TranslateService);
  modalCtrl = inject(ModalController)

  router = inject(Router)
  isOpen = false;
  users: BaseUser[] = [];
  wholeSale: WholeSale[] = [];
  currentUser: any;
  skeletons = [1, 2, 3, 4, 5, 6];
  isLoading: boolean;
  filteredUsersNames: any;
  filterData: FilterData = {
    category: 0,
    limit: 50,
    offset: 0,

  }

  filteredUsers: any[] = [];
  searchQuery: string = '';
  isSearchVisible: boolean = false;

  isMenuOpen: { [key: string]: boolean } = {};
  userCategory = UserCategory;
  totalUsers: number = 0;


  constructor(
    private userSrv: UserService,
    protected commonSrv: CommonService,
    public wholeService: WholeSaleService
  ) { }

  toggleSearch() {
    this.isSearchVisible = !this.isSearchVisible;
  }

  async ngOnInit() {
    this.isLoading = true;
    await this.getUsers();
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd && event.url === '/navigation/indirect-user') {
        this.getUsers();
      }
    });
  }

  reset() {
    this.filterData.commercialRegion = null
    this.ngOnInit()
  }
  presentPopover(event: Event, user: Particular) {
    if (!this.popover) {
      console.error('Popover is not defined');
      return;
    }
    this.popover.event = event;
    this.isOpen = true;
    this.currentUser = user;
  }


  async editProspect() {
    if (this.popover) {
      await this.popover.dismiss();
      this.isOpen = false;
    }
    if (this.wholeService.getWholeSaleBoolean) {
      this.wholeService.wholeSaleDetail = this.currentUser;
    } else {
      this.userSrv.currentUserParticular = this.currentUser;
    }

    const route = this.wholeService.getWholeSaleBoolean
      ? '/navigation/manage-user/create-whole-sale'
      : '/navigation/manage-user/create-indirect-user';

    this.router.navigate([route]);
  }

  async onAddUser() {
    this.userSrv.currentUserParticular = null; // Réinitialiser l'utilisateur en cours
    this.router.navigate(['/navigation/manage-user/create-indirect-user']);
  }

  async OnAddWholeSale() {
    this.router.navigate(['/navigation/manage-user/create-whole-sale']);
  }

  async getUserCommercial() {
    this.filterData.commercialRegion = this.commonSrv.user?.address.commercialRegion;
    this.ngOnInit();

  }

  async localisation() {
    if (this.popover) {
      await this.popover.dismiss();
      this.isOpen = false;
    }
    this.userSrv.currentUserParticular = this.currentUser;
    this.router.navigate(['/navigation/indirect-user/location-view']);

  }

  async localisations() {
    this.isLoading = true;

    // Récupération de la région commerciale
    const commercialRegion = this.commonSrv.user?.address.commercialRegion;
    const filterWithCommercialId = {
      ...this.filterData,
      commercialRegion: commercialRegion,
    };

    try {
      const response = await this.userSrv.getUsers(filterWithCommercialId);
      const users = response.data || [];

      const localisations = users
        .filter(user => user.localisation && user?.localisation?.latitude && user?.localisation?.longitude) // Filtrer les utilisateurs avec localisation valide
        .map(user => ({
          latitude: user?.localisation?.latitude,
          longitude: user?.localisation?.longitude,
        }));


      this.userSrv.currentUserLocalisation = localisations;
      this.router.navigate(['/navigation/indirect-user/location-view']);

    } catch (error) {
      await this.commonSrv.showToast({
        color: 'danger',
        message: 'Erreur lors de la récupération des utilisateurs' + error?.error?.message,
      });
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  async preventAlert() {
    this.isOpen = false;
  }

  back() {
    [UserCategory.DonutAnimator, UserCategory.Particular].includes(this.commonSrv.user?.category) ? this.router.navigate(['/navigation/home-alt']) : this.router.navigate(['/navigation/home']);
    // this.location.back()
  }

  async openModalDeletingUser() {
    try {
      if (this.popover) {
        await this.popover.dismiss();
        this.isOpen = false;
      }
      const modal = await this.modalCtrl.create({
        component: BaseModalComponent,
        cssClass: 'modalClass',
        componentProps: {
          dataModal: {
            confirmButton: this.translateService.currentLang === Language.French ? 'Supprimer' : 'Delete',
            cancelButton: this.translateService.currentLang === Language.French ? 'Annuler' : 'Cancel',
            text: this.translateService.currentLang === Language.French
              ? `Vous êtes sur le point de supprimer le compte ${this.currentUser?.firstName} dans l'application Clic Cadyst.\nConfirmez-vous cette action ?`
              : `You are about to delete the account ${this.currentUser?.firstName} from the Clic Cadyst application.\nDo you confirm this action?`,
            cssClass: 'custom-loading',
            handler: async () => await this.deleteCurrentUser(this.currentUser),
          },
        },
      });

      await modal.present();
      const { role } = await modal.onDidDismiss();
      console.log('Modal dismissed with role:', role);
    } catch (error) {
      console.error('Error opening delete user modal:', error);
    } finally {
      this.isOpen = false;
    }
  }

  async deleteCurrentUser(user: Particular) {
    try {
      user.enable = false;
      await this.userSrv.updateUserParticular(user);
      await this.getUsers();
    } catch (er) {

    }
  }

  async showFilter(): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: FilterUserComponent,
      initialBreakpoint: 0.7,
      cssClass: 'modal',
      breakpoints: [0, 0.5, 0.7, 1],
      mode: 'ios',
      componentProps: {
        filterData: this.filterData,
        filteredUsersNames: this.filteredUsersNames,
      },
    });
    modal.present();
    this.filterData = (await modal.onWillDismiss()).data;
    console.log('filerData:', this.filterData);

    if (this.filterData) {
      this.users = [];
      this.filterData.offset = 0;
      await this.getUsers();

    }
  }

  async getUsers() {
    try {
      this.skeletons = [1, 2, 3, 4, 5, 6, 7, 8];
      if (!this.wholeService.getWholeSaleBoolean) {
        const currentUserId = this.commonSrv.user?._id;
        const filterWithCommercialId = {
          ...this.filterData,
          associatedCommercialId: currentUserId,
        };
        const response = await this.userSrv.getUsers(filterWithCommercialId);
        this.users = response?.data || [];
        this.filteredUsers = [...this.users];
        this.totalUsers = response?.count || 0;
      }
      else {
        const filterDataId = {
          ...this.filterData,
          animateDonutId: this.commonSrv?.user?._id
        } // Associer l'ID du commercial actuel
        this.filterData.commercialRegion = this.commonSrv.user.address?.commercialRegion;
        const response = await this.wholeService.getWholeSale(filterDataId) ;
        const res = await this.wholeService.getWholeSale(this.filterData);
        this.wholeSale = response?.count !== 0 ? response?.data : res?.data;
        this.filteredUsers = [...this.wholeSale];
        this.totalUsers = response?.count || 0
      }
      this.isLoading = false;
      this.skeletons = [];
    } catch (error) {
      console.error('Error fetching users:', error);
      this.users = [];
      this.filteredUsers = [];
      this.isLoading = false;
      this.skeletons = [];
    }
  }

  filterUsers() {
    if (!this.searchQuery) {
      this.wholeService.getWholeSaleBoolean ? this.filteredUsers = [...this.wholeSale] : this.filteredUsers = [...this.users];   // Réinitialiser les résultats si la recherche est vide
      return;
    }
    let users = [];
    this.wholeService.getWholeSaleBoolean ? users = this.wholeSale : users = this.users; // Utiliser la liste appropriée en fonction du type d'utilisateur
    const query = this.searchQuery.toLowerCase().split(''); // Diviser la recherche en lettres
    this.filteredUsers = users.filter(user => {
      const firstName = user.firstName.toLowerCase();
      const tel = String(user.tel);

      // Vérifier si toutes les lettres de la requête sont présentes
      return query.every(letter =>
        firstName.includes(letter) || tel.includes(letter)
      );
    });
  }

  async doRefresh(event: any) {
    this.filterData = {
      category: 0,
      limit: 50,
      offset: 0,
    };
    this.isLoading = true;
    await this.getUsers();
    if (event?.target?.complete instanceof Function) {
      event.target.complete();
    }
  }

  async getFlowUsers(event: any): Promise<void> {
    const offset = this.filterData['offset'] + this.filterData['limit'] + 1;
    this.filterData['offset'] = (offset < this.totalUsers) && !this.totalUsers ? offset : this.totalUsers;

    await this.getUsers();
    (event as InfiniteScrollCustomEvent).target.complete();
  }

}
