"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1382],{71382:(x,C,c)=>{c.r(C),c.d(C,{HistoricMarketPlacePageModule:()=>w});var s=c(56610),p=c(37222),i=c(77897),u=c(74657),M=c(77575),l=c(73308),m=c(58133),O=c(88233),b=c(79898),f=c(99987),n=c(2978),h=c(28863),v=c(82571),k=c(62049),_=c(2611);function y(r,g){1&r&&n.nrm(0,"ion-progress-bar",17)}function I(r,g){if(1&r&&(n.j41(0,"ion-label"),n.EFF(1),n.nI1(2,"date"),n.k0s()),2&r){const t=n.XpG().$implicit;n.R7$(1),n.SpI(" Date: ",n.i5U(2,1,null==t?null:t.created_at,"dd/MM/YYYY \xe0 HH:mm"),"")}}function T(r,g){if(1&r&&(n.j41(0,"ion-card",18)(1,"ion-card-content")(2,"div",19)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-label"),n.EFF(7),n.nI1(8,"translate"),n.nI1(9,"number"),n.k0s(),n.DNE(10,I,3,4,"ion-label",20),n.j41(11,"ion-label"),n.EFF(12,"Statut: "),n.j41(13,"strong",21),n.nI1(14,"colorStatusOrder"),n.EFF(15),n.nI1(16,"statusOrder"),n.k0s()()(),n.j41(17,"div",22),n.nrm(18,"ion-icon",23),n.k0s()()()),2&r){const t=g.$implicit,o=n.XpG();n.Mz_("routerLink","/navigation/market-place/order-detail/",t._id,""),n.R7$(4),n.Lme("",n.bMT(5,8,"history-page.reference"),": ",(null==t?null:t.appReference)||(null==t?null:t.appReference),""),n.R7$(3),n.Lme("",n.bMT(8,10,"history-page.amount"),": ",n.bMT(9,12,null==t||null==t.cart||null==t.cart.amount?null:t.cart.amount.TTC)," POINTS"),n.R7$(3),n.Y8G("ngIf",(null==o.commonService||null==o.commonService.user?null:o.commonService.user.category)!==o.userCategory.Commercial),n.R7$(3),n.Y8G("ngClass",n.bMT(14,14,t.status)),n.R7$(2),n.JRh(n.bMT(16,16,null==t?null:t.status))}}function R(r,g){1&r&&(n.j41(0,"ion-cart")(1,"ion-thumbnail",24),n.nrm(2,"ion-skeleton-text",25),n.k0s()()),2&r&&(n.R7$(2),n.Y8G("animated",!0))}function F(r,g){1&r&&(n.j41(0,"div",26),n.nrm(1,"ion-img",27),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()),2&r&&(n.R7$(3),n.SpI(" ",n.bMT(4,1,"history-page.empty-order")," "))}const P=function(r){return{active:r}},E=[{path:"",component:(()=>{class r{constructor(t,o,e,a,d){this.location=t,this.marketService=o,this.commonService=e,this.modalCtrl=a,this.translateService=d,this.isLoading=!1,this.skeletons=[1,2,3,4,5,6],this.offset=0,this.limit=20,this.category={status:m.s.Particular},this.orderStatus={status:O.Re.CREATED},this.userCategory=m.s,this.status=O.Re,this.orders=[]}back(){this.location.back()}ionViewWillEnter(){var t=this;return(0,l.A)(function*(){t.isLoading=!0,t.orders=[],yield t.initRefresh()})()}ionViewDidEnter(){this.isLoading=!1}doRefresh(t){var o=this;return(0,l.A)(function*(){o.offset=0,o.orders=[],yield o.getOrderItemByUser(),t.target.complete()})()}initRefresh(){var t=this;return(0,l.A)(function*(){t.offset=0,t.orders=[],yield t.getOrderItemByUser()})()}getFlowOrder(t){var o=this;return(0,l.A)(function*(){o.offset+=o.limit,yield o.getOrderItemByUser(),t.target.complete()})()}showFilter(){var t=this;return(0,l.A)(function*(){const o=yield t.modalCtrl.create({component:b.W,initialBreakpoint:.6,cssClass:"modal",breakpoints:[0,.6,.7],mode:"ios",componentProps:{filterData:t.filterData}});o.present();const{data:e}=yield o.onWillDismiss();console.log(e),e&&Object.keys(e).length>0&&(t.filterData=e,t.offset=0,t.orders=[],yield t.getOrderItemByUser(),0===t.orders.length&&(yield t.commonService.showToast({message:t.translateService.currentLang===f.T.French?"Aucune commande trouv\xe9e pour cette p\xe9riode":"No orders found for this period",color:"warning"})))})()}getOrderItemByUser(){var t=this;return(0,l.A)(function*(){try{t.skeletons=[1,2,3,4,5,6],t.isLoading=!0;const o={...t.filterData,limit:t.limit,offset:t.offset,status:t.orderStatus.status};if(!t.commonService?.user?._id)return void t.handleUserOrderError("User ID is missing.");const e=yield t.marketService.getAllOrderItemsByUser(o);if(!e?.data?.length)return;t.orders=0===t.offset?e.data:[...t.orders,...e.data]}catch{t.handleUserOrderError("Failed to fetch orders.")}finally{t.isLoading=!1,t.skeletons=[]}})()}handleUserOrderError(t){this.isLoading=!1,this.skeletons=[],this.commonService.showToast({message:t,color:"danger"})}static{this.\u0275fac=function(o){return new(o||r)(n.rXU(s.aZ),n.rXU(h.j),n.rXU(v.h),n.rXU(i.W3),n.rXU(k.E))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-historic-market-place"]],decls:34,vars:32,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"title"],["slot","end","src","/assets/icons/funnel-outline.svg",3,"click"],[3,"fullscreen"],["type","indeterminate",4,"ngIf"],["id","container"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],["slot","top",1,"tab-container","ion-no-border"],[3,"ngClass","click"],[1,"orderlist"],[1,"order-list"],["class","order",3,"routerLink",4,"ngFor","ngForOf"],[4,"ngFor","ngForOf"],[3,"ionInfinite"],["class","empty-list",4,"ngIf"],["type","indeterminate"],[1,"order",3,"routerLink"],[1,"detail"],[4,"ngIf"],[3,"ngClass"],[1,"icon"],["src","/assets/icons/arrow-forward-green.svg"],[1,"skeleton"],[3,"animated"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"]],template:function(o,e){1&o&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return e.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-img",3),n.bIt("click",function(){return e.showFilter()}),n.k0s()()(),n.j41(7,"ion-content",4),n.DNE(8,y,1,0,"ion-progress-bar",5),n.j41(9,"div",6)(10,"ion-refresher",7),n.bIt("ionRefresh",function(d){return e.doRefresh(d)}),n.nrm(11,"ion-refresher-content",8),n.nI1(12,"translate"),n.nI1(13,"translate"),n.k0s(),n.j41(14,"ion-tab-bar",9)(15,"ion-tab-button",10),n.bIt("click",function(){return e.orderStatus.status=e.status.CREATED,e.orders=[],e.getOrderItemByUser()}),n.j41(16,"ion-title"),n.EFF(17),n.nI1(18,"translate"),n.k0s()(),n.j41(19,"ion-tab-button",10),n.bIt("click",function(){return e.orderStatus.status=e.status.VALIDATED,e.orders=[],e.getOrderItemByUser()}),n.j41(20,"ion-title"),n.EFF(21),n.nI1(22,"translate"),n.k0s()(),n.j41(23,"ion-tab-button",10),n.bIt("click",function(){return e.orderStatus.status=e.status.REJECTED,e.orders=[],e.getOrderItemByUser()}),n.j41(24,"ion-title"),n.EFF(25),n.nI1(26,"translate"),n.k0s()()(),n.j41(27,"div",11)(28,"div",12),n.DNE(29,T,19,18,"ion-card",13),n.DNE(30,R,3,1,"ion-cart",14),n.j41(31,"ion-infinite-scroll",15),n.bIt("ionInfinite",function(d){return e.getFlowOrder(d)}),n.nrm(32,"ion-infinite-scroll-content"),n.k0s()()(),n.DNE(33,F,5,3,"div",16),n.k0s()()),2&o&&(n.R7$(4),n.SpI(" ",n.bMT(5,14,"history-page.purchase")," "),n.R7$(3),n.Y8G("fullscreen",!0),n.R7$(1),n.Y8G("ngIf",e.isLoading),n.R7$(3),n.FS9("pullingText",n.bMT(12,16,"refresher.pull")),n.Mz_("refreshingText","",n.bMT(13,18,"refresher.refreshing"),"..."),n.R7$(4),n.Y8G("ngClass",n.eq3(26,P,e.orderStatus.status===e.status.CREATED)),n.R7$(2),n.SpI(" ",n.bMT(18,20,"history-page.tabs.in-progres")," "),n.R7$(2),n.Y8G("ngClass",n.eq3(28,P,e.orderStatus.status===e.status.VALIDATED)),n.R7$(2),n.SpI(" ",n.bMT(22,22,"history-page.tabs.validate")," "),n.R7$(2),n.Y8G("ngClass",n.eq3(30,P,e.orderStatus.status===e.status.REJECTED)),n.R7$(2),n.SpI(" ",n.bMT(26,24,"history-page.tabs.Rejected")," "),n.R7$(4),n.Y8G("ngForOf",e.orders),n.R7$(1),n.Y8G("ngForOf",e.skeletons),n.R7$(3),n.Y8G("ngIf",(null==e.orders?null:e.orders.length)<=0&&!e.isLoading))},dependencies:[s.YU,s.Sq,s.bT,i.b_,i.I9,i.W9,i.eU,i.iq,i.KW,i.Ax,i.Hp,i.he,i.FH,i.To,i.Ki,i.ds,i.Jq,i.qW,i.Zx,i.BC,i.ai,i.N7,M.Wk,s.QX,s.vh,u.D9,_.D3,_.qZ],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent;--background-color: transparent;background:#cfcfcf;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Regular;text-align:start;color:var(--clr-primary-900)}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(41 * var(--res));padding-top:0}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]{margin:auto;min-height:35px;gap:1em;margin-bottom:calc(50 * var(--res));border:none}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-tab-button[_ngcontent-%COMP%]{min-width:-moz-fit-content;min-width:fit-content}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:calc(41 * var(--res));color:#6d839d;font-family:Mont SemiBold}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#6d839d;border-bottom:3px solid #419CFB}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#0b305c}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]{border-bottom:1px solid #8597ad;box-shadow:none;border-radius:unset;margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block;line-height:initial;margin-top:calc(15.7 * var(--res));font-family:Mont SemiBold;color:#0b305c;font-size:calc(36 * var(--res))}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-primary-400[_ngcontent-%COMP%]{color:var(--ion-color-tertiary-contrast)!important}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-success-200[_ngcontent-%COMP%]{background-color:var(--ion-color-success-tint)!important;padding:3px 6px;border-radius:4px}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-info-500[_ngcontent-%COMP%]{color:var(--ion-color-tertiary-tint)!important}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-info-100[_ngcontent-%COMP%]{background-color:#cef!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-default-400[_ngcontent-%COMP%]{color:var(--ion-color-tertiary-contras)!important}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-danger-400[_ngcontent-%COMP%]{color:#fff}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-info-500[_ngcontent-%COMP%]{background-color:var(--ion-color-tertiary-tint)!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-tertiary-200[_ngcontent-%COMP%]{background-color:var(--ion-color-tertiary-tint)!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-danger-100[_ngcontent-%COMP%]{background-color:#c30404!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:1.5rem;height:1.5rem}ion-content[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]{height:4em;width:100%;margin-bottom:1rem}ion-content[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:100%;height:100%}ion-content[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{display:flex;align-items:center;flex-direction:column;justify-content:center;height:100%}ion-content[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}"]})}}return r})()}];let S=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[M.iI.forChild(E),M.iI]})}}return r})();var j=c(93887);let w=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[s.MD,p.YN,i.bv,u.h,j.G,S]})}}return r})()}}]);