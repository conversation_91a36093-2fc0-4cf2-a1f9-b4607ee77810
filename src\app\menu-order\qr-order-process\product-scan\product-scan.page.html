<app-progress-spinner *ngIf="isLoading"></app-progress-spinner>

<ion-header *ngIf="!scannerSrv?.currDisplay">
  <ion-toolbar class="header">
    <ion-img (click)="back()" slot="start" src="/assets/icons/arrow-blue.svg"></ion-img>
    <ion-title class="title"> {{ user ? 'Scanner les produits '  : ('qr-orders.title' | translate) }} </ion-title>
    <ion-img *ngIf="manualOrderEnabled && !user" (click)="openManuelOrder()" slot="end"
    src="/assets/icons/touch.svg"></ion-img>
  </ion-toolbar>
</ion-header>

<section id="container" class="scroller-container" *ngIf="!scannerSrv?.currDisplay">
  <div class="products" *ngIf="productSrv?.currentDataProductScan?.length">
    <div class="item" *ngFor="let item of productSrv?.currentDataProductScan; trackBy: trackByFn">
      <app-product-cart-qr-orders class="elt" [item]="item" [isEdit]="item?.quantity > 0"></app-product-cart-qr-orders>

    </div>
  </div>

  <div class="scan-container">

    <div class="scan-content" *ngIf="!productSrv?.currentDataProductScan?.length">
      <div  class="user-info-container" *ngIf="user">
        <p> {{('qr-orders.text' | translate)}} </p>
        <div class="user-info-item">
          <label class="user-info-label">{{ 'user-info.full-name' | translate }}</label>
          <div class="user-info-value"> {{ user?.firstName }}</div>
        </div>
        <div class="user-info-item">
          <label class="user-info-label">Type de client</label>
          <div class="user-info-value">{{ getCategory(user?.categoryType) }}</div>
        </div>
        <div class="user-info-item">
          <label class="user-info-label">{{ 'user-info.phone' | translate }}</label>
          <div class="user-info-value">{{ user?.tel }}</div>
        </div>
        <div class="user-info-item">
          <label class="user-info-label">{{ 'user-info.region' | translate }}</label>
          <div class="user-info-value">{{ user?.address.region }}</div>
        </div>
        <div class="user-info-item">
          <label class="user-info-label">{{ 'indirect-clients.ville' | translate }}</label>
          <div class="user-info-value">{{ user?.address.city }}</div>
        </div>
           <div class="user-info-item">
          <label class="user-info-label">Quartier</label>
          <div class="user-info-value">{{ user?.address?.neighborhood }}</div>
        </div> 
      </div>

      <div class="qr-placeholder">
        <img src="assets/images/qr-scanner.png">
      </div>
      <p class="scan-text">
        {{'qr-orders.scan-text' | translate}}
      </p>

    </div>

    <div class="bottom-buttons">
      <ion-button expand="block" color="secondary" class="btn--meduim btn--upper bg-secondary-400" (click)="openScan()">
        <ion-icon name="scan-outline" slot="start"></ion-icon>
        <ion-label> {{'qr-orders.text-button' | translate}} </ion-label>
      </ion-button>

      <ion-button class="btn--meduim btn--upper" color="primary" (click)="nextStep()"
        [disabled]="!productSrv.currentDataProductScan?.length" expand="block"
        *ngIf="productSrv.currentDataProductScan?.length">
        <ion-label> {{"order-new-page.second-step.next-button-label" | translate}} </ion-label>
      </ion-button>
    </div>
  </div>

  <!-- <ion-fab  *ngIf="currentDataProductScan?.length" class="ion-fab"  (click)="openScan()" vertical="bottom" horizontal="end" slot="fixed" >
    <ion-fab-button  (click)="openScan()"
      class="LiveChatBtn animate__animated animate__bounceInUp">
      <ion-img src="../../../../assets/icons/qr-icon.svg" class="img-coupons"></ion-img>
    </ion-fab-button>
    
  </ion-fab> -->
</section>



<app-qr-code-scanner *ngIf="scannerSrv?.currDisplay"></app-qr-code-scanner>