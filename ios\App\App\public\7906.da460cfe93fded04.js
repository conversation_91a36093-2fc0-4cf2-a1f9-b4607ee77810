"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7906],{46943:(x,h,c)=>{c.d(h,{J:()=>l});const l={FRONT:"front",BACK:"back"}},40129:(x,h,c)=>{c.d(h,{vi:()=>n});const n=(0,c(22126).F3)("BarcodeScanner",{web:()=>c.e(5499).then(c.bind(c,85499)).then(a=>new a.BarcodeScannerWeb)})},47906:(x,h,c)=>{c.r(h),c.d(h,{ProductScanPageModule:()=>pn});var p=c(56610),O=c(37222),l=c(77897),f=c(77575),d=c(73308),n=c(2978),a=c(68896),b=c(22126);c(46943);const _=(0,b.F3)("BarcodeScanner",{web:()=>c.e(2908).then(c.bind(c,12908)).then(o=>new o.BarcodeScannerWeb)});var y=c(82571),P=c(45312),C=c(26409),i=c(33607),u=c(14599),s=c(94934);let T=(()=>{class o{constructor(){this.commonSrv=(0,n.WQX)(y.h),this.http=(0,n.WQX)(C.Qq),this.baseUrl=(0,n.WQX)(i.K),this.storageSrv=(0,n.WQX)(u.n),this.base_url=`${this.baseUrl.getOrigin()}${P.c.basePath}`}validateScanData(t){var r=this;return(0,d.A)(function*(){try{return yield(0,s.s)(r.http.post(`${r.base_url}scanner-data`,t))}catch(e){const m={message:r.commonSrv.getError("",e).message,color:"danger"};return yield r.commonSrv.showToast(m),e}})()}checkPermission(){return(0,d.A)(function*(){try{return!!(yield _.checkPermission({force:!0}))?.granted}catch(t){return console.log(t),!1}})()}stopScan(){var t=this;return(0,d.A)(function*(){t.currDisplay=!1,document.querySelector("body").classList.remove("scanner-active"),yield _.showBackground(),yield _.stopScan()})()}showContent(){document.querySelectorAll(".hide-on-scan").forEach(r=>{r.style.display=""}),document.querySelector("body").classList.remove("scanner-active"),_.showBackground(),_.stopScan()}prepareScanner(){return(0,d.A)(function*(){document.body.classList.add("scanner-active")})()}startScan(){var t=this;return(0,d.A)(function*(){try{if(!(yield t.checkPermission()))return void t.commonSrv.showToast({color:"danger",message:"Permission refus\xe9e pour utiliser la cam\xe9ra"});yield _.hideBackground(),yield t.prepareScanner();const e=yield _.startScan();return t.restoreUI(),e?.hasContent||t.commonSrv.showToast({color:"warning",message:`${e}`}),e.content}catch(r){console.error("Erreur lors du scan",r),t.commonSrv.showToast({color:"danger",message:"Erreur lors du scan"})}finally{t.stopScan()}})()}restoreUI(){document.body.classList.remove("scanner-active"),_.showBackground(),_.stopScan()}getVolumeOrderByParticularClient(t){var r=this;return(0,d.A)(function*(){let e=new C.Nl;const{status:g=300,offset:m,limit:N,enable:F=!0,associatedCommercialId:k,startDate:K,endDate:X,customerName:Q}=t;void 0!==m&&(e=e.append("offset",m)),N&&(e=e.append("limit",N)),g&&(e=e.append("status",g)),k&&(e=e.append("user.associatedCommercial._id",k)),e=e.append("enable",F),K&&X&&(e=e.append("startDate",new p.vh("fr").transform(K,"YYYY-MM-dd")),e=e.append("endDate",new p.vh("fr").transform(X,"YYYY-MM-dd"))),Q&&(e=e.append("user.firstName",Q));try{return yield(0,s.s)(r.http.get(`${r.base_url}scanner-data/volume-order-by-particular-client`,{params:e}))}catch(U){const mn={message:r.commonSrv.getError("",U).message,color:"danger"};return yield r.commonSrv.showToast(mn),U}})()}static{this.\u0275fac=function(r){return new(r||o)}}static{this.\u0275prov=n.jDH({token:o,factory:o.\u0275fac,providedIn:"root"})}}return o})(),S=(()=>{class o{constructor(t,r,e){this.http=t,this.baseUrlService=r,this.commonSrv=e,this.url=`${this.baseUrlService.getOrigin()}${P.c.basePath}manual-order`}getManualOrderSetting(){var t=this;return(0,d.A)(function*(){try{return!0===(yield(0,s.s)(t.http.get(t.url)))}catch(r){const g={message:t.commonSrv.getError("",r).message,color:"danger"};return yield t.commonSrv.showToast(g),r}})()}static{this.\u0275fac=function(r){return new(r||o)(n.KVO(C.Qq),n.KVO(i.K),n.KVO(y.h))}}static{this.\u0275prov=n.jDH({token:o,factory:o.\u0275fac,providedIn:"root"})}}return o})();var M=c(99987),I=function(o){return o[o.ACTIVE=100]="ACTIVE",o[o.SCANNED=200]="SCANNED",o[o.USED=300]="USED",o[o.INACTIVE=99]="INACTIVE",o}(I||{}),R=c(23985),E=c(44444),w=c(40129),D=c(58133),$=c(17709),L=c(39316),Y=c(62049),B=c(68953),G=c(11244);const J=function(o){return{"card-content":o}};let V=(()=>{class o{constructor(t){this.commonSrv=t,this.modalIsOpen=new n.bkB,this.CompanyCategory=B.kJ,this.userCategory=D.s}ngOnInit(){}static{this.\u0275fac=function(r){return new(r||o)(n.rXU(y.h))}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-product-cart-qr-orders"]],inputs:{item:"item",isEdit:"isEdit",isCatalogue:"isCatalogue"},outputs:{modalIsOpen:"modalIsOpen"},decls:15,vars:11,consts:[[1,"product-card-container"],[1,"containerize",3,"ngClass"],[1,"product-container"],[1,"image-container"],[3,"src"],[1,"price-container"],[1,"packaging"],[1,"quantity-container"]],template:function(r,e){1&r&&(n.j41(0,"div",0)(1,"ion-card")(2,"div",1)(3,"div",2)(4,"div",3),n.nrm(5,"ion-img",4),n.k0s(),n.j41(6,"div",5)(7,"ion-label"),n.EFF(8),n.nI1(9,"capitalize"),n.k0s(),n.j41(10,"span",6),n.EFF(11),n.nI1(12,"capitalize"),n.k0s()(),n.j41(13,"div",7),n.EFF(14),n.k0s()()()()()),2&r&&(n.R7$(2),n.Y8G("ngClass",n.eq3(9,J,e.isEdit&&(null==e.commonSrv||null==e.commonSrv.user?null:e.commonSrv.user.category)!==e.userCategory.Particular)),n.R7$(3),n.Y8G("src",null==e.item||null==e.item.product?null:e.item.product.image),n.R7$(3),n.SpI(" ",n.bMT(9,5,null==e.item||null==e.item.product?null:e.item.product.label)," "),n.R7$(3),n.SpI(" ",n.bMT(12,7,null==e.item||null==e.item.packaging?null:e.item.packaging.label)," "),n.R7$(3),n.JRh(e.item.quantity))},dependencies:[l.b_,l.KW,l.he,p.YU,G.F],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.product-card-container[_ngcontent-%COMP%]{height:calc(550 * var(--res));width:calc(420 * var(--res));border-radius:1rem}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:100%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]{height:100%;background-color:#ebf5ff}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;flex-direction:column;position:relative;height:100%;gap:.5rem}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{display:flex;width:50%;height:50%;justify-content:center;align-items:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:100%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]{height:20px;width:25%;display:flex;justify-content:center;align-self:flex-start}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{height:20px;width:20px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]{display:flex;width:100%;color:#0b305c;padding-left:calc(25 * var(--res));margin-bottom:10px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-size:calc(35 * var(--res))}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]   ion-label.text-content[_ngcontent-%COMP%]{color:#fff}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-particular[_ngcontent-%COMP%]{width:100%;display:flex;color:#0b305c;text-align:center;margin-bottom:10px;flex-direction:column}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-particular[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(35 * var(--res))}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-particular[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:10px;color:#6d839d}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]{text-align:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:14px;font-weight:500;font-family:var(--mont-bold);color:#0b305c}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#6d839d;font-weight:300;margin-bottom:1rem}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]{width:70%;color:#fff;border-radius:10px 0 0;background-color:#419cfb;display:flex;justify-content:center;align-items:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(37 * var(--res));font-family:Mont SemiBold;margin-bottom:10%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-particular[_ngcontent-%COMP%]{width:100%;color:#fff;background-color:#143c5d;display:flex;justify-content:center;align-items:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-particular[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(37 * var(--res));font-family:Mont SemiBold;margin-bottom:7%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .edit-product[_ngcontent-%COMP%]{background-color:#419cfb!important}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .empty-product[_ngcontent-%COMP%]   .packaging[_ngcontent-%COMP%]{width:100%;justify-content:center;background-color:#419cfb}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .empty-product[_ngcontent-%COMP%]   .packaging[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#fff}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .empty-product[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]{width:0%;visibility:hidden}.quantity-container[_ngcontent-%COMP%]{position:absolute;top:0;right:0;background-color:var(--clr-secondary-400);color:var(--clr-tertiary-100);font-family:var(--mont-bold);padding:.5em;border-bottom-left-radius:.5rem}@media only screen and (max-width: 400px){.product-card-container[_ngcontent-%COMP%]{height:calc(450 * var(--res));width:calc(390 * var(--res))}}"]})}}return o})();var H=c(45381),Z=c(71333),z=c(74657);function q(o,v){1&o&&n.nrm(0,"app-progress-spinner")}function nn(o,v){if(1&o){const t=n.RV6();n.j41(0,"ion-img",6),n.bIt("click",function(){n.eBV(t);const e=n.XpG(2);return n.Njj(e.openManuelOrder())}),n.k0s()}}function tn(o,v){if(1&o){const t=n.RV6();n.j41(0,"ion-header")(1,"ion-toolbar",2)(2,"ion-img",3),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.back())}),n.k0s(),n.j41(3,"ion-title",4),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.DNE(6,nn,1,0,"ion-img",5),n.k0s()()}if(2&o){const t=n.XpG();n.R7$(4),n.SpI(" ",t.user?"Scanner les produits ":n.bMT(5,2,"qr-orders.title")," "),n.R7$(2),n.Y8G("ngIf",t.manualOrderEnabled&&!t.user)}}function en(o,v){if(1&o&&(n.j41(0,"div",17),n.nrm(1,"app-product-cart-qr-orders",18),n.k0s()),2&o){const t=v.$implicit;n.R7$(1),n.Y8G("item",t)("isEdit",(null==t?null:t.quantity)>0)}}function rn(o,v){if(1&o&&(n.j41(0,"div",15),n.DNE(1,en,2,2,"div",16),n.k0s()),2&o){const t=n.XpG(2);n.R7$(1),n.Y8G("ngForOf",null==t.productSrv?null:t.productSrv.currentDataProductScan)("ngForTrackBy",t.trackByFn)}}function on(o,v){if(1&o&&(n.j41(0,"div",24)(1,"p"),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"div",25)(5,"label",26),n.EFF(6),n.nI1(7,"translate"),n.k0s(),n.j41(8,"div",27),n.EFF(9),n.k0s()(),n.j41(10,"div",25)(11,"label",26),n.EFF(12,"Type de client"),n.k0s(),n.j41(13,"div",27),n.EFF(14),n.k0s()(),n.j41(15,"div",25)(16,"label",26),n.EFF(17),n.nI1(18,"translate"),n.k0s(),n.j41(19,"div",27),n.EFF(20),n.k0s()(),n.j41(21,"div",25)(22,"label",26),n.EFF(23),n.nI1(24,"translate"),n.k0s(),n.j41(25,"div",27),n.EFF(26),n.k0s()(),n.j41(27,"div",25)(28,"label",26),n.EFF(29),n.nI1(30,"translate"),n.k0s(),n.j41(31,"div",27),n.EFF(32),n.k0s()(),n.j41(33,"div",25)(34,"label",26),n.EFF(35,"Quartier"),n.k0s(),n.j41(36,"div",27),n.EFF(37),n.k0s()()()),2&o){const t=n.XpG(3);n.R7$(2),n.SpI(" ",n.bMT(3,11,"qr-orders.text")," "),n.R7$(4),n.JRh(n.bMT(7,13,"user-info.full-name")),n.R7$(3),n.SpI(" ",null==t.user?null:t.user.firstName,""),n.R7$(5),n.JRh(t.getCategory(null==t.user?null:t.user.categoryType)),n.R7$(3),n.JRh(n.bMT(18,15,"user-info.phone")),n.R7$(3),n.JRh(null==t.user?null:t.user.tel),n.R7$(3),n.JRh(n.bMT(24,17,"user-info.region")),n.R7$(3),n.JRh(null==t.user?null:t.user.address.region),n.R7$(3),n.JRh(n.bMT(30,19,"indirect-clients.ville")),n.R7$(3),n.JRh(null==t.user?null:t.user.address.city),n.R7$(5),n.JRh(null==t.user||null==t.user.address?null:t.user.address.neighborhood)}}function cn(o,v){if(1&o&&(n.j41(0,"div",19),n.DNE(1,on,38,21,"div",20),n.j41(2,"div",21),n.nrm(3,"img",22),n.k0s(),n.j41(4,"p",23),n.EFF(5),n.nI1(6,"translate"),n.k0s()()),2&o){const t=n.XpG(2);n.R7$(1),n.Y8G("ngIf",t.user),n.R7$(4),n.SpI(" ",n.bMT(6,2,"qr-orders.scan-text")," ")}}function an(o,v){if(1&o){const t=n.RV6();n.j41(0,"ion-button",28),n.bIt("click",function(){n.eBV(t);const e=n.XpG(2);return n.Njj(e.nextStep())}),n.j41(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()}if(2&o){const t=n.XpG(2);n.Y8G("disabled",!(null!=t.productSrv.currentDataProductScan&&t.productSrv.currentDataProductScan.length)),n.R7$(2),n.SpI(" ",n.bMT(3,2,"order-new-page.second-step.next-button-label")," ")}}function sn(o,v){if(1&o){const t=n.RV6();n.j41(0,"section",7),n.DNE(1,rn,2,2,"div",8),n.j41(2,"div",9),n.DNE(3,cn,7,4,"div",10),n.j41(4,"div",11)(5,"ion-button",12),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openScan())}),n.nrm(6,"ion-icon",13),n.j41(7,"ion-label"),n.EFF(8),n.nI1(9,"translate"),n.k0s()(),n.DNE(10,an,4,4,"ion-button",14),n.k0s()()()}if(2&o){const t=n.XpG();n.R7$(1),n.Y8G("ngIf",null==t.productSrv||null==t.productSrv.currentDataProductScan?null:t.productSrv.currentDataProductScan.length),n.R7$(2),n.Y8G("ngIf",!(null!=t.productSrv&&null!=t.productSrv.currentDataProductScan&&t.productSrv.currentDataProductScan.length)),n.R7$(5),n.SpI(" ",n.bMT(9,4,"qr-orders.text-button")," "),n.R7$(2),n.Y8G("ngIf",null==t.productSrv.currentDataProductScan?null:t.productSrv.currentDataProductScan.length)}}function ln(o,v){1&o&&n.nrm(0,"app-qr-code-scanner")}let W=(()=>{class o{constructor(t,r,e,g,m,N,F,k){this.router=t,this.location=r,this.qrCodeSrv=e,this.commonSrv=g,this.productSrv=m,this.storageService=N,this.loadingCtrl=F,this.translateService=k,this.scannerSrv=(0,n.WQX)(a.I),this.scannerCommunitySrv=(0,n.WQX)(T),this.platform=(0,n.WQX)(l.OD),this.userSrv=(0,n.WQX)(R.D),this.user=null,this.route=(0,n.WQX)(f.nX),this.manualOrderService=(0,n.WQX)(S)}ngOnInit(){var t=this;return(0,d.A)(function*(){t.route.snapshot.params.id&&(yield t.getUser(t.route.snapshot.params.id)),yield t.fetchManualOrderSetting();const r=b.Ii.getPlatform();if(console.log("\u{1f50d} Initialisation MLKit : plateforme =",r),"android"===r)try{const e=yield w.vi.installGoogleBarcodeScannerModule();console.log("\u2705 MLKit install\xe9 avec succ\xe8s :",e)}catch(e){console.error("\u274c Erreur installation MLKit :",e)}else console.log("\u{1f6ab} MLKit non install\xe9 car plateforme != Android")})()}ionViewWillEnter(){this.commonSrv.showNav=!1}back(){this.location.back()}trackByFn(t,r){return t}showDetail(){this.router.navigate(["/item-detail"])}showScan(){var t=this;return(0,d.A)(function*(){yield t.platform.ready();const r=b.Ii.getPlatform();let e;if(console.log("\u{1f4f1} Plateforme d\xe9tect\xe9e :",r),"android"===r){console.log("\u{1f9e0} Android d\xe9tect\xe9 : tentative avec MLKit");try{t.scannerSrv.currDisplay=!0,e=yield t.scannerSrv.startScan(),console.log("\u2705 Scan MLKit r\xe9ussi :",e)}catch(g){console.warn("\u26a0\ufe0f Erreur MLKit, pas de scanner Android fonctionnel:",g),yield t.commonSrv.showToast({message:t.translateService.currentLang===M.T.French?"Erreur lors du scan. Veuillez r\xe9essayer.":"Error during scan. Please try again.",color:"danger"})}finally{t.scannerSrv.currDisplay=!1}}else if("ios"===r){console.log("\u{1f34f} iOS d\xe9tect\xe9 : utilisation du Scanner Community");try{e=yield t.scannerCommunitySrv.startScan(),console.log("\u2705 Scan community r\xe9ussi :",e)}catch(g){console.warn("\u26a0\ufe0f Erreur Scanner Community iOS:",g),yield t.commonSrv.showToast({message:t.translateService.currentLang===M.T.French?"Erreur lors du scan iOS. Veuillez r\xe9essayer.":"Scan error on iOS. Please try again.",color:"danger"})}}else console.warn(`\u{1f6d1} Plateforme non support\xe9e: ${r}`),yield t.commonSrv.showToast({message:t.translateService.currentLang===M.T.French?"Plateforme non support\xe9e":"Unsupported platform",color:"danger"});return e})()}openScan(){var t=this;return(0,d.A)(function*(){let r;try{const e=P.c?.isMockQrCode?'{"code":"a123456789"}':yield t.showScan();if(!e)return void(yield t.commonSrv.showToast({color:"danger",message:t.translateService.currentLang===M.T.French?"Aucun r\xe9sultat de scan":"No scan result"}));let g;try{g=JSON.parse(e)}catch{return void(yield t.commonSrv.showToast({color:"danger",message:t.translateService.currentLang===M.T.French?"QR code invalide":"Invalid QR code"}))}if(!g?.code)return void(yield t.commonSrv.showToast({color:"danger",message:t.translateService.currentLang===M.T.French?"Code absent du QR code":"Code is missing from the QR code"}));if(r=yield t.loadingCtrl.create({message:t.translateService.currentLang===M.T.French?`V\xe9rification du scan...\nCode: ${g?.code}`:`Checking scan...\nCode: ${g?.code}`}),yield r.present(),yield t.verifyScan(g))return;const m=yield t.qrCodeSrv.getQrCodeDataAnUpDateStateToScanned({code:g?.code,particularUser:t.user?.category===D.s.Particular?t.user?._id:null});if(console.log(`==========QR Code data: ${JSON.stringify(m)}=================`),m instanceof Error||!m?.code)return console.log(`==========QR Code not found or error: ${JSON.stringify(m)}=================`),void(yield t.commonSrv.showToast({message:t.translateService.currentLang===M.T.French?"QR Code non actif ou invalide":"QR Code is inactive or invalid",color:"danger"}));if(m?.status===I.USED)return console.log(`==========QR Code already used: ${JSON.stringify(m)}=================`),void(yield t.commonSrv.showToast({message:t.translateService.currentLang===M.T.French?"QR Code d\xe9j\xe0 utilis\xe9":"QR Code is already used",color:"danger"}));yield t.addProduct(m,g)}catch(e){return console.error("Error in openScan:",e),yield t.commonSrv.showToast({message:t.translateService.currentLang===M.T.French?"Une erreur est survenue lors du scan":"An error occurred during scan",color:"danger"}),e}finally{r&&(yield r.dismiss())}})()}verifyScan(t){var r=this;return(0,d.A)(function*(){return-1!==r.productSrv.dataQrCode.findIndex(g=>g.code===t?.code)&&(console.log(`==========QR Code already scanned: ${t?.code}=================`),yield r.commonSrv.showToast({message:r.translateService.currentLang===M.T.French?"QR Code d\xe9j\xe0 scann\xe9":"QR Code is already scanned",color:"danger"}),!0)})()}addProduct(t,r){var e=this;return(0,d.A)(function*(){const g=e.productSrv.currentDataProductScan.findIndex(m=>m?.product?._id===t?.product?._id&&m?.packaging?._id===t?.packaging?._id);-1!==g?e.productSrv.currentDataProductScan[g].quantity=(e.productSrv.currentDataProductScan[g].quantity||1)+1:e.productSrv.currentDataProductScan.unshift({...t,code:r?.code,quantity:1}),e.productSrv.dataQrCode.push(t)})()}nextStep(){const t=this.productSrv.currentDataProductScan.filter(r=>r.quantity&&r.quantity>0);this.storageService.store("items",JSON.stringify(t)),this.storageService.store("qrCodeData",JSON.stringify(this.productSrv.dataQrCode)),this.router.navigate(["/order/choice-suppliers"])}openManuelOrder(){this.router.navigate(["/order/particular-order/first-step"])}fetchManualOrderSetting(){var t=this;return(0,d.A)(function*(){try{const r=yield t.manualOrderService.getManualOrderSetting();t.manualOrderEnabled=r}catch(r){console.error("Erreur lors de la r\xe9cup\xe9ration de la configuration :",r)}})()}getUser(t){var r=this;return(0,d.A)(function*(){r.user=yield r.userSrv.find(t),r.qrCodeSrv.currenUser=r.user})()}getCategory(t){switch(t){case E.iL.BHB:return"BHB";case E.iL.BS:return"BS";case E.iL.BPI:return"BPI";default:return"Unknown Category"}}static{this.\u0275fac=function(r){return new(r||o)(n.rXU(f.Ix),n.rXU(p.aZ),n.rXU($.Q),n.rXU(y.h),n.rXU(L.b),n.rXU(u.n),n.rXU(l.Xi),n.rXU(Y.E))}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-product-scan"]],decls:4,vars:4,consts:[[4,"ngIf"],["id","container","class","scroller-container",4,"ngIf"],[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"title"],["slot","end","src","/assets/icons/touch.svg",3,"click",4,"ngIf"],["slot","end","src","/assets/icons/touch.svg",3,"click"],["id","container",1,"scroller-container"],["class","products",4,"ngIf"],[1,"scan-container"],["class","scan-content",4,"ngIf"],[1,"bottom-buttons"],["expand","block","color","secondary",1,"btn--meduim","btn--upper","bg-secondary-400",3,"click"],["name","scan-outline","slot","start"],["class","btn--meduim btn--upper","color","primary","expand","block",3,"disabled","click",4,"ngIf"],[1,"products"],["class","item",4,"ngFor","ngForOf","ngForTrackBy"],[1,"item"],[1,"elt",3,"item","isEdit"],[1,"scan-content"],["class","user-info-container",4,"ngIf"],[1,"qr-placeholder"],["src","assets/images/qr-scanner.png"],[1,"scan-text"],[1,"user-info-container"],[1,"user-info-item"],[1,"user-info-label"],[1,"user-info-value"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled","click"]],template:function(r,e){1&r&&(n.DNE(0,q,1,0,"app-progress-spinner",0),n.DNE(1,tn,7,4,"ion-header",0),n.DNE(2,sn,11,6,"section",1),n.DNE(3,ln,1,0,"app-qr-code-scanner",0)),2&r&&(n.Y8G("ngIf",e.isLoading),n.R7$(1),n.Y8G("ngIf",!(null!=e.scannerSrv&&e.scannerSrv.currDisplay)),n.R7$(1),n.Y8G("ngIf",!(null!=e.scannerSrv&&e.scannerSrv.currDisplay)),n.R7$(1),n.Y8G("ngIf",null==e.scannerSrv?null:e.scannerSrv.currDisplay))},dependencies:[p.Sq,p.bT,l.Jm,l.eU,l.iq,l.KW,l.he,l.BC,l.ai,V,H.k,Z._,z.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding-inline:calc(41 * var(--res));--border-color: transparent;--background: transparent;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(46 * var(--res));text-align:start;margin-bottom:0!important;color:var(--clr-primary-700);font-family:var(--mont-regular)}.ion-fab[_ngcontent-%COMP%]{bottom:14%!important}ion-content[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]{--background: var(--clr-secondary-0);width:calc(16 * var(--resW));height:calc(16 * var(--resW))}ion-content[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{rotate:90deg}ion-content[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]{margin-bottom:calc(20 * var(--resW));gap:.75rem}#container[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;padding-top:0;height:100%;background-color:transparent;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));overflow-x:hidden;overflow-y:auto}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));height:max-content;flex-direction:row;justify-content:flex-start;overflow:hidden;display:flex;flex-wrap:wrap;gap:30px;justify-content:center;width:100%}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .scan-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:48px 24px;flex:1}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .scan-content[_ngcontent-%COMP%]   .qr-placeholder[_ngcontent-%COMP%]{width:120px;height:120px;margin-bottom:24px;opacity:.5}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .scan-content[_ngcontent-%COMP%]   .qr-placeholder[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .scan-content[_ngcontent-%COMP%]   .scan-text[_ngcontent-%COMP%]{text-align:center;color:#666;font-size:16px;line-height:1.4;max-width:280px;margin:0 auto}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .bottom-buttons[_ngcontent-%COMP%]{padding:16px;display:flex;flex-direction:column;background:#fff;position:fixed;bottom:0;gap:.5em;left:0;right:0}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .bottom-buttons[_ngcontent-%COMP%]   .next-button[_ngcontent-%COMP%]{--color: #666;--border-color: #ddd;margin:0}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]{--background: #f3f4f6}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: #fff}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;align-items:center;gap:5px;margin-bottom:20px}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{text-align:center;color:#e70a1e;font-size:14px;line-height:1.4;max-width:280px;margin:0 auto}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;border-bottom:.6px solid var(--clr-primary-700);border-radius:3px;width:83%;font-weight:400;height:2em;padding:16px}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%], #container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]   .user-info-value[_ngcontent-%COMP%]{font-weight:600;color:#0b305c;font-size:15px}.title[_ngcontent-%COMP%]{text-align:center;color:#143c5d;margin-bottom:1em}.title[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:22px;font-weight:700}.header[_ngcontent-%COMP%]{padding:calc(41 * var(--res))}.header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#143c5d;font-family:Mont Bold;font-weight:700;font-size:calc(45 * var(--res));margin-bottom:13%}.btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));position:fixed;bottom:2%;width:calc(100% - 10 * var(--res))}app-qr-code-scanner[_ngcontent-%COMP%]{height:100%!important}"]})}}return o})();const dn=[{path:"",component:W},{path:"user-scan/:id",component:W}];let gn=(()=>{class o{static{this.\u0275fac=function(r){return new(r||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[f.iI.forChild(dn),f.iI]})}}return o})();var un=c(93887);let pn=(()=>{class o{static{this.\u0275fac=function(r){return new(r||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[p.MD,O.YN,l.bv,un.G,z.h,gn]})}}return o})()},39316:(x,h,c)=>{c.d(h,{b:()=>A});var p=c(73308),O=c(26409),l=c(94934),f=c(45312),d=c(2978),n=c(82571),a=c(33607),b=c(77897);let A=(()=>{class _{constructor(P,C,i,u){this.http=P,this.commonSrv=C,this.baseUrlService=i,this.toastController=u,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+f.c.basePath+"products"}getProducts(P){var C=this;return(0,p.A)(function*(){try{let i=new O.Nl;return P?.limit&&(i=i.append("limit",P?.limit)),yield(0,l.s)(C.http.get(C.url,{params:i}))}catch(i){const s={message:C.commonSrv.getError("",i).message,color:"danger"};return yield C.commonSrv.showToast(s),i}})()}getProduct(P){var C=this;return(0,p.A)(function*(){try{return yield(0,l.s)(C.http.get(`${C.url}/${P}`))}catch(i){const s={message:C.commonSrv.getError("",i).message,color:"danger"};return yield C.commonSrv.showToast(s),i}})()}static{this.\u0275fac=function(C){return new(C||_)(d.KVO(O.Qq),d.KVO(n.h),d.KVO(a.K),d.KVO(b.K_))}}static{this.\u0275prov=d.jDH({token:_,factory:_.\u0275fac,providedIn:"root"})}}return _})()},11244:(x,h,c)=>{c.d(h,{F:()=>O});var p=c(2978);let O=(()=>{class l{transform(d){return console.log(),`${d?.slice(0,1)?.toLocaleUpperCase()+d?.slice(1)?.toLocaleLowerCase()}`}static{this.\u0275fac=function(n){return new(n||l)}}static{this.\u0275pipe=p.EJ8({name:"capitalize",type:l,pure:!0})}}return l})()},68896:(x,h,c)=>{c.d(h,{I:()=>y});var p=c(73308),O=c(2978),l=c(40129),f=c(82571),d=c(45312),n=c(26409),a=c(33607),b=c(14599),A=c(94934),_=c(56610);let y=(()=>{class P{constructor(){this.commonSrv=(0,O.WQX)(f.h),this.http=(0,O.WQX)(n.Qq),this.baseUrl=(0,O.WQX)(a.K),this.storageSrv=(0,O.WQX)(b.n),this.base_url=`${this.baseUrl.getOrigin()}${d.c.basePath}`}validateScanData(i){var u=this;return(0,p.A)(function*(){try{return yield(0,A.s)(u.http.post(`${u.base_url}scanner-data`,i))}catch(s){const S={message:u.commonSrv.getError("",s).message,color:"danger"};return yield u.commonSrv.showToast(S),s}})()}checkPermission(){return(0,p.A)(function*(){try{const{camera:i}=yield l.vi.requestPermissions();return"granted"===i}catch(i){return console.log(i),!1}})()}stopScan(){var i=this;return(0,p.A)(function*(){i.currDisplay=!1,document.querySelector("body").classList.remove("scanner-active")})()}showContent(){document.querySelectorAll(".hide-on-scan").forEach(u=>{u.style.display=""}),document.querySelector("body").classList.remove("scanner-active")}prepareScanner(){return(0,p.A)(function*(){document.body.classList.add("scanner-active")})()}startScan(){var i=this;return(0,p.A)(function*(){try{if(!(yield i.checkPermission()))return void(yield i.commonSrv.showToast({color:"danger",message:"Permission refus\xe9e pour utiliser la cam\xe9ra"}));yield i.prepareScanner(),yield l.vi.installGoogleBarcodeScannerModule(),console.log("\u2705 Module MLKit install\xe9 avec succ\xe8s");const{barcodes:s}=yield l.vi.scan();if(i.restoreUI(),s&&s.length>0)return console.log("\u{1f3af} Scan r\xe9ussi:",s[0]),s[0].rawValue||s[0].displayValue;yield i.commonSrv.showToast({color:"warning",message:"Aucun code-barres d\xe9tect\xe9"})}catch(u){console.error("\u274c Erreur MLKit scan:",u),yield i.commonSrv.showToast({color:"danger",message:"Erreur lors du scan"})}finally{i.stopScan()}})()}restoreUI(){document.body.classList.remove("scanner-active")}getVolumeOrderByParticularClient(i){var u=this;return(0,p.A)(function*(){let s=new n.Nl;const{status:T=300,offset:S,limit:M,enable:j=!0,associatedCommercialId:I,startDate:R,endDate:E,customerName:w}=i;void 0!==S&&(s=s.append("offset",S)),M&&(s=s.append("limit",M)),T&&(s=s.append("status",T)),I&&(s=s.append("user.associatedCommercial._id",I)),s=s.append("enable",j),R&&E&&(s=s.append("startDate",new _.vh("fr").transform(R,"YYYY-MM-dd")),s=s.append("endDate",new _.vh("fr").transform(E,"YYYY-MM-dd"))),w&&(s=s.append("user.firstName",w));try{return yield(0,A.s)(u.http.get(`${u.base_url}scanner-data/volume-order-by-particular-client`,{params:s}))}catch(D){const L={message:u.commonSrv.getError("",D).message,color:"danger"};return yield u.commonSrv.showToast(L),D}})()}static{this.\u0275fac=function(u){return new(u||P)}}static{this.\u0275prov=O.jDH({token:P,factory:P.\u0275fac,providedIn:"root"})}}return P})()}}]);