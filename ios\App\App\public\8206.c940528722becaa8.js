"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8206],{68206:(m,g,r)=>{r.r(g),r.d(g,{UserDetailPageModule:()=>f});var c=r(56610),a=r(37222),s=r(77897),l=r(77575),u=r(73308),n=r(2978),M=r(23985),d=r(44444),b=r(74657);const p=[{path:"",component:(()=>{class o{constructor(){this.location=(0,n.WQX)(c.aZ),this.formBuilder=(0,n.WQX)(a.ok),this.userSrv=(0,n.WQX)(M.D),this.route=(0,n.WQX)(l.nX)}ngOnInit(){var e=this;return(0,u.A)(function*(){e.route.snapshot.params.id&&(e.userId=e.route.snapshot.params.id,yield e.getUser(e.userId)),e.form=e.formBuilder.group({name:[e.user.firstName,a.k0.required],phone:[e.user.tel??6e8,a.k0.required],commercial:[e.user.socialReason??"N/A",a.k0.required],localisation:[e.user.address.district??"N/A",a.k0.required],seller:[e.user.associatedCompanies??["N/A","N/A"],a.k0.required]})})()}getCategory(e){switch(e){case d.iL.BHB:return"BHB";case d.iL.BS:return"BS";case d.iL.BPI:return"BPI";default:return"Unknown Category"}}getUser(e){var i=this;return(0,u.A)(function*(){i.user=yield i.userSrv.find(e)})()}back(){this.location.back()}static{this.\u0275fac=function(i){return new(i||o)}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-user-detail"]],decls:77,vars:52,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[1,"user-info-container"],[1,"user-info-item"],[1,"user-info-label"],[1,"user-info-value"]],template:function(i,t){1&i&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return t.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"translate"),n.k0s()()(),n.j41(6,"ion-content")(7,"div",3)(8,"div",4)(9,"label",5),n.EFF(10),n.nI1(11,"translate"),n.k0s(),n.j41(12,"div",6),n.EFF(13),n.k0s()(),n.j41(14,"div",4)(15,"label",5),n.EFF(16,"Type de client"),n.k0s(),n.j41(17,"div",6),n.EFF(18),n.k0s()(),n.j41(19,"div",4)(20,"label",5),n.EFF(21),n.nI1(22,"translate"),n.k0s(),n.j41(23,"div",6),n.EFF(24),n.k0s()(),n.j41(25,"div",4)(26,"label",5),n.EFF(27),n.nI1(28,"translate"),n.k0s(),n.j41(29,"div",6),n.EFF(30),n.k0s()(),n.j41(31,"div",4)(32,"label",5),n.EFF(33),n.nI1(34,"translate"),n.k0s(),n.j41(35,"div",6),n.EFF(36),n.k0s()(),n.j41(37,"div",4)(38,"label",5),n.EFF(39,"Quartier"),n.k0s(),n.j41(40,"div",6),n.EFF(41),n.k0s()(),n.j41(42,"div",4)(43,"label",5),n.EFF(44),n.nI1(45,"translate"),n.k0s(),n.j41(46,"div",6),n.EFF(47),n.k0s()(),n.j41(48,"div",4)(49,"label",5),n.EFF(50),n.nI1(51,"translate"),n.k0s(),n.j41(52,"div",6),n.EFF(53),n.nI1(54,"translate"),n.nI1(55,"translate"),n.k0s()(),n.j41(56,"div",4)(57,"label",5),n.EFF(58),n.nI1(59,"translate"),n.k0s(),n.j41(60,"div",6),n.EFF(61),n.nI1(62,"date"),n.k0s()(),n.j41(63,"div",4)(64,"label",5),n.EFF(65),n.nI1(66,"translate"),n.k0s(),n.j41(67,"div",6),n.EFF(68),n.nI1(69,"translate"),n.nI1(70,"translate"),n.k0s()(),n.j41(71,"div",4)(72,"label",5),n.EFF(73),n.nI1(74,"translate"),n.k0s(),n.j41(75,"div",6),n.EFF(76),n.k0s()()()()),2&i&&(n.R7$(4),n.SpI(" ",n.bMT(5,21,"user-info.title")," "),n.R7$(6),n.JRh(n.bMT(11,23,"user-info.full-name")),n.R7$(3),n.SpI(" ",null==t.user?null:t.user.firstName,""),n.R7$(5),n.JRh(t.getCategory(null==t.user?null:t.user.categoryType)),n.R7$(3),n.JRh(n.bMT(22,25,"user-info.phone")),n.R7$(3),n.JRh(null==t.user?null:t.user.tel),n.R7$(3),n.JRh(n.bMT(28,27,"user-info.region")),n.R7$(3),n.JRh(null==t.user?null:t.user.address.region),n.R7$(3),n.JRh(n.bMT(34,29,"indirect-clients.ville")),n.R7$(3),n.JRh(null==t.user?null:t.user.address.city),n.R7$(5),n.JRh(null==t.user||null==t.user.address?null:t.user.address.neighborhood),n.R7$(3),n.JRh(n.bMT(45,31,"user-info.location")),n.R7$(3),n.JRh(null==t.user?null:t.user.address.district),n.R7$(3),n.JRh(n.bMT(51,33,"user-info.category")),n.R7$(3),n.JRh(0===(null==t.user?null:t.user.category)?n.bMT(54,35,"user-info.particular"):n.bMT(55,37,"user-info.other")),n.R7$(5),n.JRh(n.bMT(59,39,"user-info.created-at")),n.R7$(3),n.JRh(n.i5U(62,41,null==t.user?null:t.user.created_at,"dd/MM/yyyy HH:mm")),n.R7$(4),n.JRh(n.bMT(66,44,"user-info.enabled")),n.R7$(3),n.JRh(null!=t.user&&t.user.enable?n.bMT(69,46,"user-info.yes"):n.bMT(70,48,"user-info.no")),n.R7$(5),n.JRh(n.bMT(74,50,"user-info.company")),n.R7$(3),n.SpI(" ",(null==t.user?null:t.user.socialReason)||"N/A",""))},dependencies:[s.W9,s.eU,s.KW,s.BC,s.ai,c.vh,b.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:calc(41 * var(--res));--border-color: transparent;--background: #f1f2f4;background:#fff;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;color:#0b305c}ion-content[_ngcontent-%COMP%]{--background: #f1f2f4;--overflow: auto;margin:20px;padding:10px}ion-content[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;align-items:center;gap:20px;height:auto;--overflow: auto}ion-content[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;border:1px solid rgba(65,156,251,.168627451);border-radius:9px;width:83%;font-weight:400;height:2em;padding:16px}ion-content[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:600;color:#0b305c;font-size:15px}ion-content[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]   .user-info-value[_ngcontent-%COMP%]{font-weight:600;color:#0b305c;font-size:15px}"]})}}return o})()}];let h=(()=>{class o{static{this.\u0275fac=function(i){return new(i||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[l.iI.forChild(p),l.iI]})}}return o})(),f=(()=>{class o{static{this.\u0275fac=function(i){return new(i||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[c.MD,a.YN,a.X1,s.bv,b.h,h]})}}return o})()}}]);