"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4292],{84292:(r,i,t)=>{t.r(i),t.d(i,{AppWeb:()=>o});var n=t(73308),s=t(22126);class o extends s.E_{constructor(){super(),this.handleVisibilityChange=()=>{const e={isActive:!0!==document.hidden};this.notifyListeners("appStateChange",e),document.hidden?this.notifyListeners("pause",null):this.notifyListeners("resume",null)},document.addEventListener("visibilitychange",this.handleVisibilityChange,!1)}exitApp(){throw this.unimplemented("Not implemented on web.")}getInfo(){var e=this;return(0,n.A)(function*(){throw e.unimplemented("Not implemented on web.")})()}getLaunchUrl(){return(0,n.A)(function*(){return{url:""}})()}getState(){return(0,n.A)(function*(){return{isActive:!0!==document.hidden}})()}minimizeApp(){var e=this;return(0,n.A)(function*(){throw e.unimplemented("Not implemented on web.")})()}}}}]);