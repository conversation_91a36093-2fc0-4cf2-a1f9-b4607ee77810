import { Point } from '../models/point';
import { Router } from '@angular/router';
import { commercialRegions, regions, mounthList, yearList, neighborhoods } from './../mocks/mocks';
import { Injectable, inject } from '@angular/core';
import { Address } from '../models/address';
import { Language } from '../enum/language.enum';
import { AuthUser } from '../models/user.models';
import { Platform, PopoverController, ToastController } from '@ionic/angular';
import { Company } from '../models/company.model';
import { ToastModel } from '../models/toast.model';
import { QueryResult } from '../models/query-result';
import { UserCategory } from '../enum/user-category.enum';
import { cities, hoursForEnglish, hoursForFrench } from '../mocks/mocks';
import { lastValueFrom } from 'rxjs';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpParams } from '@angular/common/http';
import { BaseUrlService } from './base-url.service';
import { CartItem } from '../models/cart.model';
import { AlertController, ModalController } from '@ionic/angular';
import { PaymentAction } from '../models/order';
import { ImageBanner } from '../models/image-banner.entity';
import moment from 'moment';
import { MarketPlaceService } from 'src/app/menu-order/services/market-place.service';
import { TransformEnumToStringPipe } from '../pipes/transform-enum-to-string.pipe';

@Injectable({
  providedIn: 'root',
})
export class CommonService {

  user: AuthUser;
  userCategory = UserCategory;
  editUser: boolean = false;
  anboardingView: boolean = true;
  toast: HTMLIonToastElement;
  base_url: string;
  url: string;
  tab: string;
  showNav: boolean = false;
  cartItems: CartItem[] = [];
  isEdit: boolean;
  productCategory: string;
  orderDetailNotification: boolean = false;

  router: Router = inject(Router);
  private http = inject(HttpClient);
  modalCtrl = inject(ModalController);
  popoverCtrl = inject(PopoverController);
  private baseUrl = inject(BaseUrlService);
  alertController = inject(AlertController);
  public toastController = inject(ToastController);
  platform = inject(Platform);

  constructor() {
    this.base_url = `${this.baseUrl.getOrigin()}${environment.basePath}mobile`;
    this.url = `${this.baseUrl.getOrigin()}${environment.basePath}`;
    this.handleBackButton();
  }

  getRegions = (): string[] => regions;

  trackByFn = (index: any, item: any) => index;

  get commercialRegions() {
    return commercialRegions;
  }

  getCities(region: string): string[] {
    return cities[region];
  }
  getNeighborhood(city: string): string[] {
    return neighborhoods[city];
  }
  getMounth(): object[] {
    return mounthList;
  }
  getyear(): string[] {
    return yearList;
  }

  getCommercialRegion(region: string): string | null {
    const data = commercialRegions
    const normalizedRegion = region.trim().toLowerCase();
    console.log('Normalized region:', normalizedRegion);

    for (const [key, regions] of Object.entries(data)) {
      console.log('Checking key:', key, 'with regions:', regions);
      if (regions.some(r => r.trim().toLowerCase() === normalizedRegion)) {
        console.log('Match found:', key);
        return key;
      }
    }

    console.log('No match found');
    return null;
  }

  getHours(quart: string, langue: string) {
    return langue === Language.French
      ? hoursForFrench[quart]
      : hoursForEnglish[quart];
  }

  getUserType(user: UserCategory): string {
    return UserCategory[user].toLocaleLowerCase();
  }

  getUserTypes(): string[] {
    return Object.keys(UserCategory).filter((value) =>
      isNaN(Number(value))
    ) as string[];
  }

  normalizeToArray(value: any): any[] {
    if (Array.isArray(value)) return [...value];
    if (value && typeof value === 'object') return [value];
    return [];
  }
  getError(title: string, error: any): QueryResult {
    if (error && error?.error && error?.error?.errors?.length) {
      for (const err of error.error.errors) {
        error.error.message = err + ';'
      }
    }
    return {
      status: error?.error?.statusCode,
      message: error?.error?.message || 'Une erreur s\'est produite',
      data: title,
    };
  }

  async showToast(toastModel: ToastModel): Promise<void> {
    await this.toast?.dismiss();
    this.toast = await this.toastController.create({
      message: toastModel?.message,
      color: toastModel?.color || 'warning',
      icon: toastModel?.icon || 'alert-circle-outline',
      duration: toastModel?.duration || 4000,
      position: 'top',
    });
    this.toast.present();
  }

  initCompany(): Company {
    return {
      address: this.initAddress(),
      name: '',
      defaultStore: '',
      afrilandKey: '',
      nui: '',
      rccm: '',
      tel: '',
      erpShipToDesc: '',
      erpSoldToId: 0,
      erpShipToId: 0,
      precompteRate: 0,
      category: 0,
      q1Enabled: false,
      annualOrderEnabled: false,
      createAt: 0,
      updateAt: 0,
    };
  }

  initPoint(): Point {
    return {
      validated: 0,
      unvalidated: 0,
      archived: 0,
    };
  }

  initAddress(): Address {
    return {
      region: '',
      city: '',
      district: '',
    };
  }

  navigateTo(url: string): void {
    this.orderDetailNotification = true;
    this.router.navigate([url]);
  }

  sortPricesByLabelProduct(prices: CartItem[]): CartItem[] {
    return prices.sort((a, b) => {
      if (a?.product?.label?.toLowerCase() < b?.product?.label.toLowerCase()) {
        return -1;
      }
      if (a?.product?.label.toLowerCase() > b?.product?.label.toLowerCase()) {
        return 1;
      }
      return 0;
    });
  }

  verifyAllFieldsForm(object: any, language: string): string | boolean {
    object as object;
    for (const key in object) {
      if (
        object[key] == null ||
        object[key] == undefined ||
        object[key] === ''
      ) {
        console.log('first level:', key, '=', object[key]);

        return language === Language.French
          ? 'Veuillez renseigner tous les champs'
          : 'Please fill in all fields';
      }

      if (typeof object[key] === 'object') {
        const newObject = object[key];
        for (const keyInObject in newObject) {
          if (!newObject[keyInObject]) {
            console.log('key in Object', keyInObject);

            return language === Language.French
              ? 'Veuillez renseigner tous les champs'
              : 'Please fill in all fields';
          }
        }
      }

      if (
        (!/\d{9}|\+\d{1} \(\d{3}\) \d{3}-\d{4}/gi.test(object[key]) ||
          object[key].length !== 9) &&
        key === 'tel'
      ) {
        return language === Language.French
          ? 'Renseignez le bon format du numéro de téléphone, ex: 6xxxxxxxx'
          : 'Enter the correct format for the phone number, e.g. 6xxxxxxxx';
      }

      if (
        !/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(object[key]) &&
        key === 'email'
      ) {
        return language === Language.French
          ? 'Veuillez entrer un email valide'
          : 'Please enter a valid email';
      }
    }

    return false;
  }

  verifyPhoneNumber(key: string | number) {
    if ((!/\d{9}|\+\d{1} \(\d{3}\) \d{3}-\d{4}/gi.test(`${key}`) ||
      `${key}`.length !== 9)
    ) return false;

    return TransformEnumToStringPipe
  }
  isEmail(phoneOrMail: any): boolean {
    return phoneOrMail.match(
      /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    );
  }

  isPhone(phoneOrEmail: any): boolean {
    const regex = new RegExp(/^6[0-9]{8}$/);
    if (phoneOrEmail == null) {
      return false;
    }
    if (regex.test(phoneOrEmail) == true) {
      return true;
    } else {
      return false;
    }
  }
  async getAllImageBanner(param?: any): Promise<{ data: ImageBanner[]; count: number }> {
    try {
      const params = this.buildQueryParams(param);
      return await lastValueFrom(
        this.http.get<{ data: ImageBanner[]; count: number }>(
          `${this.url}image-banner/banner-commercial-region`,
          { params }
        )
      );
    } catch (error) {
      console.error('Error fetching banners:', error);
      return { data: [], count: 0 };
    }
  }

  private buildQueryParams(param: any): HttpParams {
    let params = new HttpParams();
    const { level, offset, limit, commercialRegion, date, enable = true } = param;

    if (limit) params = params.append('limit', limit.toString());
    if (offset) params = params.append('offset', offset.toString());
    if (level) params = params.append('level', level.toString());
    if (commercialRegion) params = params.append('commercialRegion', commercialRegion);

    if (date?.start && date?.end) {
      params = params.append('startDate', moment(date.start).format('YYYY-MM-DD'));
      params = params.append('endDate', moment(date.end).format('YYYY-MM-DD'));
    }

    return params.append('enable', enable.toString());
  }

  async getMinimalAppVersion(platform: "android" | "ios"): Promise<string> {
    const params = new HttpParams().set('platform', `${platform}`);
    const { minimalVersion } = await lastValueFrom(this.http.get<{ minimalVersion: string }>(`${this.base_url}/minimal-version`, { params }));
    return minimalVersion;
  }

  async getElementForFilterByKeys(module: string, param: any): Promise<any> {
    try {
      let params = new HttpParams();
      const { keyForFilters, status } = param;
      if (keyForFilters) params = params.append('keyForFilters', keyForFilters);
      return await lastValueFrom(
        this.http.get<any>(`${this.url}${module}/filters-elements`, {
          params,
        })
      );

    } catch (error) {
      return error;
    }
  }

  async getOrderProcessState(): Promise<{ isDisableOrderProcess: boolean }> {
    try {
      let params = new HttpParams();

      return await lastValueFrom(this.http.get<{ isDisableOrderProcess: boolean }>(`${this.url}prices/order-process-disable-state`, { params }));
    } catch (error) {
      return error;
    }
  }

  getDisplayValue(value: number): string {
    return this.user.category === UserCategory.Particular
      ? `${value} Points`
      : `${value.toLocaleString('fr-FR')} XAF`;
  }

  handleBackButton() {
    console.log('============== CURRENT URL IS =================:', this.router.url);

    // Gérer le bouton retour pour quitter l'application ou revenir en arrière    
    this.platform.backButton.subscribeWithPriority(10, () => {
      if (['navigation/home', 'navigation/home-alt'].includes(this.router.url)) {
        navigator['app'].exitApp();
      } else {
        window.history.back();
      }
    });
  }

}

export const meansOfPayments = [
  {
    color: 'bg-primary',
    image: '/assets/images/cimencam.png',
    label: PaymentAction.MY_ACCOUNT,
    class: 'max-width',
  },
  {
    color: 'bg-white',
    image: '/assets/logos/afriland.svg',
    label: PaymentAction.AFRILAND,
    class: 'max-width',
  },
  {
    color: 'bg-blue',
    image: '/assets/logos/visa.png',
    label: PaymentAction.VISA,
    class: 'min-width',
  },
  {
    color: 'bg-white',
    image: '/assets/logos/express_union.png',
    label: PaymentAction.EU,
    class: 'min-width',
  },
  {
    color: 'bg-blue',
    image: '/assets/logos/M2u.png',
    label: PaymentAction.M2U,
    class: 'min-width',
  },
  {
    color: 'bg-orange',
    image: '/assets/logos/om-money.png',
    label: PaymentAction.ORANGE_MONEY,
    class: 'min-width',
  },
  {
    color: 'bg-yellow',
    image: '/assets/logos/mtn-money.png',
    label: PaymentAction.MOBILE_MONEY,
    class: 'min-width',
  },
  {
    color: 'bg-white',
    image: '/assets/logos/credit card-cuate.svg',
    label: PaymentAction.CREDIT,
    class: 'min-width',
  }
];
