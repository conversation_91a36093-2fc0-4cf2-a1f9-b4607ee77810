"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7572],{17572:(A,y,s)=>{s.r(y),s.d(y,{ChoiceSuppliersPageModule:()=>R});var _=s(56610),g=s(37222),m=s(77897),f=s(77575),p=s(73308),h=s(58133),e=s(2978),D=s(14599),O=s(82571),P=s(43556),M=s(93387),i=s(95908),n=s(39316),t=s(62049),a=s(28935),r=s(71333),d=s(94440),v=s(74657);function S(u,U){1&u&&e.nrm(0,"app-progress-spinner")}function E(u,U){if(1&u){const o=e.RV6();e.j41(0,"div",16),e.bIt("click",function(l){const b=e.eBV(o).$implicit;return e.XpG(2).selectSupplier(b),e.Njj(l.stopPropagation())}),e.EFF(1),e.k0s()}if(2&u){const o=U.$implicit;e.R7$(1),e.SpI(" ",o.name," ")}}function T(u,U){if(1&u&&(e.j41(0,"div",14),e.DNE(1,E,2,1,"div",15),e.k0s()),2&u){const o=e.XpG();e.R7$(1),e.Y8G("ngForOf",o.filteredSuppliers)}}function I(u,U){if(1&u){const o=e.RV6();e.j41(0,"ion-card",17),e.bIt("click",function(){const C=e.eBV(o).$implicit,b=e.XpG();return e.Njj(b.nextStep(C))}),e.j41(1,"div",6)(2,"div",7),e.nrm(3,"ion-img",8),e.k0s(),e.j41(4,"div",18)(5,"label",19),e.EFF(6),e.nI1(7,"truncateString"),e.k0s(),e.j41(8,"p"),e.EFF(9),e.nI1(10,"truncateString"),e.k0s(),e.j41(11,"ion-button",20),e.EFF(12),e.nI1(13,"translate"),e.k0s()(),e.j41(14,"div",21),e.nrm(15,"ion-icon",22),e.k0s()()()}if(2&u){const o=U.$implicit,c=e.XpG();e.R7$(6),e.JRh(e.i5U(7,3,o.name,20)),e.R7$(3),e.JRh(e.i5U(10,6,null==c.commonService||null==c.commonService.user||null==c.commonService.user.address?null:c.commonService.user.address.region,28)),e.R7$(3),e.SpI(" ",e.bMT(13,9,"order-new-page.first-step.choose")," ")}}const L=[{path:"",component:(()=>{class u{constructor(o,c,l,C,b,K,w,W,j,F){this.router=o,this.location=c,this.storageSrv=l,this.commonService=C,this.companySrv=b,this.priceSrv=K,this.packagingSrv=w,this.productSrv=W,this.translateService=j,this.wholeSaleService=F,this.isLoading=!0,this.isSelectedCompany=!1,this.filteredSuppliers=[],this.showSuggestions=!1,this.offset=0,this.limit=20,this.skeletons=[1,2,3,4,5,6],this.filterData={name:""},this.otherSupplier={id:"otherId",name:"Autre grossiste"},this.filterForm=new g.gE({name:new g.MJ("")}),this.user=this.storageSrv.getUserConnected()}ngOnInit(){var o=this;return(0,p.A)(function*(){o.isLoading=!0;try{const c=[o.getDistributorLoyalityProgram(),o.getSuppliers()];o.user.category!==h.s.Commercial&&c.push(o.getStore()),yield Promise.all(c)}catch(c){console.error("Error initializing component:",c)}finally{o.isLoading=!1}})()}getSuppliers(){var o=this;return(0,p.A)(function*(){o.skeletons=[1,2,3,4,5,6],o.isLoading=!0;const c={...o.filterData,offset:o.offset,limit:o.limit},l=o.commonService.user?.associatedSuppliers;if(l&&Array.isArray(l)&&l.length>0)o.suppliers=l;else{const C=yield o.companySrv.getParticularCompanies(c);o.suppliers=C.data,o.suppliers.unshift(o.otherSupplier),o.offset=o.offset+o.limit}o.isLoading=!1,o.skeletons=[]})()}getDistributorLoyalityProgram(){var o=this;return(0,p.A)(function*(){o.isLoading=!0;const c={...o.filterData,offset:o.offset,limit:o.limit,isLoyaltyProgDistributor:!0},l=yield o.companySrv.getCompanies(c);o.loyaltyProgramDistributor=l.data,o.filteredSuppliers=[],o.offset=o.offset+o.limit,o.isLoading=!1})()}selectCompany(o){this.selectedCompany=o,this.isSelectedCompany=!0}ionViewWillEnter(){return(0,p.A)(function*(){})()}getStore(){var o=this;return(0,p.A)(function*(){o.isLoading=!0;const c={companyId:o.companySrv.selectedCompanyForSalesOrderProcess?._id},l=yield o.priceSrv.getStores(c);l&&(o.initData=l,o.storageSrv.store("stores",JSON.stringify(l))),o.isLoading=!1})()}nextStep(o){var c=this;return(0,p.A)(function*(){const l=c.filterForm.value.name?.trim();if(l&&(!o||o.name!==l)){o={name:l,region:c.commonService.user.address.region,city:c.commonService.user.address.city,district:c.commonService.user.address.district,associatedDonutAnimator:c.commonService.user};try{c.wholeSaleService.createWholeSale(o)}catch(b){c.commonService.showToast({message:b.error?.message||"Error creating new company",color:"danger"})}}o||(o={...c.otherSupplier});const C={company:o._id,items:[],amount:null};yield c.storageSrv.store("cart",JSON.stringify(C)),yield c.storageSrv.store("supplier",JSON.stringify(o)),c.router.navigate(["order/recap-scan"]),c.productSrv.currentDataProductScan=[]})()}filterSuppliers(o){const c=o.target.value.toLowerCase();this.filterForm.patchValue({name:c}),""===c.trim()?this.showSuggestions=!1:(this.filteredSuppliers=this.loyaltyProgramDistributor.filter(l=>l.name.toLowerCase().includes(c)),this.showSuggestions=!0)}selectSupplier(o){this.selectedSupplier=o,this.filterForm.patchValue({name:o.name}),this.filteredSuppliers=[],this.showSuggestions=!1}onDocumentClick(o){this.showSuggestions=!1}back(){this.location.back()}static{this.\u0275fac=function(c){return new(c||u)(e.rXU(f.Ix),e.rXU(_.aZ),e.rXU(D.n),e.rXU(O.h),e.rXU(P.B),e.rXU(M.A),e.rXU(i.L),e.rXU(n.b),e.rXU(t.E),e.rXU(a.G))}}static{this.\u0275cmp=e.VBU({type:u,selectors:[["app-choice-suppliers"]],hostBindings:function(c,l){1&c&&e.bIt("click",function(b){return l.onDocumentClick(b)},!1,e.EBC)},decls:20,vars:14,consts:[[4,"ngIf"],[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"title"],["id","container"],[1,"custom-card",3,"formGroup"],[1,"container"],[1,"image"],["src","../../../../assets/icons/man.png"],[1,"company","relative"],["formControlName","name","type","text",3,"clearInput","placeholder","input","focus"],["class","autocomplete-list",4,"ngIf"],["expand","block",3,"click"],["class","custom-card",3,"click",4,"ngFor","ngForOf"],[1,"autocomplete-list"],["class","autocomplete-item",3,"click",4,"ngFor","ngForOf"],[1,"autocomplete-item",3,"click"],[1,"custom-card",3,"click"],[1,"company"],["for",""],["expand","block"],[1,"chevron"],["slot","end","name","chevron-forward-outline","color","primary"]],template:function(c,l){1&c&&(e.DNE(0,S,1,0,"app-progress-spinner",0),e.j41(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-img",2),e.bIt("click",function(){return l.back()}),e.k0s(),e.j41(4,"ion-title",3),e.EFF(5),e.nI1(6,"translate"),e.k0s()()(),e.j41(7,"div",4)(8,"ion-card",5)(9,"div",6)(10,"div",7),e.nrm(11,"ion-img",8),e.k0s(),e.j41(12,"div",9)(13,"ion-input",10),e.bIt("input",function(b){return l.filterSuppliers(b)})("focus",function(){return l.showSuggestions=!0}),e.nI1(14,"translate"),e.DNE(15,T,2,1,"div",11),e.k0s(),e.j41(16,"ion-button",12),e.bIt("click",function(){return l.nextStep(l.selectedSupplier||l.otherSupplier)}),e.EFF(17),e.nI1(18,"translate"),e.k0s()()()(),e.DNE(19,I,16,11,"ion-card",13),e.k0s()),2&c&&(e.Y8G("ngIf",l.isLoading),e.R7$(5),e.JRh(e.bMT(6,8,"order-new-page.first-step.choose-store")),e.R7$(3),e.Y8G("formGroup",l.filterForm),e.R7$(5),e.FS9("placeholder",e.bMT(14,10,"order-new-page.first-step.supplier")),e.Y8G("clearInput",!0),e.R7$(2),e.Y8G("ngIf",l.filteredSuppliers.length>0&&l.showSuggestions),e.R7$(2),e.SpI(" ",e.bMT(18,12,"order-new-page.first-step.choose")," "),e.R7$(2),e.Y8G("ngForOf",l.suppliers))},dependencies:[_.Sq,_.bT,g.BC,g.cb,m.Jm,m.b_,m.eU,m.iq,m.KW,m.$w,m.BC,m.ai,m.Gw,r._,g.j4,g.JD,d.c,v.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding-inline:calc(41 * var(--res));margin:1rem;--border-color: transparent;--background: transparent;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(46 * var(--res));text-align:start;margin-bottom:0!important;color:var(--clr-primary-700);font-family:var(--mont-regular)}#container[_ngcontent-%COMP%]{padding:calc(41 * var(--res));overflow:auto;height:100%}.title[_ngcontent-%COMP%]{text-align:center;color:#143c5d;font-size:22px}.custom-card[_ngcontent-%COMP%]{margin:12px 0;border:1px solid #dcdcdc;border-radius:8px}.custom-card[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:126px;padding:calc(41 * var(--res))}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center;gap:1em;padding:1em}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;border:2px solid #419CFB;border-radius:50%;padding:10px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:43px;width:43px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5em;align-items:baseline;color:#143c5d;width:75%}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:20px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{color:#fff;border-radius:4px;width:100%;background-color:#143c5d}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .chevron[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{height:20px;width:20px}ion-segment[_ngcontent-%COMP%]{--indicator-color: #0078d4;--background: transparent}ion-segment-button[checked][_ngcontent-%COMP%]{background-color:#0078d4;color:#fff}ion-input[_ngcontent-%COMP%]{font-family:Mont Regular;position:relative;--padding-start: 0;font-size:calc(36 * var(--res));border-bottom:1px solid rgb(92,92,92)}.relative[_ngcontent-%COMP%]{position:relative}.autocomplete-list[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;right:0;border:1px solid #ccc;height:auto!important;overflow-y:auto;background:white;z-index:1;border-radius:4px;border:10px;box-shadow:0 4px 6px #0000001a}.autocomplete-item[_ngcontent-%COMP%]{padding:8px;cursor:pointer}.autocomplete-item[_ngcontent-%COMP%]:hover{background-color:#f0f0f0}"]})}}return u})()}];let x=(()=>{class u{static{this.\u0275fac=function(c){return new(c||u)}}static{this.\u0275mod=e.$C({type:u})}static{this.\u0275inj=e.G2t({imports:[f.iI.forChild(L),f.iI]})}}return u})();var $=s(93887);let R=(()=>{class u{static{this.\u0275fac=function(c){return new(c||u)}}static{this.\u0275mod=e.$C({type:u})}static{this.\u0275inj=e.G2t({imports:[_.MD,g.YN,m.bv,$.G,v.h,g.X1,g.YN,x]})}}return u})()},43556:(A,y,s)=>{s.d(y,{B:()=>M});var _=s(73308),g=s(94934),m=s(45312),f=s(26409),h=(s(99987),s(2978)),e=s(33607),D=s(82571),O=s(14599),P=s(74657);let M=(()=>{class i{constructor(t,a,r,d,v){this.baseUrl=t,this.http=a,this.commonSrv=r,this.storageSrv=d,this.translateService=v,this.base_url=`${this.baseUrl.getOrigin()}${m.c.basePath}`,this.base_url+="companies"}create(t){var a=this;return(0,_.A)(function*(){try{return delete t._id,yield(0,g.s)(a.http.post(a.base_url,t))}catch(r){return a.commonSrv.getError("Echec de cr\xe9ation de la compagnie",r)}})()}getCompanies(t){var a=this;return(0,_.A)(function*(){try{let r=new f.Nl;const{category:d,city:v,limit:S,name:E,regionCom:T,solToId:I,tel:B,users:L,offset:x,enable:$=!0,projection:R,isLoyaltyProgDistributor:u}=t;return void 0!==d&&(r=r.append("category",d)),v&&(r=r.append("address.city",v)),E&&(r=r.append("name",E)),I&&(r=r.append("erpSoldToId",I)),B&&(r=r.append("tel",`${B}`)),R&&(r=r.append("projection",`${R}`)),L&&(r=r.append("users",`${L}`)),T&&(r=r.append("address.commercialRegion",T)),u&&(r=r.append("isLoyaltyProgDistributor",u)),void 0!==S&&(r=r.append("limit",S)),void 0!==x&&(r=r.append("offset",x)),r=r.set("enable",$),yield(0,g.s)(a.http.get(a.base_url,{params:r}))}catch(r){const v={message:a.commonSrv.getError("",r).message,color:"danger"};return yield a.commonSrv.showToast(v),r}})()}getParticularCompanies(t){var a=this;return(0,_.A)(function*(){let r=new f.Nl;const{limit:d,offset:v,enable:S=!0,commercialRegion:E}=t;return void 0!==d&&(r=r.append("limit",d)),void 0!==v&&(r=r.append("offset",v)),E&&(r=r.append("address.commercialRegion",E)),r=r.set("enable",S),yield(0,g.s)(a.http.get(a.base_url+"/particular-suppliers",{params:r}))})()}find(t){var a=this;return(0,_.A)(function*(){try{return yield(0,g.s)(a.http.get(a.base_url+"/"+t))}catch{return a.commonSrv.initCompany()}})()}getBalance(t){var a=this;return(0,_.A)(function*(){try{let r=new f.Nl;const{company:d}=a.storageSrv.getUserConnected();return r=r.set("_id",d?d?._id:t?.companyId),yield(0,g.s)(a.http.get(`${a.base_url}/balance`,{params:r}))}catch(r){return yield a.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de votre solde",color:"danger"}),r}})()}getUsersCompany(t,a){var r=this;return(0,_.A)(function*(){try{let d=new f.Nl;const{email:v,enable:S=!0}=a;return v&&(d=d.append("email",v)),d=d.append("enable",S),yield(0,g.s)(r.http.get(`${r.base_url}/${t}/users`,{params:d}))}catch(d){return yield r.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de vos informations",color:"danger"}),d}})()}static{this.\u0275fac=function(a){return new(a||i)(h.KVO(e.K),h.KVO(f.Qq),h.KVO(D.h),h.KVO(O.n),h.KVO(P.c$))}}static{this.\u0275prov=h.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})()},93387:(A,y,s)=>{s.d(y,{A:()=>O});var _=s(73308),g=s(26409),m=s(94934),f=s(58133),p=s(45312),h=s(2978),e=s(82571),D=s(33607);let O=(()=>{class P{constructor(i,n,t){this.http=i,this.commonSrv=n,this.baseUrlService=t,this.url=this.baseUrlService.getOrigin()+p.c.basePath+"prices"}getStores(i){var n=this;return(0,_.A)(function*(){try{let t=new g.Nl;const{companyId:a}=i;return a&&(t=t.append("company._id",a)),t=t.append("enable",!0),yield(0,m.s)(n.http.get(`${n.url}/${n.commonSrv.user.category===f.s.Commercial?"stores-sales":"stores"}`,{params:t}))}catch(t){const r={message:n.commonSrv.getError("",t).message,color:"danger"};return yield n.commonSrv.showToast(r),t}})()}getPricesForOrder(i){var n=this;return(0,_.A)(function*(){try{let t=new g.Nl;const{store:a,packaging:r,companyId:d,enable:v=!0}=i;return t=t.append("enable",v),a&&(t=t.append("store",a)),r&&(t=t.append("packaging",r)),d&&(t=t.append("company._id",d)),yield(0,m.s)(n.http.get(`${n.url}/${n.commonSrv.user.category===f.s.Commercial?"products-sales":"products"}`,{headers:{Authorization:`Bearer ${n.commonSrv?.user?.accessToken}`},params:t}))}catch(t){const r={message:n.commonSrv.getError("",t).message,color:"danger"};return yield n.commonSrv.showToast(r),t}})()}getPricesForCurrentOrder(i){var n=this;return(0,_.A)(function*(){try{let t=new g.Nl;const{storeId:a,packagingId:r,userId:d,enable:v=!0}=i;return t=t.append("enable",v),a&&(t=t.append("store",a)),r&&(t=t.append("packaging",r)),d&&(t=t.append("user",d)),yield(0,m.s)(n.http.get(`${n.url}/products-change`,{params:t}))}catch(t){return t}})()}getShippingAddress(i){var n=this;return(0,_.A)(function*(){try{let t=new g.Nl;const{offset:a,limit:r,enable:d=!0}=i;return a&&(t=t.append("offset",a)),r&&(t=t.append("limit",r)),t=t.append("enable",d),yield(0,m.s)(n.http.get(`${n.url}`,{params:t}))}catch(t){return t}})()}static{this.\u0275fac=function(n){return new(n||P)(h.KVO(g.Qq),h.KVO(e.h),h.KVO(D.K))}}static{this.\u0275prov=h.jDH({token:P,factory:P.\u0275fac,providedIn:"root"})}}return P})()},39316:(A,y,s)=>{s.d(y,{b:()=>O});var _=s(73308),g=s(26409),m=s(94934),f=s(45312),p=s(2978),h=s(82571),e=s(33607),D=s(77897);let O=(()=>{class P{constructor(i,n,t,a){this.http=i,this.commonSrv=n,this.baseUrlService=t,this.toastController=a,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+f.c.basePath+"products"}getProducts(i){var n=this;return(0,_.A)(function*(){try{let t=new g.Nl;return i?.limit&&(t=t.append("limit",i?.limit)),yield(0,m.s)(n.http.get(n.url,{params:t}))}catch(t){const r={message:n.commonSrv.getError("",t).message,color:"danger"};return yield n.commonSrv.showToast(r),t}})()}getProduct(i){var n=this;return(0,_.A)(function*(){try{return yield(0,m.s)(n.http.get(`${n.url}/${i}`))}catch(t){const r={message:n.commonSrv.getError("",t).message,color:"danger"};return yield n.commonSrv.showToast(r),t}})()}static{this.\u0275fac=function(n){return new(n||P)(p.KVO(g.Qq),p.KVO(h.h),p.KVO(e.K),p.KVO(D.K_))}}static{this.\u0275prov=p.jDH({token:P,factory:P.\u0275fac,providedIn:"root"})}}return P})()},94440:(A,y,s)=>{s.d(y,{c:()=>g});var _=s(2978);let g=(()=>{class m{transform(p,...h){return p?p.length>h[0]?`${p.substring(0,h[0]-3)}...`:p:""}static{this.\u0275fac=function(h){return new(h||m)}}static{this.\u0275pipe=_.EJ8({name:"truncateString",type:m,pure:!0})}}return m})()},28935:(A,y,s)=>{s.d(y,{G:()=>D});var _=s(73308),g=s(26409),m=s(45312),f=s(56610),p=s(2978),h=s(82571),e=s(33607);let D=(()=>{class O{constructor(M,i,n){this.commonSrv=M,this.baseUrlService=i,this.http=n,this.url="",this.getWholeSaleBoolean=!1,this.url=this.baseUrlService.getOrigin()+m.c.basePath}createWholeSale(M){var i=this;return(0,_.A)(function*(){try{const n=yield i.http.post(`${i.url}whole-sale`,M).toPromise();return i.commonSrv.showToast({color:"success",message:"Demi gros cre\xe9 avec succ\xe8s"}),n}catch(n){const a={message:i.commonSrv.getError("",n).message,color:"danger"};return yield i.commonSrv.showToast(a),n}})()}getWholeSale(M){var i=this;return(0,_.A)(function*(){try{let n=new g.Nl;const{offset:t,limit:a,startDate:r,endDate:d,tel:v,name:S,commercialRegion:E,animateDonutId:T}=M;return r&&d&&(n=n.append("startDate",new f.vh("fr").transform(r,"YYYY-MM-dd")),n=n.append("endDate",new f.vh("fr").transform(d,"YYYY-MM-dd"))),t&&(n=n.append("offset",t)),a&&(n=n.append("limit",a)),S&&(n=n.append("firstName",S)),E&&(n=n.append("address.commercialRegion",E)),T&&(n=n.append("associatedDonutAnimator.id",T)),v&&(n=n.append("tel",v)),n=n.append("enable",!0),yield i.http.get(`${i.url}whole-sale`,{params:n}).toPromise()}catch(n){const a={message:i.commonSrv.getError("",n).message,color:"danger"};return yield i.commonSrv.showToast(a),n}})()}find(M){var i=this;return(0,_.A)(function*(){try{return yield i.http.get(`${i.url}whole-sale/${M}`).toPromise()}catch(n){const a={message:i.commonSrv.getError("",n).message,color:"danger"};return yield i.commonSrv.showToast(a),null}})()}updateWholeSale(M){var i=this;return(0,_.A)(function*(){try{const n=yield i.http.patch(`${i.url}whole-sale/${M._id}`,M).toPromise();return i.commonSrv.showToast({color:"success",message:"Demi gros modifi\xe9 avec succ\xe8s"}),n}catch(n){const a={message:i.commonSrv.getError("",n).message,color:"danger"};return yield i.commonSrv.showToast(a),n}})()}static{this.\u0275fac=function(i){return new(i||O)(p.KVO(h.h),p.KVO(e.K),p.KVO(g.Qq))}}static{this.\u0275prov=p.jDH({token:O,factory:O.\u0275fac,providedIn:"root"})}}return O})()}}]);