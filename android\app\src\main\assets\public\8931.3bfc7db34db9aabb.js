"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8931],{98931:(k,v,o)=>{o.r(v),o.d(v,{EmployeesOrdersPageModule:()=>w});var a=o(77897),h=o(56610),P=o(74657),u=o(37222),M=o(77575),O=o(73308),R=o(35025),f=o.n(R),T=o(26409),C=o(99987),E=o(44444),i=o(18080),m=o(58133),p=o(88233),s=o(79898),t=o(2978),e=o(82571),c=o(81559),d=o(39316),_=o(14599),F=o(62049),D=o(2611),I=o(94440);function S(l,L){1&l&&t.nrm(0,"ion-progress-bar",16)}const y=function(l){return{active:l}};function $(l,L){if(1&l){const n=t.RV6();t.j41(0,"ion-tab-button",11),t.bIt("click",function(){t.eBV(n);const r=t.XpG();return r.filterForm.status=r.orderStatus.CREATED,r.orders=[],t.Njj(r.getOrders())}),t.j41(1,"ion-title"),t.EFF(2),t.nI1(3,"translate"),t.k0s()()}if(2&l){const n=t.XpG();t.Y8G("ngClass",t.eq3(4,y,n.filterForm.status===n.orderStatus.CREATED)),t.R7$(2),t.JRh(t.bMT(3,2,"history-page.tabs.in-progres"))}}function j(l,L){if(1&l){const n=t.RV6();t.j41(0,"ion-tab-button",11),t.bIt("click",function(){t.eBV(n);const r=t.XpG();return r.filterForm.status=r.orderStatus.CREDIT_IN_VALIDATION,r.orders=[],t.Njj(r.getOrders())}),t.j41(1,"ion-title"),t.EFF(2),t.nI1(3,"translate"),t.k0s()()}if(2&l){const n=t.XpG();t.Y8G("ngClass",t.eq3(4,y,n.filterForm.status===n.orderStatus.CREDIT_IN_VALIDATION)),t.R7$(2),t.JRh(t.bMT(3,2,"history-page.tabs.in-progres"))}}function B(l,L){if(1&l){const n=t.RV6();t.j41(0,"ion-tab-button",11),t.bIt("click",function(){t.eBV(n);const r=t.XpG();return r.filterForm.status=r.orderStatus.CREDIT_IN_VALIDATION,r.orders=[],t.Njj(r.getOrders())}),t.j41(1,"ion-title"),t.EFF(2),t.nI1(3,"translate"),t.k0s()()}if(2&l){const n=t.XpG();t.Y8G("ngClass",t.eq3(4,y,n.filterForm.status===n.orderStatus.CREDIT_IN_VALIDATION)),t.R7$(2),t.JRh(t.bMT(3,2,"history-page.tabs.prevalidate"))}}function x(l,L){if(1&l){const n=t.RV6();t.j41(0,"ion-card",17),t.bIt("click",function(){const b=t.eBV(n).$implicit,A=t.XpG();return t.Njj(A.orderService.order=b)}),t.j41(1,"ion-card-content")(2,"div",18)(3,"ion-label"),t.EFF(4),t.nI1(5,"translate"),t.j41(6,"strong"),t.EFF(7),t.k0s()(),t.j41(8,"ion-label"),t.EFF(9),t.nI1(10,"translate"),t.j41(11,"strong"),t.EFF(12),t.k0s()(),t.j41(13,"ion-label"),t.EFF(14),t.nI1(15,"translate"),t.j41(16,"strong"),t.EFF(17),t.nI1(18,"number"),t.k0s()(),t.j41(19,"ion-label"),t.EFF(20,"Date: "),t.j41(21,"strong"),t.EFF(22),t.nI1(23,"date"),t.k0s()(),t.j41(24,"ion-label"),t.EFF(25,"Statut: "),t.j41(26,"strong",19),t.nI1(27,"colorStatusOrder"),t.EFF(28),t.nI1(29,"statusOrder"),t.k0s()()(),t.j41(30,"div",20),t.nrm(31,"ion-icon",21),t.k0s()()()}if(2&l){const n=L.$implicit;t.Mz_("routerLink","/order/detail/",null==n?null:n._id,""),t.R7$(4),t.SpI("",t.bMT(5,10,"history-page.employee-name"),": "),t.R7$(3),t.JRh((null==n||null==n.user?null:n.user.firstName)+" "+(null==n||null==n.user?null:n.user.lastName)||"N/A"),t.R7$(2),t.SpI("",t.bMT(10,12,"history-page.reference"),": "),t.R7$(3),t.JRh((null==n?null:n.customerReference)||(null==n?null:n.appReference)||"N/A"),t.R7$(2),t.SpI("",t.bMT(15,14,"history-page.amount"),": "),t.R7$(3),t.SpI("",t.bMT(18,16,null==n||null==n.cart||null==n.cart.amount?null:n.cart.amount.TTC)," FCFA"),t.R7$(5),t.JRh(t.i5U(23,18,null==n?null:n.created_at,"dd/MM/YYYY \xe0 HH:mm")),t.R7$(4),t.Y8G("ngClass",t.bMT(27,21,n.status)),t.R7$(2),t.JRh(t.bMT(29,23,null==n?null:n.status))}}function N(l,L){1&l&&(t.j41(0,"div",22),t.nrm(1,"ion-img",23),t.j41(2,"ion-label"),t.EFF(3),t.nI1(4,"translate"),t.k0s()()),2&l&&(t.R7$(3),t.SpI(" ",t.bMT(4,1,"history-page.empty-order")," "))}const Y=[{path:"",component:(()=>{class l{constructor(n,g,r,b,A,U,G){this.location=n,this.commonSrv=g,this.orderService=r,this.modalCtrl=b,this.productService=A,this.storageService=U,this.translateService=G,this.isLoading=!1,this.tabOption=p.Re.PAID,this.orderStatus=p.Re,this.employeeType=E.PB,this.orders=[],this.skeletons=[1,2,3,4,5,6],this.filterForm={status:this.commonSrv?.user?.employeeType===E.PB.DRH?p.Re.CREDIT_IN_VALIDATION:p.Re.CREATED,date:{start:f()().startOf("year"),end:f()().endOf("year")},userCategory:m.s.EmployeeLapasta,paymentMode:i.I.CREDIT,customer:"",product:"",validation:null,appReference:"",enable:!0},this.offset=0,this.limit=20}ionViewWillEnter(){var n=this;return(0,O.A)(function*(){n.isLoading=!0,n.storageService.getUserConnected(),n.orders=[],yield n.getOrders()})()}getOrderByUser(){var n=this;return(0,O.A)(function*(){n.skeletons=[1,2,3,4,5,6],n.isLoading=!0;const g={status:n.tabOption,limit:n.limit,offset:n.offset,...n.filterData},r=(yield n.orderService.getAllOrder(g)).data;n.orders=n.orders.concat(r),n.isLoading=!1,n.skeletons=[]})()}getOrders(){var n=this;return(0,O.A)(function*(){if(n.isLoading=!0,(n.filterForm.date.start||n.filterForm.date.end)&&n.filterForm.date.start>n.filterForm.date.end)return yield n.commonSrv.showToast({message:n.translateService.currentLang===C.T.French?"Incorrect time interval !!":"Intervale de temps incorrect !!",color:"warning"}),n.isLoading=!1;let g={...n.filterForm,limit:n.limit,offset:n.offset};n.commonSrv.user.employeeType===E.PB.CORDO_RH&&n.filterForm.status===p.Re.CREDIT_IN_VALIDATION&&(g.validation=p.Cs.CORDO_RH),n.commonSrv.user.employeeType===E.PB.CORDO_RH&&n.filterForm.status===p.Re.PAID&&(g.validation=p.Cs.DRH);const r=yield n.orderService.getOrders(g);return r instanceof T.yz?(yield n.commonSrv.showToast({message:n.translateService.currentLang===C.T.French?`One occurred.You do not have the authorization.${r?.message}`:`Une erreur est survenue.${r?.message}`,color:"warning"}),n.isLoading=!1):(n.orders=r?.data,n.orders.forEach(b=>b?.cart?.items.filter(U=>U.quantity)),n.isLoading=!1)})()}doRefresh(n){var g=this;return(0,O.A)(function*(){g.filterData=null,g.orders=[],yield g.getOrders(),n.target.complete()})()}getFlowOrder(n){var g=this;return(0,O.A)(function*(){g.offset=g.offset+g.limit+1,yield g.getOrders(),n.target.complete()})()}showFilter(){var n=this;return(0,O.A)(function*(){const g=yield n.modalCtrl.create({component:s.W,initialBreakpoint:.6,cssClass:"modal",breakpoints:[0,.6,.7],mode:"ios",componentProps:{filterData:n.filterData}});g.present();const r=(yield g.onWillDismiss()).data;r&&(n.filterForm.date.start=r?.startDate,n.filterForm.date.end=r?.endDate,n.filterForm.appReference=r?.customerReference,n.orders=[],yield n.getOrders())})()}back(){this.location.back()}static{this.\u0275fac=function(g){return new(g||l)(t.rXU(h.aZ),t.rXU(e.h),t.rXU(c.Q),t.rXU(a.W3),t.rXU(d.b),t.rXU(_.n),t.rXU(F.E))}}static{this.\u0275cmp=t.VBU({type:l,selectors:[["app-employees-orders"]],decls:28,vars:25,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],["slot","end","src","/assets/icons/funnel-outline.svg",3,"click"],[3,"fullscreen"],["type","indeterminate",4,"ngIf"],["id","container"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],["slot","top",1,"tab-container","ion-no-border"],[3,"ngClass","click",4,"ngIf"],[3,"ngClass","click"],[1,"order-list"],["class","order",3,"routerLink","click",4,"ngFor","ngForOf"],[3,"ionInfinite"],["class","empty-list",4,"ngIf"],["type","indeterminate"],[1,"order",3,"routerLink","click"],[1,"detail"],[3,"ngClass"],[1,"icon"],["src","/assets/icons/arrow-forward-green.svg"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"]],template:function(g,r){1&g&&(t.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),t.bIt("click",function(){return r.back()}),t.k0s(),t.j41(3,"ion-title",2),t.EFF(4),t.nI1(5,"truncateString"),t.nI1(6,"translate"),t.k0s(),t.j41(7,"ion-img",3),t.bIt("click",function(){return r.showFilter()}),t.k0s()()(),t.j41(8,"ion-content",4),t.DNE(9,S,1,0,"ion-progress-bar",5),t.j41(10,"div",6)(11,"ion-refresher",7),t.bIt("ionRefresh",function(A){return r.doRefresh(A)}),t.nrm(12,"ion-refresher-content",8),t.nI1(13,"translate"),t.nI1(14,"translate"),t.k0s(),t.j41(15,"ion-tab-bar",9),t.DNE(16,$,4,6,"ion-tab-button",10),t.DNE(17,j,4,6,"ion-tab-button",10),t.DNE(18,B,4,6,"ion-tab-button",10),t.j41(19,"ion-tab-button",11),t.bIt("click",function(){return r.filterForm.status=r.orderStatus.PAID,r.orders=[],r.getOrders()}),t.j41(20,"ion-title"),t.EFF(21),t.nI1(22,"translate"),t.k0s()()(),t.j41(23,"div",12),t.DNE(24,x,32,25,"ion-card",13),t.j41(25,"ion-infinite-scroll",14),t.bIt("ionInfinite",function(A){return r.getFlowOrder(A)}),t.nrm(26,"ion-infinite-scroll-content"),t.k0s()(),t.DNE(27,N,5,3,"div",15),t.k0s()()),2&g&&(t.R7$(4),t.JRh(t.i5U(5,12,t.bMT(6,15,"all-orders-page.employees-orders"),25)),t.R7$(4),t.Y8G("fullscreen",!0),t.R7$(1),t.Y8G("ngIf",r.isLoading),t.R7$(3),t.FS9("pullingText",t.bMT(13,17,"refresher.pull")),t.Mz_("refreshingText","",t.bMT(14,19,"refresher.refreshing"),"..."),t.R7$(4),t.Y8G("ngIf",(null==r.commonSrv||null==r.commonSrv.user?null:r.commonSrv.user.employeeType)===r.employeeType.CORDO_RH),t.R7$(1),t.Y8G("ngIf",(null==r.commonSrv||null==r.commonSrv.user?null:r.commonSrv.user.employeeType)===r.employeeType.DRH),t.R7$(1),t.Y8G("ngIf",(null==r.commonSrv||null==r.commonSrv.user?null:r.commonSrv.user.employeeType)===r.employeeType.CORDO_RH),t.R7$(1),t.Y8G("ngClass",t.eq3(23,y,r.filterForm.status===r.orderStatus.PAID)),t.R7$(2),t.JRh(t.bMT(22,21,"history-page.tabs.validate")),t.R7$(3),t.Y8G("ngForOf",r.orders),t.R7$(3),t.Y8G("ngIf",(null==r.orders?null:r.orders.length)<=0&&!r.isLoading))},dependencies:[h.YU,h.Sq,h.bT,a.b_,a.I9,a.W9,a.eU,a.iq,a.KW,a.Ax,a.Hp,a.he,a.FH,a.To,a.Ki,a.Jq,a.qW,a.BC,a.ai,a.N7,M.Wk,h.QX,h.vh,D.D3,I.c,D.qZ,P.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:calc(41 * var(--res));--border-color: transparent;--background: transparent;background:#fff;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding-top:0}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]{margin:auto;min-height:40px;margin-bottom:calc(50 * var(--res));border:none}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:calc(45 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#143c5d;border-bottom:3px solid #143c5d}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#143c5d}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block;line-height:initial;margin-top:calc(15.7 * var(--res));font-family:Mont Light;color:#000;font-size:calc(36 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-primary-400[_ngcontent-%COMP%]{color:#0d7d3d!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-success-200[_ngcontent-%COMP%]{background-color:#b8ddb6!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-info-500[_ngcontent-%COMP%]{color:#0af!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-info-100[_ngcontent-%COMP%]{background-color:#cef!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-default-400[_ngcontent-%COMP%]{color:#fff!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-info-500[_ngcontent-%COMP%]{background-color:#0af!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-tertiary-200[_ngcontent-%COMP%]{background-color:#f0efef!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:2rem;height:2rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]{height:4em;width:100%;margin-bottom:1rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:100%;height:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{margin-top:20vh;display:flex;align-items:center;flex-direction:column;justify-content:center;height:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}ion-infinite-scroll-content[_ngcontent-%COMP%]{min-height:15px}"]})}}return l})()}];let K=(()=>{class l{static{this.\u0275fac=function(g){return new(g||l)}}static{this.\u0275mod=t.$C({type:l})}static{this.\u0275inj=t.G2t({imports:[M.iI.forChild(Y),M.iI]})}}return l})();var W=o(93887);let w=(()=>{class l{static{this.\u0275fac=function(g){return new(g||l)}}static{this.\u0275mod=t.$C({type:l})}static{this.\u0275inj=t.G2t({imports:[h.MD,u.YN,a.bv,W.G,P.h,u.X1,K]})}}return l})()},81559:(k,v,o)=>{o.d(v,{Q:()=>i});var a=o(73308),h=o(35025),P=o.n(h),u=o(94934),M=o(56610),O=o(45312),R=o(26409),f=o(2978),T=o(82571),C=o(33607),E=o(14599);let i=(()=>{class m{constructor(s,t,e,c){this.http=s,this.commonSrv=t,this.baseUrlService=e,this.storageSrv=c,this.url=this.baseUrlService.getOrigin()+O.c.basePath}create(s){var t=this;return(0,a.A)(function*(){try{return yield(0,u.s)(t.http.post(`${t.url}orders`,s))}catch(e){const d={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(d),e}})()}createOrderByCommercialForClient(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,u.s)(e.http.post(`${e.url}orders/${t}`,s))}catch(c){const _={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(_),c}})()}getAllOrder(s){var t=this;return(0,a.A)(function*(){try{let e=new R.Nl;const{num:c,commercialId:d,status:_,offset:F,limit:D,startDate:I,endDate:S,customerReference:y,selectedCompanyId:$}=s;return I&&S&&(e=e.append("startDate",new M.vh("fr").transform(I,"YYYY-MM-dd"))),S&&I&&(e=e.append("endDate",new M.vh("fr").transform(S,"YYYY-MM-dd"))),y&&(e=e.append("appReference",y)),$&&(e=e.append("selectedCompanyId",$)),d&&(e=e.append("commercial",d)),void 0!==F&&(e=e.append("offset",F)),D&&(e=e.append("limit",D)),_&&(e=e.append("status",_)),c&&(e=e.append("appReference",c)),yield(0,u.s)(t.http.get(`${t.url}orders/history`,{params:e}))}catch(e){const d={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(d),e}})()}getOrders(s){var t=this;return(0,a.A)(function*(){try{let e=new R.Nl;const{status:c,appReference:d,offset:_,limit:F,userCategory:D,paymentMode:I,validation:S,customer:y,product:$,date:j,enable:B=!0}=s;return _&&(e=e.append("offset",_)),F&&(e=e.append("limit",F)),c&&(e=e.append("status",c)),d&&(e=e.append("appReference",`${d}`)),I&&(e=e.append("payment.mode.id",I)),D&&(e=e.append("user.category",D)),y&&(e=e.append("user.email",y)),$&&(e=e.append("cart.items.product.label",$)),S&&(e=e.append("validation",S)),j.start&&j.end&&(e=e.append("startDate",P()(j.start).format("YYYY-MM-DD")),e=e.append("endDate",P()(j.end).format("YYYY-MM-DD"))),e=e.append("enable",B),yield(0,u.s)(t.http.get(`${t.url}orders`,{params:e}))}catch(e){return e}})()}updateOrders(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,u.s)(e.http.patch(`${e.url}orders/${s}`,t))}catch(c){const _={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(_),c}})()}RhValidatedOrder(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,u.s)(e.http.patch(`${e.url}orders/${s._id}/validate`,t))}catch(c){const _={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(_),c}})()}RhRejectOrder(s){var t=this;return(0,a.A)(function*(){try{return yield(0,u.s)(t.http.patch(`${t.url}orders/${s._id}/reject`,{}))}catch(e){const d={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(d),e}})()}sendOtp(s){var t=this;return(0,a.A)(function*(){try{return yield(0,u.s)(t.http.post(`${t.url}callback/afriland`,s))}catch(e){const d={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(d),e}})()}sendWallet(s){var t=this;return(0,a.A)(function*(){try{return yield(0,u.s)(t.http.post(`${t.url}orders/verify-Wallet-Nber`,s))}catch(e){const d={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(d),e}})()}ubaPayment(s){var t=this;return(0,a.A)(function*(){try{return yield(0,u.s)(t.http.post(`${t.url}orders/m2u-paymentRequest`,s))}catch(e){const d={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(d),e}})()}find(s){var t=this;return(0,a.A)(function*(){try{return yield(0,u.s)(t.http.get(t.url+"orders/"+s))}catch(e){const d={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(d),e}})()}getCardToken(){var s=this;return(0,a.A)(function*(){try{return yield(0,u.s)(s.http.post(`${s.url}orders/order-generate-visa-key`,{}))}catch(t){const c={message:s.commonSrv.getError("",t).message,color:"danger"};return yield s.commonSrv.showToast(c),t}})()}setupPayerAuthentication(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,u.s)(e.http.post(`${e.url}orders/order-setup-payer-auth`,{transientTokenJwt:s,order:t}))}catch(c){const _={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(_),c}})()}authorizationWithPAEnroll(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,u.s)(e.http.post(`${e.url}orders/order-authorization-pay-enroll`,{order:s,options:t}))}catch(c){const _={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(_),c}})()}checkIfOrderExist(s){var t=this;return(0,a.A)(function*(){try{return yield(0,u.s)(t.http.get(`${t.url}orders/${s}/exist`))}catch(e){const d={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(d),e}})()}generatePurchaseOrder(s){var t=this;return(0,a.A)(function*(){try{return yield(0,u.s)(t.http.get(`${t.url}orders/${s}/generate-purchase`))}catch(e){const d={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(d),e}})()}cancellationOrder(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,u.s)(e.http.patch(`${e.url}orders/${s}/cancellation-order`,t))}catch(c){const _={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(_),c}})()}updateCarrier(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,u.s)(e.http.patch(`${e.url}orders/${s}/add-carrier`,{carrier:t}))}catch(c){const _={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(_),c}})()}static{this.\u0275fac=function(t){return new(t||m)(f.KVO(R.Qq),f.KVO(T.h),f.KVO(C.K),f.KVO(E.n))}}static{this.\u0275prov=f.jDH({token:m,factory:m.\u0275fac,providedIn:"root"})}}return m})()},39316:(k,v,o)=>{o.d(v,{b:()=>T});var a=o(73308),h=o(26409),P=o(94934),u=o(45312),M=o(2978),O=o(82571),R=o(33607),f=o(77897);let T=(()=>{class C{constructor(i,m,p,s){this.http=i,this.commonSrv=m,this.baseUrlService=p,this.toastController=s,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+u.c.basePath+"products"}getProducts(i){var m=this;return(0,a.A)(function*(){try{let p=new h.Nl;return i?.limit&&(p=p.append("limit",i?.limit)),yield(0,P.s)(m.http.get(m.url,{params:p}))}catch(p){const t={message:m.commonSrv.getError("",p).message,color:"danger"};return yield m.commonSrv.showToast(t),p}})()}getProduct(i){var m=this;return(0,a.A)(function*(){try{return yield(0,P.s)(m.http.get(`${m.url}/${i}`))}catch(p){const t={message:m.commonSrv.getError("",p).message,color:"danger"};return yield m.commonSrv.showToast(t),p}})()}static{this.\u0275fac=function(m){return new(m||C)(M.KVO(h.Qq),M.KVO(O.h),M.KVO(R.K),M.KVO(f.K_))}}static{this.\u0275prov=M.jDH({token:C,factory:C.\u0275fac,providedIn:"root"})}}return C})()},44444:(k,v,o)=>{o.d(v,{PB:()=>T,cs:()=>P,iL:()=>E}),o(68953);class h{constructor(m){this.email="",this.firstName="",this.lastName="",this.tel="",this.password="",this.cni="",this.nui="",this.address={region:"",city:"",district:""},this.category=m}}class P extends h{}var T=function(i){return i[i.NORMAL=100]="NORMAL",i[i.CORDO_RH=101]="CORDO_RH",i[i.DRH=102]="DRH",i}(T||{}),E=function(i){return i[i.BHB=101]="BHB",i[i.BS=102]="BS",i[i.BPI=103]="BPI",i}(E||{})},94440:(k,v,o)=>{o.d(v,{c:()=>h});var a=o(2978);let h=(()=>{class P{transform(M,...O){return M?M.length>O[0]?`${M.substring(0,O[0]-3)}...`:M:""}static{this.\u0275fac=function(O){return new(O||P)}}static{this.\u0275pipe=a.EJ8({name:"truncateString",type:P,pure:!0})}}return P})()}}]);