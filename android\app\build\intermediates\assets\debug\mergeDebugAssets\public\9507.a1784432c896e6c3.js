"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9507],{29507:(L,I,a)=>{a.r(I),a.d(I,{CreateIndirectUserPageModule:()=>Z});var b=a(56610),m=a(37222),h=a(77897),P=a(77575),_=a(73308),e=a(2978),E=a(63037),U=a(43556),y=a(58133),x=a(5141),v=a(44444),d=a(71682),s=a(82571),u=a(23985);const O=(0,a(22126).F3)("Geolocation",{web:()=>a.e(3243).then(a.bind(a,33243)).then(i=>new i.GeolocationWeb)});!function C(i=!1){typeof window>"u"||(window.CapacitorUtils=window.CapacitorUtils||{},void 0===window.Capacitor||i?void 0!==window.cordova&&function p(i){i.CapacitorUtils.Synapse=new Proxy({},{get:(f,t)=>i.cordova.plugins[t]})}(window):function r(i){i.CapacitorUtils.Synapse=new Proxy({},{get:(f,t)=>new Proxy({},{get:(n,o)=>(l,g,F)=>{const N=i.Capacitor.Plugins[t];void 0!==N?"function"==typeof N[o]?(0,_.A)(function*(){try{const A=yield N[o](l);g(A)}catch(A){F(A)}})():F(new Error(`Method ${o} not found in Capacitor plugin ${t}`)):F(new Error(`Capacitor plugin ${t} not found`))}})})}(window))}();var M=a(32401),T=a(28935),S=a(71333),R=a(74657);function w(i,f){1&i&&e.nrm(0,"app-progress-spinner")}function k(i,f){if(1&i&&(e.j41(0,"ion-select-option",34),e.EFF(1),e.k0s()),2&i){const t=f.$implicit;e.Y8G("value",t),e.R7$(1),e.SpI(" ",t," ")}}function j(i,f){if(1&i&&(e.j41(0,"ion-select-option",34),e.EFF(1),e.k0s()),2&i){const t=f.$implicit;e.Y8G("value",t),e.R7$(1),e.SpI(" ",t," ")}}function D(i,f){if(1&i){const t=e.RV6();e.j41(0,"ion-item",40),e.bIt("click",function(){const l=e.eBV(t).$implicit,g=e.XpG(3);return e.Njj(g.selectNeighborhood(l))}),e.EFF(1),e.k0s()}if(2&i){const t=f.$implicit;e.R7$(1),e.SpI(" ",t," ")}}function $(i,f){if(1&i&&(e.j41(0,"ion-list",38),e.DNE(1,D,2,1,"ion-item",39),e.k0s()),2&i){const t=e.XpG(2);e.R7$(1),e.Y8G("ngForOf",t.filteredNeighborhoods)}}function G(i,f){if(1&i){const t=e.RV6();e.j41(0,"ion-item")(1,"ion-label",10),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"ion-input",35,36),e.bIt("ionChange",function(o){e.eBV(t);const l=e.XpG();return e.Njj(l.filterNeighborhoods(o))}),e.k0s(),e.DNE(6,$,2,1,"ion-list",37),e.k0s()}if(2&i){const t=e.XpG();e.R7$(2),e.JRh(e.bMT(3,3,"indirect-clients.Neighborhood")),e.R7$(2),e.Y8G("value",t.selectedNeighborhood),e.R7$(2),e.Y8G("ngIf",t.filteredNeighborhoods.length>0&&t.isSearching)}}function B(i,f){1&i&&(e.j41(0,"ion-item")(1,"ion-label",10),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.nrm(4,"ion-input",41),e.k0s()),2&i&&(e.R7$(2),e.JRh(e.bMT(3,1,"indirect-clients.enter-net")))}function W(i,f){if(1&i&&(e.j41(0,"ion-select-option",34),e.EFF(1),e.k0s()),2&i){const t=f.$implicit;e.Y8G("value",t.code),e.R7$(1),e.SpI(" ",null==t?null:t.name," ")}}function Y(i,f){if(1&i&&(e.j41(0,"ion-select-option",34),e.EFF(1),e.k0s()),2&i){const t=f.$implicit;e.Y8G("value",t),e.R7$(1),e.SpI(" ",null==t?null:t.name," ")}}function K(i,f){if(1&i&&(e.j41(0,"div",42),e.nrm(1,"img",43),e.j41(2,"div",44),e.EFF(3),e.k0s()()),2&i){const t=e.XpG();e.R7$(1),e.Y8G("src",t.attachment.file,e.B4B),e.R7$(2),e.JRh(t.attachment.name)}}function z(i,f){if(1&i&&(e.j41(0,"div")(1,"ion-label"),e.EFF(2),e.k0s()()),2&i){const t=e.XpG();e.R7$(2),e.JRh(t.localisation.latitude)}}function V(i,f){if(1&i&&(e.j41(0,"div")(1,"ion-label"),e.EFF(2),e.k0s()()),2&i){const t=e.XpG();e.R7$(2),e.JRh(t.localisation.longitude)}}function J(i,f){1&i&&e.nrm(0,"ion-spinner",45)}const X=[{path:"",component:(()=>{class i{constructor(t,n,o,l){this.fb=t,this.imageCompress=n,this.commonSrv=o,this.wholeSrv=l,this.companySrv=(0,e.WQX)(U.B),this.router=(0,e.WQX)(P.Ix),this.commonService=(0,e.WQX)(s.h),this.location=(0,e.WQX)(b.aZ),this.userServ=(0,e.WQX)(u.D),this.authenticationService=(0,e.WQX)(d.k),this.localisation=null,this.LIMIT_SIZE=15e5,this.hasDescription=!1,this.isManualInput=!1,this.filteredNeighborhoods=[],this.selectedNeighborhood="",this.isSearching=!1,this.wholeSales=[],this.companies=[],this.userToUpdate=null,this.attachment={file:"",name:"",contentType:""},this.particualrCategories=[{name:"BHB",code:v.iL.BHB},{name:"BS",code:v.iL.BS}],this.previewImage=null,this.isPreviewVisible=!1,this.isFullscreen=!1}ngOnInit(){var t=this;return(0,_.A)(function*(){t.userToUpdate=t.userServ.currentUserParticular,t.initForm(),t.regions=t.commonService.getRegions(),yield t.getWholeSale(),t.userToUpdate&&t.patchForm(t.userToUpdate)})()}getRegion(t){var n=this;return(0,_.A)(function*(){const o=t.detail.value;n.cities=n.commonService.getCities(o);try{const l=(yield n.companySrv.getCompanies({projection:"name,erpSoldToId",regionCom:n.getCommercialRegion(o),isLoyaltyProgDistributor:!0}))?.data||[];n.wholeSales=n.wholeSales.concat(l)}catch(l){console.error("Erreur lors du chargement des compagnies pour la r\xe9gion s\xe9lectionn\xe9e\u202f:",l)}})()}getWholeSale(){var t=this;return(0,_.A)(function*(){const n=yield t.wholeSrv.getWholeSale({animateDonutId:t.commonSrv.user._id}),o=yield t.wholeSrv.getWholeSale({commercialRegion:t.commonSrv.user.address.commercialRegion});t.wholeSales=n?.data?.length?n?.data:o?.data||[]})()}getcities(t){this.neighborhoods=this.commonService.getNeighborhood(t.detail.value),this.isManualInput=!(this.neighborhoods?.length>0)}filterNeighborhoods(t){const n=t.detail.value.toLowerCase();this.isSearching=!0,this.filteredNeighborhoods=""!==n?this.neighborhoods.filter(o=>o.toLowerCase().includes(n)):[...this.neighborhoods]}selectNeighborhood(t){this.selectedNeighborhood=t,this.form.get("neighborhood").setValue(t),this.isSearching=!1}initForm(){this.form=this.fb.group({firstName:["",m.k0.required],particularType:["",m.k0.required],neighborhood:[""],social:[""],tel:["",[m.k0.required,this.phoneValidator()]],region:["",m.k0.required],city:["",m.k0.required],district:[""],associatedCompanies:[[]]})}patchForm(t){this.form.patchValue({firstName:t.firstName,tel:t.tel,social:t.socialReason,region:t.address?.region||"",district:t.address?.district||"",associatedCompanies:t.associatedSuppliers?.map(n=>n._id)||[]}),this.localisation={latitude:t?.localisation?.latitude,longitude:t?.localisation?.longitude},console.log("Form patched:",this.form.value),console.log("User to update:",t)}onSubmit(){var t=this;return(0,_.A)(function*(){if(t.isLoading=!0,t.form.invalid)return t.markFormGroupTouched(t.form),t.commonService.showToast({color:"warning",message:"An error occurred"}),void(t.isLoading=!1);const n=t.form.value,o=new v.cs(0);o.firstName=n.firstName,o.socialReason=n.social,o.category=y.s.Particular,o.categoryType=n.particularType,o.tel=n.tel,o.email=Math.floor(16777215*Math.random()).toString(16)+"@mail.com",o.address={region:n?.region,city:n?.city,commercialRegion:t.getCommercialRegion(n?.region),district:n.district,neighborhood:n.neighborhood},o.associatedSuppliers=n.associatedCompanies,o.profilePicture=t.attachment.file,o.localisation=t.localisation;const l=t.commonSrv.user;l&&(o.associatedCommercial={_id:l._id,firstName:l.firstName,category:l.category});try{if(t.userToUpdate){const g=t.form.value;t.userToUpdate.firstName=g.firstName||t.userToUpdate.firstName,t.userToUpdate.socialReason=g.social||t.userToUpdate.socialReason,t.userToUpdate.category=y.s.Particular,t.userToUpdate.tel=g.tel||t.userToUpdate.tel,t.userToUpdate.categoryType=g.categoryType||t.userToUpdate.categoryType,t.userToUpdate.associatedSuppliers=g.associatedCompanies,t.userToUpdate.profilePicture=t.attachment.file,t.userToUpdate.address={region:g?.region??t.userToUpdate?.address?.region,commercialRegion:t.getCommercialRegion(g?.region),district:g?.district||t.userToUpdate?.address?.district,neighborhood:g?.neighborhood||t.userToUpdate?.address?.neighborhood},yield t.userServ.updateUserParticular(t.userToUpdate),t.router.navigateByUrl("/navigation/indirect-user"),t.isLoading=!0,t.userServ.currentUserParticular=null,t.form.reset()}else 201==(yield t.authenticationService.signupParticular(o))?.status&&(t.router.navigateByUrl("/navigation/indirect-user"),t.isLoading=!1,t.form.reset())}catch{}finally{t.isLoading=!1}})()}getCommercialRegion(t){const n=x.MJ,o=t.trim().toLowerCase();for(const[l,g]of Object.entries(n))if(console.log("Checking key:",l,"with regions:",g),g.some(F=>F.trim().toLowerCase()===o))return console.log("Match found:",l),l;return console.log("No match found"),null}markFormGroupTouched(t){Object.values(t.controls).forEach(n=>{n.markAsTouched(),n instanceof m.gE&&this.markFormGroupTouched(n)})}setAttachment(t){var n=this;return(0,_.A)(function*(){try{n.file=n.getFileFromDataSet(t),n.attachment.file=yield n.getFileDataUrl(n.file);let o=yield n.getFileSize(n.file,n.attachment.file);n.validateFileSize(o,n.LIMIT_SIZE),[n.attachment.name,n.attachment.contentType]=[n.file.name,n.file.type]}catch(o){n.handleError(o)}})()}getFileFromDataSet(t){const n=t.target.files[0];return this.validate(n),n}getFileDataUrl(t){var n=this;return(0,_.A)(function*(){const o=yield n.convertFileToDataUrl(t);return n.validate(o),o})()}convertFileToDataUrl(t){return new Promise((n,o)=>{const l=new FileReader;l.readAsDataURL(t),l.onload=()=>n(l.result),l.onerror=g=>o(g)})}validate(t){if(!t)throw new Error("Une erreur est survenue, veuillez ressayer SVP !")}getFileSize(t,n){var o=this;return(0,_.A)(function*(){let l=t.size;return"application/pdf"!=t.type&&(n=yield o.imageCompress.compressFile(n,E._k.Up),l=o.imageCompress.byteCount(n)),l})()}validateFileSize(t,n){if(t>n)throw new Error("Veuillez choisir un fichier de moins de 1,5 MB SVP !")}handleError(t){this.file=null,this.commonService.showToast({color:"danger",message:`${t.message}`})}back(){this.userServ.currentUserParticular=null,this.form.reset(),this.location.back()}getLocation(){var t=this;return(0,_.A)(function*(){t.isLoading=!0;try{const n=yield O.getCurrentPosition();t.localisation={latitude:n.coords.latitude,longitude:n.coords.longitude},console.log("Current location:",t.localisation)}catch(n){console.error("Error getting location",n)}finally{t.isLoading=!1}})()}phoneValidator(){return t=>{const n=t.value;return n?this.commonSrv.isPhone(n)?null:{invalidPhone:!0}:null}}takePicture(){var t=this;return(0,_.A)(function*(){try{const n=yield M.i7.getPhoto({quality:90,allowEditing:!1,resultType:M.LK.DataUrl,source:M.ru.Camera});n.dataUrl&&(t.attachment.file=n.dataUrl,t.attachment.contentType="image/jpeg",t.attachment.name="camera_photo.jpg",t.commonService.showToast({color:"success",message:"Photo captur\xe9e avec succ\xe8s"}))}catch(n){console.error("Erreur lors de la capture photo:",n),t.commonService.showToast({color:"danger",message:"Erreur lors de la capture photo"})}})()}showPreview(){(this.file?.name||this.attachment?.name)&&(this.isPreviewVisible=!0)}closePreview(){this.isPreviewVisible=!1,this.isFullscreen=!1}toggleFullscreen(){this.isFullscreen=!this.isFullscreen}static{this.\u0275fac=function(n){return new(n||i)(e.rXU(m.ok),e.rXU(E.ep),e.rXU(s.h),e.rXU(T.G))}}static{this.\u0275cmp=e.VBU({type:i,selectors:[["app-create-indirect-user"]],decls:92,vars:58,consts:[[4,"ngIf"],[3,"translucent"],[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"buttons"],[1,"transparent"],["name","search"],["name","funnel-outline"],[3,"fullscreen"],[3,"formGroup","ngSubmit"],["position","floating"],["formControlName","firstName","clearInput",""],["formControlName","social","clearInput",""],["type","number","clearInput","","formControlName","tel"],["mode","ios","formControlName","region","interface","action-sheet",3,"cancelText","ionChange"],["stores",""],[3,"value",4,"ngFor","ngForOf"],["mode","ios","formControlName","city","interface","action-sheet",3,"cancelText","ionChange"],["type","text","clearInput","","formControlName","district"],["formControlName","particularType","mode","ios","interface","action-sheet"],["formControlName","associatedCompanies","multiple","true","mode","ios","interface","action-sheet"],[1,"iconImage"],[1,"image"],[1,"attachment-btn"],["type","file","id","file","accept","image/*,.pdf","multiple","",2,"display","none",3,"change"],["src","/assets/icons/imageIcon.svg","alt","Upload","slot","end","onclick","document.getElementById('file').click()",2,"cursor","pointer","width","40px","height","40px"],["src","/assets/icons/camera.png","alt","Camera","slot","end",2,"cursor","pointer","width","40px","height","40px","margin-left","10px",3,"click"],["class","preview-image","style","margin-top: 10px;",4,"ngIf"],[1,"image","localisation"],["type","file","id","file","accept","image/*,.pdf","multiple","",2,"display","none"],["src","/assets/icons/location.svg","alt","Upload","slot","end",2,"cursor","pointer","width","40px","height","40px",3,"click"],[1,"btn-validate"],["type","submit","color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled"],["name","bubbles",4,"ngIf"],[3,"value"],["type","text",3,"value","ionChange"],["searchInput",""],["class","autocomplete-list",4,"ngIf"],[1,"autocomplete-list"],["button","",3,"click",4,"ngFor","ngForOf"],["button","",3,"click"],["formControlName","neighborhood","type","text"],[1,"preview-image",2,"margin-top","10px"],["alt","Preview",2,"max-width","100px","max-height","100px","border-radius","8px","border","1px solid #ccc",3,"src"],[2,"font-size","12px","color","#666"],["name","bubbles"]],template:function(n,o){1&n&&(e.DNE(0,w,1,0,"app-progress-spinner",0),e.j41(1,"ion-header",1)(2,"ion-toolbar",2)(3,"ion-img",3),e.bIt("click",function(){return o.back()}),e.k0s(),e.j41(4,"ion-title"),e.EFF(5),e.nI1(6,"translate"),e.k0s()(),e.j41(7,"div",4)(8,"ion-button",5),e.nrm(9,"ion-icon",6),e.k0s(),e.j41(10,"ion-button",5),e.nrm(11,"ion-icon",7),e.k0s()()(),e.j41(12,"ion-content",8)(13,"form",9),e.bIt("ngSubmit",function(){return o.onSubmit()}),e.j41(14,"ion-item")(15,"ion-label",10),e.EFF(16),e.nI1(17,"translate"),e.k0s(),e.nrm(18,"ion-input",11),e.k0s(),e.j41(19,"ion-item")(20,"ion-label",10),e.EFF(21),e.nI1(22,"translate"),e.k0s(),e.nrm(23,"ion-input",12),e.k0s(),e.j41(24,"ion-item")(25,"ion-label",10),e.EFF(26),e.nI1(27,"translate"),e.k0s(),e.nrm(28,"ion-input",13),e.k0s(),e.j41(29,"ion-item")(30,"ion-label",10),e.EFF(31),e.nI1(32,"translate"),e.k0s(),e.j41(33,"ion-select",14,15),e.bIt("ionChange",function(g){return o.getRegion(g)}),e.nI1(35,"translate"),e.DNE(36,k,2,2,"ion-select-option",16),e.k0s()(),e.j41(37,"ion-item")(38,"ion-label",10),e.EFF(39),e.nI1(40,"translate"),e.k0s(),e.j41(41,"ion-select",17,15),e.bIt("ionChange",function(g){return o.getcities(g)}),e.nI1(43,"translate"),e.DNE(44,j,2,2,"ion-select-option",16),e.k0s()(),e.DNE(45,G,7,5,"ion-item",0),e.DNE(46,B,5,3,"ion-item",0),e.j41(47,"ion-item")(48,"ion-label",10),e.EFF(49),e.nI1(50,"translate"),e.k0s(),e.nrm(51,"ion-input",18),e.k0s(),e.j41(52,"ion-item")(53,"ion-label",10),e.EFF(54),e.nI1(55,"translate"),e.k0s(),e.j41(56,"ion-select",19),e.DNE(57,W,2,2,"ion-select-option",16),e.k0s()(),e.j41(58,"ion-item")(59,"ion-label",10),e.EFF(60),e.nI1(61,"translate"),e.k0s(),e.j41(62,"ion-select",20),e.DNE(63,Y,2,2,"ion-select-option",16),e.k0s()(),e.j41(64,"div",21)(65,"div",22)(66,"div",23)(67,"input",24),e.bIt("change",function(g){return o.setAttachment(g)}),e.k0s(),e.nrm(68,"img",25),e.j41(69,"img",26),e.bIt("click",function(){return o.takePicture()}),e.k0s()(),e.DNE(70,K,4,2,"div",27),e.k0s(),e.j41(71,"div",28)(72,"ion-label",10),e.EFF(73),e.nI1(74,"translate"),e.k0s()()(),e.j41(75,"div",21)(76,"div",22)(77,"div",23),e.nrm(78,"input",29),e.j41(79,"img",30),e.bIt("click",function(){return o.getLocation()}),e.k0s()()(),e.j41(80,"div",28)(81,"ion-label",10),e.EFF(82),e.nI1(83,"translate"),e.k0s(),e.DNE(84,z,3,1,"div",0),e.DNE(85,V,3,1,"div",0),e.k0s()(),e.j41(86,"div",31)(87,"ion-button",32)(88,"ion-label"),e.EFF(89),e.nI1(90,"translate"),e.k0s(),e.DNE(91,J,1,0,"ion-spinner",33),e.k0s()()()()),2&n&&(e.Y8G("ngIf",o.isLoading),e.R7$(1),e.Y8G("translucent",!0),e.R7$(4),e.JRh(e.bMT(6,30,"indirect-clients.title")),e.R7$(7),e.Y8G("fullscreen",!0),e.R7$(1),e.Y8G("formGroup",o.form),e.R7$(3),e.JRh(e.bMT(17,32,"indirect-clients.name")),e.R7$(5),e.JRh(e.bMT(22,34,"indirect-clients.business-name")),e.R7$(5),e.JRh(e.bMT(27,36,"indirect-clients.phone")),e.R7$(5),e.JRh(e.bMT(32,38,"indirect-clients.reseller-new-page.first-step.select-region-label")),e.R7$(2),e.FS9("cancelText",e.bMT(35,40,"indirect-clients.button.cancel")),e.R7$(3),e.Y8G("ngForOf",o.regions),e.R7$(3),e.JRh(e.bMT(40,42,"indirect-clients.city")),e.R7$(2),e.FS9("cancelText",e.bMT(43,44,"indirect-clients.button.cancel")),e.R7$(3),e.Y8G("ngForOf",o.cities),e.R7$(1),e.Y8G("ngIf",!o.isManualInput),e.R7$(1),e.Y8G("ngIf",o.isManualInput),e.R7$(3),e.JRh(e.bMT(50,46,"indirect-clients.location")),e.R7$(5),e.JRh(e.bMT(55,48,"indirect-clients.Type-client")),e.R7$(3),e.Y8G("ngForOf",o.particualrCategories),e.R7$(3),e.JRh(e.bMT(61,50,"indirect-clients.vendors")),e.R7$(3),e.Y8G("ngForOf",o.wholeSales),e.R7$(7),e.Y8G("ngIf",o.attachment.file),e.R7$(3),e.Lme("",e.bMT(74,52,"indirect-clients.images"),": ",o.attachment.name||(null==o.file?null:o.file.name)," "),e.R7$(9),e.SpI("",e.bMT(83,54,"indirect-clients.location"),":"),e.R7$(2),e.Y8G("ngIf",o.localisation),e.R7$(1),e.Y8G("ngIf",o.localisation),e.R7$(2),e.Y8G("disabled",!o.form.valid),e.R7$(2),e.JRh(e.bMT(90,56,"indirect-clients.save")),e.R7$(2),e.Y8G("ngIf",o.isLoading))},dependencies:[b.Sq,b.bT,m.qT,m.BC,m.cb,h.Jm,h.W9,h.eU,h.iq,h.KW,h.$w,h.uz,h.he,h.nf,h.Nm,h.Ip,h.w2,h.BC,h.ai,h.su,h.Je,h.Gw,S._,m.j4,m.JD,R.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{display:flex;padding-top:13px;align-items:center}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700!important;margin:auto}ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res));color:#0b305c}.header[_ngcontent-%COMP%]{--background: #f1f2f4;--border-color: transparent;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.buttons[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent !important;--box-shadow: none;color:#0b305c;border:none;--padding: auto}.buttons[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#419cfb}ion-content[_ngcontent-%COMP%]{--background: #f1f2f4}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{width:calc(100% - 48px);margin:2em auto auto;padding:auto;--background: #f1f2f4}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:1rem;color:#0b305c;font-family:var(--mont-regular)}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0px;margin-bottom:calc(41 * var(--res));--background: $color-nineteen;--ripple-color: transparent;--background-activated: transparent;--background-activated-opacity: transparent;--background-focused: transparent;--background-focused-opacity: transparent;--background-hover: transparent;--background-hover-opacity: transparent}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:var(--mont-regular);color:#0b305c;font-size:1rem}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#fff}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .autocomplete-list[_ngcontent-%COMP%]{width:100%;background:#f1f2f4}ion-text[_ngcontent-%COMP%]{font-size:1rem}.submit[_ngcontent-%COMP%]{width:calc(100% - 48px);margin-left:24px}.add[_ngcontent-%COMP%]{font-size:.6rem;font-weight:700;--color: #0b305c;--background: #419cfb2b;--padding: 5px 10px;--border-radius: 30px;margin:1rem auto}.iconImage[_ngcontent-%COMP%]{display:flex;width:calc(100% - 48px);margin-bottom:1rem}.iconImage[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{border:1.12px solid #d5dfeb;border-radius:6px;padding:.5rem}.iconImage[_ngcontent-%COMP%]   .image.localisation[_ngcontent-%COMP%]{position:relative}.iconImage[_ngcontent-%COMP%]   .image.localisation[_ngcontent-%COMP%]   .has-image[_ngcontent-%COMP%]{color:var(--ion-color-primary);text-decoration:underline;cursor:pointer}.iconImage[_ngcontent-%COMP%]   .image.localisation[_ngcontent-%COMP%]   .preview-container[_ngcontent-%COMP%]{position:fixed;inset:0;background:rgba(0,0,0,.8);z-index:999;display:flex;justify-content:center;align-items:center}.iconImage[_ngcontent-%COMP%]   .image.localisation[_ngcontent-%COMP%]   .preview-container[_ngcontent-%COMP%]   .preview-image[_ngcontent-%COMP%]{max-width:90%;max-height:90vh;object-fit:contain}.iconImage[_ngcontent-%COMP%]   .localisation[_ngcontent-%COMP%]{margin-left:.2rem}"]})}}return i})()}];let Q=(()=>{class i{static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275mod=e.$C({type:i})}static{this.\u0275inj=e.G2t({imports:[P.iI.forChild(X),P.iI]})}}return i})();var H=a(93887);let Z=(()=>{class i{static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275mod=e.$C({type:i})}static{this.\u0275inj=e.G2t({imports:[b.MD,m.YN,h.bv,H.G,R.h,m.X1,Q]})}}return i})()},43556:(L,I,a)=>{a.d(I,{B:()=>v});var b=a(73308),m=a(94934),h=a(45312),P=a(26409),e=(a(99987),a(2978)),E=a(33607),U=a(82571),y=a(14599),x=a(74657);let v=(()=>{class d{constructor(u,c,r,p,C){this.baseUrl=u,this.http=c,this.commonSrv=r,this.storageSrv=p,this.translateService=C,this.base_url=`${this.baseUrl.getOrigin()}${h.c.basePath}`,this.base_url+="companies"}create(u){var c=this;return(0,b.A)(function*(){try{return delete u._id,yield(0,m.s)(c.http.post(c.base_url,u))}catch(r){return c.commonSrv.getError("Echec de cr\xe9ation de la compagnie",r)}})()}getCompanies(u){var c=this;return(0,b.A)(function*(){try{let r=new P.Nl;const{category:p,city:C,limit:O,name:M,regionCom:T,solToId:S,tel:R,users:w,offset:k,enable:j=!0,projection:D,isLoyaltyProgDistributor:$}=u;return void 0!==p&&(r=r.append("category",p)),C&&(r=r.append("address.city",C)),M&&(r=r.append("name",M)),S&&(r=r.append("erpSoldToId",S)),R&&(r=r.append("tel",`${R}`)),D&&(r=r.append("projection",`${D}`)),w&&(r=r.append("users",`${w}`)),T&&(r=r.append("address.commercialRegion",T)),$&&(r=r.append("isLoyaltyProgDistributor",$)),void 0!==O&&(r=r.append("limit",O)),void 0!==k&&(r=r.append("offset",k)),r=r.set("enable",j),yield(0,m.s)(c.http.get(c.base_url,{params:r}))}catch(r){const C={message:c.commonSrv.getError("",r).message,color:"danger"};return yield c.commonSrv.showToast(C),r}})()}getParticularCompanies(u){var c=this;return(0,b.A)(function*(){let r=new P.Nl;const{limit:p,offset:C,enable:O=!0,commercialRegion:M}=u;return void 0!==p&&(r=r.append("limit",p)),void 0!==C&&(r=r.append("offset",C)),M&&(r=r.append("address.commercialRegion",M)),r=r.set("enable",O),yield(0,m.s)(c.http.get(c.base_url+"/particular-suppliers",{params:r}))})()}find(u){var c=this;return(0,b.A)(function*(){try{return yield(0,m.s)(c.http.get(c.base_url+"/"+u))}catch{return c.commonSrv.initCompany()}})()}getBalance(u){var c=this;return(0,b.A)(function*(){try{let r=new P.Nl;const{company:p}=c.storageSrv.getUserConnected();return r=r.set("_id",p?p?._id:u?.companyId),yield(0,m.s)(c.http.get(`${c.base_url}/balance`,{params:r}))}catch(r){return yield c.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de votre solde",color:"danger"}),r}})()}getUsersCompany(u,c){var r=this;return(0,b.A)(function*(){try{let p=new P.Nl;const{email:C,enable:O=!0}=c;return C&&(p=p.append("email",C)),p=p.append("enable",O),yield(0,m.s)(r.http.get(`${r.base_url}/${u}/users`,{params:p}))}catch(p){return yield r.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de vos informations",color:"danger"}),p}})()}static{this.\u0275fac=function(c){return new(c||d)(e.KVO(E.K),e.KVO(P.Qq),e.KVO(U.h),e.KVO(y.n),e.KVO(x.c$))}}static{this.\u0275prov=e.jDH({token:d,factory:d.\u0275fac,providedIn:"root"})}}return d})()},28935:(L,I,a)=>{a.d(I,{G:()=>U});var b=a(73308),m=a(26409),h=a(45312),P=a(56610),_=a(2978),e=a(82571),E=a(33607);let U=(()=>{class y{constructor(v,d,s){this.commonSrv=v,this.baseUrlService=d,this.http=s,this.url="",this.getWholeSaleBoolean=!1,this.url=this.baseUrlService.getOrigin()+h.c.basePath}createWholeSale(v){var d=this;return(0,b.A)(function*(){try{const s=yield d.http.post(`${d.url}whole-sale`,v).toPromise();return d.commonSrv.showToast({color:"success",message:"Demi gros cre\xe9 avec succ\xe8s"}),s}catch(s){const c={message:d.commonSrv.getError("",s).message,color:"danger"};return yield d.commonSrv.showToast(c),s}})()}getWholeSale(v){var d=this;return(0,b.A)(function*(){try{let s=new m.Nl;const{offset:u,limit:c,startDate:r,endDate:p,tel:C,name:O,commercialRegion:M,animateDonutId:T}=v;return r&&p&&(s=s.append("startDate",new P.vh("fr").transform(r,"YYYY-MM-dd")),s=s.append("endDate",new P.vh("fr").transform(p,"YYYY-MM-dd"))),u&&(s=s.append("offset",u)),c&&(s=s.append("limit",c)),O&&(s=s.append("firstName",O)),M&&(s=s.append("address.commercialRegion",M)),T&&(s=s.append("associatedDonutAnimator._id",T)),C&&(s=s.append("tel",C)),s=s.append("enable",!0),yield d.http.get(`${d.url}whole-sale`,{params:s}).toPromise()}catch(s){const c={message:d.commonSrv.getError("",s).message,color:"danger"};return yield d.commonSrv.showToast(c),s}})()}find(v){var d=this;return(0,b.A)(function*(){try{return yield d.http.get(`${d.url}whole-sale/${v}`).toPromise()}catch(s){const c={message:d.commonSrv.getError("",s).message,color:"danger"};return yield d.commonSrv.showToast(c),null}})()}updateWholeSale(v){var d=this;return(0,b.A)(function*(){try{const s=yield d.http.patch(`${d.url}whole-sale/${v._id}`,v).toPromise();return d.commonSrv.showToast({color:"success",message:"Demi gros modifi\xe9 avec succ\xe8s"}),s}catch(s){const c={message:d.commonSrv.getError("",s).message,color:"danger"};return yield d.commonSrv.showToast(c),s}})()}static{this.\u0275fac=function(d){return new(d||y)(_.KVO(e.h),_.KVO(E.K),_.KVO(m.Qq))}}static{this.\u0275prov=_.jDH({token:y,factory:y.\u0275fac,providedIn:"root"})}}return y})()}}]);