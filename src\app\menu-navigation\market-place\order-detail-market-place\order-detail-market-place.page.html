<ion-header>
  <ion-toolbar class="header">
    <ion-img (click)="back()" slot="start" src="/assets/icons/arrow-back.svg"></ion-img>
    <ion-title class="title">
      {{ "order.detail.reference" | translate | capitalize}}
      <span class="color-primary">
        {{ marketService?.order?.appReference }}
      </span>
    </ion-title>
    <ion-buttons slot="end" *ngIf="commonSrv.user.category !== userCategory.Commercial && commonSrv.user.category !== userCategory.Particular">
      <ion-button (click)="presentPopover($event)">
        <ion-icon name="ellipsis-vertical"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div id="container" class="order-history-page">
    <div class="dialogRef" class="scroller-container historic-bill-detail-container containers">
      <div class="order-detail">
        <ion-title class="h3-title ">{{ "order.detail.title" | translate | capitalize}}</ion-title>
        <div class="bill-info">
          <div class="right-block">
            <ion-label class="title">{{ "order.detail.reference" | translate | capitalize}} : </ion-label>
            <ion-label class="title">{{ "order.detail.created-at" | translate | capitalize}} :</ion-label>
            <ion-label class="title">{{ "order.detail.name" | translate | capitalize}} :</ion-label>
            <ion-label class="title">{{ "order.detail.email" | translate | capitalize}} :</ion-label>
            <ion-label class="title" *ngIf="marketService?.order?.validation?.date">
              {{ "order.detail.validated-at" | translate | capitalize}} :</ion-label>
            <ion-label class="title" *ngIf="marketService?.order?.appReference">
              {{ "history-page.ref" | translate | capitalize}} :</ion-label>
          </div>
          <div class="left-block">
            <ion-label class="value">{{marketService?.order?.appReference}}</ion-label>
            <ion-label class="value">{{marketService?.order?.created_at | date: 'dd/MM/YY'}} &nbsp;
              <span class="value">{{("preposition.to" | translate | capitalize) + (marketService?.order?.created_at |
                date:
                'HH:mm:ss')}}</span>
            </ion-label>
            <ion-label class="value">{{marketService?.order?.user?.firstName || '' | capitalize}} </ion-label>
            <ion-label class="value">{{marketService?.order?.user?.email || 'N/A' | truncateString: 18 | capitalize
              }}</ion-label>
            <ion-label *ngIf="marketService?.order?.validation?.date" class="value">
              {{marketService?.order?.validation?.date| date: 'dd/MM/YY'}}
            </ion-label>
            <ion-label *ngIf="marketService?.order?.appReference" class="value">
              {{marketService?.order?.appReference | capitalize}}
            </ion-label>
          </div>
        </div>
      </div>
      <div *ngIf="false">
        <ion-title class="h3-title ">{{ "order.detail.product-list" | translate}}</ion-title>
        <div class=" list-elt-header ">
          <div class="col col-desc "></div>
          <div class="col col-qdt title">Quantité</div>
          <div class="col col-price title">PU</div>
          <div class="col col-amount title">Montant</div>
        </div>
        <div class="list-elt-contain">
          <div class=" list-elt " *ngIf="marketService?.order?.cart?.items">
            <div class="col col-desc ">
              <div class="col-desc-elt">
                <ion-img src="../../../assets/images/cimencam.png"></ion-img>
              </div>
            </div>
            <div class="col col-qdt ">
              {{marketService?.order?.cart?.quantity}}
              {{marketService?.order?.cart?.items?.name}}
            </div>
            <div class="col col-price ">
              {{marketService?.order?.cart?.items.price | number:''}} POINTS
            </div>
            <div class="col col-amount ">
              {{(marketService?.order?.cart?.items?.price) * marketService?.order?.cart?.quantity | number:'' }}
              XAF</div>
          </div>
        </div>
      </div>

      <ul class="product-list">
        <li class="list-elt head">
          <ion-label class="col"><strong>{{"order-new-page.third-step.product" | translate}}</strong></ion-label>
          <ion-label class="col"><strong>{{"order-new-page.third-step.qte" | translate}}</strong></ion-label>
          <ion-label class="col"><strong>{{"order-new-page.third-step.unitPrice"
              | translate}}</strong></ion-label>
        </li>

        <div *ngFor="let item of items; trackBy: trackByFn">
          <li class="list-elt">
            <div class="col product">
              <ion-label>{{item?.name || item?.category?.label || 'N\A' | capitalize}}</ion-label>
              <ion-label>{{item?.name || item?.category?.label || 'N\A'}}</ion-label>
            </div>
            <ion-label class="col">{{item?.quantity || marketService?.order?.cart?.quantity  || "N\A"}}</ion-label>
            <ion-label class="col">{{item?.price || item?.unitPrice | number:'':'fr'}} XAF</ion-label>
          </li>
          <li class="list-elt line"></li>
        </div>
      </ul>

      <!-- <div class="order-summary" *ngIf="marketService?.order?.rejectReason">
        <ion-title class="h3-title">{{ "order.detail.motif-rejet" | translate}} </ion-title>
        <div class="bill-info">
          <ion-label class="value reason">{{marketService?.order?.rejectReason}}</ion-label>
        </div>
      </div>  -->

      <div class="flex-dir">
        <!--validate btn for DRH-->
        <ion-button class="btn--meduim btn--upper" color="primary" (click)="showModalConfirmValidation()"
          *ngIf="!isValidate && [orderStatus?.CREDIT_IN_VALIDATION].includes(marketService?.order?.status)
          && commonSrv?.user?.employeeType === employeeType.DRH && commonSrv?.user?.authorizations.includes(orderAction.VALIDATE)" expand="block">
          <ion-label> VALIDER</ion-label>
        </ion-button>

        <!--validate btn for coordo-RH-->
        <ion-button class="btn--meduim btn--upper" color="primary" (click)="showModalConfirmValidation()"
          *ngIf="!isValidate && [orderStatus.CREATED].includes(marketService?.order?.status)
           && commonSrv?.user?.employeeType === employeeType.CORDO_RH && commonSrv?.user?.authorizations.includes(orderAction.VALIDATE)" expand="block">
          <ion-label> VALIDER</ion-label>
        </ion-button>

        <!--reject btn for DRH-->
        <ion-button class="btn--meduim btn--upper" color="danger" (click)="showModalRejectOrder()"
          *ngIf="!isReject && [orderStatus?.CREDIT_IN_VALIDATION].includes(marketService?.order?.status)
          && commonSrv?.user?.employeeType === employeeType.DRH && commonSrv?.user?.authorizations.includes(orderAction.VALIDATE)" expand="block">
          <ion-label> REJETER</ion-label>
        </ion-button>

        <!--reject btn for coordo-RH-->
        <ion-button class="btn--meduim btn--upper" color="danger" (click)="showModalRejectOrder()"
          *ngIf="!isReject && [orderStatus?.CREATED].includes(marketService?.order?.status)
          && commonSrv?.user?.employeeType === employeeType.CORDO_RH && commonSrv?.user?.authorizations.includes(orderAction.VALIDATE)" expand="block">
          <ion-label> REJETER</ion-label>
        </ion-button>
      </div>
      <!-- <div class="btn-validate"
        *ngIf="marketService?.order?.status != orderStatus.VALIDATED && commonSrv.user.category !== userCategory.Commercial">
        <ion-button class="btn--meduim btn--upper" color="primary" expand="block" routerLink="order-update"> -->
      <!-- <ion-spinner name="bubbles" *ngIf="isLoading"></ion-spinner> -->
      <!-- <ion-label *ngIf="!isLoading" class="green-btn"> MODIFIER LA COMMANDE </ion-label>
        </ion-button>
      </div> -->
      <div class="btn-validate" *ngIf="marketService?.order?.status === orderStatus.CREDIT_IN_AWAIT_VALIDATION 
        && commonSrv.user.category === userCategory.Commercial" (click)="showModalConfirmValidation()">
        <ion-button class="btn--meduim btn--upper" color="primary" expand="block">
          <ion-label *ngIf="!isLoading" class="green-btn"> {{'VALIDER LA COMMANDE'}} </ion-label>
        </ion-button>
      </div>
      <!-- <div class="btn-validate" *ngIf="commonSrv.user.category !== userCategory.CompanyUser">
        <ion-button class="btn--meduim btn--upper" color="medium" expand="block"
          (click)="generatePurchaseOrder(marketService?.order?._id)">
          <ion-spinner name="bubbles" *ngIf="isLoading"></ion-spinner>
          <ion-label *ngIf="!isLoading" class="green-btn"> {{"reseller-new-page.detail.download-order" | translate}}
          </ion-label>
        </ion-button>
      </div> -->
    </div>
  </div>
</ion-content>