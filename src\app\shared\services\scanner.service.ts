import { inject, Injectable } from '@angular/core';
import { BarcodeScanner } from '@awesome-cordova-plugins/barcode-scanner/ngx';
import { CommonService } from './common.service';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpParams } from '@angular/common/http';
import { BaseUrlService } from './base-url.service';
import { StorageService } from './storage.service';
import { ToastModel } from '../models/toast.model';
import { lastValueFrom } from 'rxjs';
import { requestResponse } from '../types';
import { OrderSupplier } from '../models/order-particular';
import { DatePipe } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class ScannerService {
  currDisplay: boolean;
  base_url: string;

  commonSrv = inject(CommonService);
  private http = inject(HttpClient);
  private baseUrl = inject(BaseUrlService);
  private storageSrv = inject(StorageService);
  private barcodeScanner = inject(BarcodeScanner);

  constructor() {
    this.base_url = `${this.baseUrl.getOrigin()}${environment.basePath}`;
  }

  async validateScanData(dataScan: OrderSupplier) {
    try {
      return await lastValueFrom(
        this.http.post<requestResponse>(`${this.base_url}scanner-data`, dataScan)
      );
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error);
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      };
      await this.commonSrv.showToast(toastMessage);
      return error;
    }
  }

  // async checkPermission(): Promise<boolean> {
  //   try {
  //     // check or request permission
  //     const { camera } = await BarcodeScanner.requestPermissions();
  //     return camera === 'granted';
  //   } catch (e) {
  //     console.log(e);
  //     return false;
  //   }
  // }

  async stopScan() {
    this.currDisplay = false;
    document.querySelector('body').classList.remove('scanner-active');
    // Avec le nouveau scanner ML Kit, aucune méthode spécifique n'est nécessaire pour arrêter
    // car il utilise un modal natif qui se ferme automatiquement
  }

  private showContent() {
    // Show all content again
    const elements = document.querySelectorAll('.hide-on-scan');
    elements.forEach(element => {
      (element as HTMLElement).style.display = '';
    });

    document.querySelector('body').classList.remove('scanner-active');
    // Le nouveau scanner ne nécessite pas de gestion manuelle du fond
  }

  async prepareScanner() {
    // Le nouveau scanner ML Kit utilise un modal natif, donc nous n'avons pas besoin
    // de préparer l'interface utilisateur de la même manière
    document.body.classList.add('scanner-active');
  }

  async startScan(): Promise<string | undefined> {

    try {
      const result = await this.barcodeScanner.scan({
        showTorchButton: true,
        prompt: 'Placez le QR code dans le cadre',
        resultDisplayDuration: 1000,
        formats: 'QR_CODE,PDF_417,CODE_128',
        orientation: 'portrait',
      });

      if (!result.cancelled) {
        console.log('📦 Code scanné :', result.text);
        return result.text;
      } else {
        console.log('🚫 Scan annulé');
      }
    } catch (error) {
      console.error('❌ Erreur scan BarcodeScanner:', error);
      await this.commonSrv.showToast({
        color: 'danger',
        message: 'Erreur lors du scan (BarcodeScanner)',
      });
    }
  }

  private restoreUI() {
    // Show all content again
    // document.querySelector('ion-content')['style']['opacity'] = '1';
    // document.querySelector('ion-header')['style']['opacity'] = '1';
    // document.querySelector('ion-footer')['style']['opacity'] = '1';
    // document.querySelectorAll('ion-fab').forEach(fab => {
    //   (fab as HTMLElement)['style']['opacity'] = '1';
    // });

    // Remove scanner-active class
    document.body.classList.remove('scanner-active');

    // Le nouveau scanner ML Kit n'a pas besoin d'être arrêté manuellement
    // car il s'exécute dans un modal natif
  }

  async getVolumeOrderByParticularClient(param: any): Promise<any> {

    let params = new HttpParams();
    const { status = 300, offset, limit, enable = true, associatedCommercialId, startDate, endDate, customerName } = param;
    (offset !== undefined) && (params = params.append('offset', offset));
    limit && (params = params.append('limit', limit));
    status && (params = params.append('status', status));

    (associatedCommercialId) && (params = params.append('user.associatedCommercial._id', associatedCommercialId));
    params = params.append('enable', enable);

    if (startDate && endDate) {
      params = params.append('startDate', new DatePipe('fr').transform(startDate, 'YYYY-MM-dd'));
      params = params.append('endDate', new DatePipe('fr').transform(endDate, 'YYYY-MM-dd'));
    }
    if (customerName) {
      params = params.append('user.firstName', customerName);
    }

    try {
      const response = await lastValueFrom(
        this.http.get<any>(`${this.base_url}scanner-data/volume-order-by-particular-client`, { params })
      );
      return response;
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error);
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      };
      await this.commonSrv.showToast(toastMessage);
      return error;
    }
  }

}





