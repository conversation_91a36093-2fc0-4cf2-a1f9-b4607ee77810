import { inject, Injectable } from '@angular/core';
import { BarcodeScanner } from '@capacitor-mlkit/barcode-scanning';
import { CommonService } from './common.service';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpParams } from '@angular/common/http';
import { BaseUrlService } from './base-url.service';
import { StorageService } from './storage.service';
import { ToastModel } from '../models/toast.model';
import { lastValueFrom } from 'rxjs';
import { requestResponse } from '../types';
import { OrderSupplier } from '../models/order-particular';
import { DatePipe } from '@angular/common';
import { TranslateConfigService } from './translate-config.service';

@Injectable({
  providedIn: 'root'
})
export class ScannerService {
  currDisplay: boolean;
  base_url: string;
  private isModuleInstalled: boolean = false;

  commonSrv = inject(CommonService);
  private http = inject(HttpClient);
  private baseUrl = inject(BaseUrlService);
  private storageSrv = inject(StorageService)
  private translateService = inject(TranslateConfigService);
  constructor() {
    this.base_url = `${this.baseUrl.getOrigin()}${environment.basePath}`;
  }

  get isFrench(): boolean {
    return this.translateService.currentLang === 'fr';
  }


  /**
   * Vérifie si le module Google Barcode Scanner est disponible
   */
  async isBarcodeScannerAvailable(): Promise<boolean> {
    try {
      const result = await BarcodeScanner.isGoogleBarcodeScannerModuleAvailable();
      return result.available;
    } catch (error) {
      console.warn('⚠️ Impossible de vérifier la disponibilité du scanner:', error);
      return false;
    }
  }

  /**
   * Initialise le module Google Barcode Scanner
   * À appeler au démarrage de l'application
   */
  async initializeBarcodeScanner(): Promise<boolean> {
    try {
      console.log('🔧 Initialisation du module Google Barcode Scanner...');

      // Vérifier si le module est déjà installé
      const isAvailable = await BarcodeScanner.isGoogleBarcodeScannerModuleAvailable();

      if (!isAvailable.available) {
        console.log('📦 Installation du module Google Barcode Scanner...');
        await BarcodeScanner.installGoogleBarcodeScannerModule();
        console.log('✅ Module Google Barcode Scanner installé avec succès');

        // Afficher un toast de succès
        await this.commonSrv.showToast({
          color: 'success',
          message: this.isFrench
            ? 'Scanner initialisé avec succès'
            : 'Scanner successfully initialized',
        });
      } else {
        console.log('✅ Module Google Barcode Scanner déjà disponible');
      }

      this.isModuleInstalled = true;
      return true;

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation du scanner:', error);

      await this.commonSrv.showToast({
        color: 'warning',
        message: this.isFrench
          ? 'Une erreur est survenue. Initialisation en cours...'
          : 'An error occurred. Initialization in progress...',
      });

      this.isModuleInstalled = false;
      return false;
    }
  }

  async validateScanData(dataScan: OrderSupplier) {
    try {
      return await lastValueFrom(
        this.http.post<requestResponse>(`${this.base_url}scanner-data`, dataScan)
      );
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error);
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      };
      await this.commonSrv.showToast(toastMessage);
      return error;
    }
  }

  async checkPermission(): Promise<boolean> {
    try {
      // check or request permission
      const { camera } = await BarcodeScanner.requestPermissions();
      return camera === 'granted';
    } catch (e) {
      console.log(e);
      return false;
    }
  }

  async stopScan() {
    this.currDisplay = false;
    document.querySelector('body').classList.remove('scanner-active');
    // Avec le nouveau scanner ML Kit, aucune méthode spécifique n'est nécessaire pour arrêter
    // car il utilise un modal natif qui se ferme automatiquement
  }



  async prepareScanner() {
    // Le nouveau scanner ML Kit utilise un modal natif, donc nous n'avons pas besoin
    // de préparer l'interface utilisateur de la même manière
    document.body.classList.add('scanner-active');
  }

  async startScan(): Promise<string | undefined> {
    try {
      // Vérifier les permissions
      const permission = await this.checkPermission();
      if (!permission) {
        await this.commonSrv.showToast({
          color: 'danger',
          message: this.isFrench
            ? 'Permission refusée pour utiliser la caméra'
            : 'Permission denied to use the camera',
        });
        return;
      }

      // S’assurer que le module est installé
      if (!this.isModuleInstalled) {
        console.log('🔄 Module non installé, vérification et initialisation...');
        const isAvailable = await this.isBarcodeScannerAvailable();

        if (!isAvailable) {
          console.log('📦 Installation du module en cours...');
          const initialized = await this.initializeBarcodeScanner();
          if (!initialized) {
            console.log("⚠️ Tentative de scan malgré l'erreur d'initialisation");
          }
        } else {
          this.isModuleInstalled = true;
          console.log('✅ Module déjà disponible');
        }
      }

      await this.prepareScanner();

      console.log('📱 Démarrage du scan...');
      const { barcodes } = await BarcodeScanner.scan();

      this.restoreUI();

      if (barcodes && barcodes.length > 0) {
        console.log('✅ Code scanné avec succès:', barcodes[0].displayValue);
        return barcodes[0].displayValue;
      } else {
        await this.commonSrv.showToast({
          color: 'warning',
          message: this.isFrench
            ? 'Aucun code-barres détecté'
            : 'No barcode detected',
        });
        return undefined;
      }
    } catch (error: any) {
      console.error('❌ Erreur lors du scan:', error);

      if (error?.message?.includes('Google Barcode Scanner Module is not available')) {
        console.log("🔄 Tentative d'installation du module...");
        try {
          await this.initializeBarcodeScanner();
          await this.commonSrv.showToast({
            color: 'primary',
            message: this.isFrench
              ? 'Scanner initialisé. Veuillez réessayer.'
              : 'Scanner initialized. Please try again.',
          });
        } catch (installError) {
          await this.commonSrv.showToast({
            color: 'danger',
            message: this.isFrench
              ? "Erreur d'initialisation du scanner"
              : 'Scanner initialization error',
          });
        }
      } else {
        await this.commonSrv.showToast({
          color: 'danger',
          message: this.isFrench
            ? 'Erreur lors du scan'
            : 'Error during scanning',
        });
      }

      return undefined;
    } finally {
      this.stopScan();
    }
  }


  private restoreUI() {
    // Show all content again
    // document.querySelector('ion-content')['style']['opacity'] = '1';
    // document.querySelector('ion-header')['style']['opacity'] = '1';
    // document.querySelector('ion-footer')['style']['opacity'] = '1';
    // document.querySelectorAll('ion-fab').forEach(fab => {
    //   (fab as HTMLElement)['style']['opacity'] = '1';
    // });

    // Remove scanner-active class
    document.body.classList.remove('scanner-active');

    // Le nouveau scanner ML Kit n'a pas besoin d'être arrêté manuellement
    // car il s'exécute dans un modal natif
  }

  async getVolumeOrderByParticularClient(param: any): Promise<any> {

    let params = new HttpParams();
    const { status = 300, offset, limit, enable = true, associatedCommercialId, startDate, endDate, customerName } = param;
    (offset !== undefined) && (params = params.append('offset', offset));
    limit && (params = params.append('limit', limit));
    status && (params = params.append('status', status));

    (associatedCommercialId) && (params = params.append('user.associatedCommercial._id', associatedCommercialId));
    params = params.append('enable', enable);

    if (startDate && endDate) {
      params = params.append('startDate', new DatePipe('fr').transform(startDate, 'YYYY-MM-dd'));
      params = params.append('endDate', new DatePipe('fr').transform(endDate, 'YYYY-MM-dd'));
    }
    if (customerName) {
      params = params.append('user.firstName', customerName);
    }

    try {
      const response = await lastValueFrom(
        this.http.get<any>(`${this.base_url}scanner-data/volume-order-by-particular-client`, { params })
      );
      return response;
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error);
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      };
      await this.commonSrv.showToast(toastMessage);
      return error;
    }
  }

}





