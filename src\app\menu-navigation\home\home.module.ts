import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { HomePageRoutingModule } from './home-routing.module';

import { HomePage } from './home.page';
import { TranslateModule } from '@ngx-translate/core';
import { RedirectToSabitouComponent } from './redirect-to-sabitou/redirect-to-sabitou.component';
import { VersionService } from 'src/app/shared/services/version.service';
// import { AppVersion } from '@ionic-native/app-version/ngx';
import { SharedModule } from "../../shared/shared.module";
import { OnboardingComponent } from './anboarding/anboarding.component';
import { MultipleAccountComponent } from './multiple-account/multiple-account.component';
import { BarcodeScanner } from '@capacitor-mlkit/barcode-scanning';
@NgModule({
    declarations: [HomePage, RedirectToSabitouComponent, OnboardingComponent, MultipleAccountComponent],
    providers: [
        VersionService
    ],
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        HomePageRoutingModule,
        TranslateModule,
        SharedModule
    ]
})
export class HomePageModule { }
