"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2457],{42457:(x,g,o)=>{o.r(g),o.d(g,{ListOrderPageModule:()=>y});var l=o(56610),u=o(37222),d=o(74657),r=o(77897),a=o(77575),s=o(58133),p=o(88233),m=function(t){return t.CREATE="create_oder_supplier",t.UPDATE="update_oder_supplier",t.DELETE="delete_oder_supplier",t.VIEW="view_oder_supplier",t}(m||{}),O=o(838),n=o(2978),C=o(82571);const P=function(){return["/order/history"]};function M(t,k){1&t&&(n.j41(0,"a",7),n.nrm(1,"ion-img",11),n.j41(2,"ion-label"),n.<PERSON><PERSON>(3),n.nI1(4,"translate"),n.k0s(),n.nrm(5,"ion-icon",10),n.k0s()),2&t&&(n.Y8G("routerLink",n.lJ4(5,P)),n.R7$(1),n.Y8G("src","/assets/images/entreprise.png"),n.R7$(2),n.SpI(" ",n.bMT(4,3,"list-order.direct")," "))}const b=function(){return["/order/validate-order"]};function h(t,k){1&t&&(n.j41(0,"a",7),n.nrm(1,"ion-img",11),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.nrm(5,"ion-icon",10),n.k0s()),2&t&&(n.Y8G("routerLink",n.lJ4(5,b)),n.R7$(1),n.Y8G("src","/assets/images/man.png"),n.R7$(2),n.SpI(" ",n.bMT(4,3,"list-order.indirect")," "))}const v=function(){return["/order/validate-client-order-via-qrcode"]},_=[{path:"",component:(()=>{class t{constructor(c,e){this.commonSrv=c,this.router=e,this.userCategory=s.s,this.orderAction=p.T3,this.supplierAction=m,this.qrCodeAction=O.LB,c.handleBackButton()}ngOnInit(){this.commonSrv.showNav=!0}back(){[s.s.DonutAnimator,s.s.Particular].includes(this.commonSrv.user?.category)?this.router.navigate(["/navigation/home-alt"]):this.router.navigate(["/navigation/home"])}static{this.\u0275fac=function(e){return new(e||t)(n.rXU(C.h),n.rXU(a.Ix))}}static{this.\u0275cmp=n.VBU({type:t,selectors:[["app-list-order"]],decls:16,vars:9,consts:[[1,"header"],["slot","start",1,"back-button",3,"click"],["src","/assets/icons/arrow-back.svg"],[1,"title"],[3,"fullscreen"],[1,"container"],["class","item",3,"routerLink",4,"ngIf"],[1,"item",3,"routerLink"],[1,"order-icon-qrcode",3,"src"],[1,"title-qr-code"],["slot","end","name","chevron-forward"],[1,"order-icon",3,"src"]],template:function(e,i){1&e&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"button",1),n.bIt("click",function(){return i.back()}),n.nrm(3,"ion-img",2),n.k0s(),n.j41(4,"ion-title",3),n.EFF(5),n.nI1(6,"translate"),n.k0s()()(),n.j41(7,"ion-content",4)(8,"div",5),n.DNE(9,M,6,6,"a",6),n.j41(10,"a",7),n.nrm(11,"ion-img",8),n.j41(12,"ion-label",9),n.EFF(13," Validate Order "),n.k0s(),n.nrm(14,"ion-icon",10),n.k0s(),n.DNE(15,h,6,6,"a",6),n.k0s()()),2&e&&(n.R7$(5),n.JRh(n.bMT(6,6,"list-order.title")),n.R7$(2),n.Y8G("fullscreen",!0),n.R7$(2),n.Y8G("ngIf",i.commonSrv.user.category!==i.userCategory.DonutAnimator||i.commonSrv.user.authorizations.includes(i.orderAction.VIEW)),n.R7$(1),n.Y8G("routerLink",n.lJ4(8,v)),n.R7$(1),n.Y8G("src","/assets/images/qr-scanner.png"),n.R7$(4),n.Y8G("ngIf",i.commonSrv.user.category===i.userCategory.DonutAnimator||i.commonSrv.user.authorizations.includes(i.supplierAction.VIEW)))},dependencies:[l.bT,r.W9,r.eU,r.iq,r.KW,r.he,r.BC,r.ai,r.oY,a.Wk,d.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-button[_ngcontent-%COMP%]{margin:1em 0}ion-title[_ngcontent-%COMP%]{color:var(--ion-color-primary);font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700!important;margin:1em 0}.header[_ngcontent-%COMP%]{--background: --ion-color-light-tint;--border-color: transparent;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{background-color:transparent}.header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]{--background: --ion-color-light-tint}ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]{background:--ion-color-medium-contrast;border-radius:5px;box-shadow:0 4px 4px #00000017;width:calc(100% - 48px);margin:1em;padding:1em;display:flex;justify-content:space-between}ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-semibold);text-align:center;margin:auto}ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .order-icon-qrcode[_ngcontent-%COMP%]{width:15%}ion-content[_ngcontent-%COMP%]   ion-avatar[_ngcontent-%COMP%]{width:50px;height:50px}ion-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:1rem;color:--ion-color-primary;font-weight:700}ion-content[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{padding:8px;border-radius:50px;border:1px solid var(--clr-primary-750)}ion-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none}"]})}}return t})()}];let f=(()=>{class t{static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[a.iI.forChild(_),a.iI]})}}return t})(),y=(()=>{class t{static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[l.MD,d.h,u.YN,r.bv,f]})}}return t})()}}]);