import { Component, HostListener, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Company } from 'src/app/shared/models/company.model';
import { BaseUser } from 'src/app/shared/models/user.models';
import { CommonService } from 'src/app/shared/services/common.service';
import { StorageService } from 'src/app/shared/services/storage.service';
import { CompanyService } from '../../services/company.service';
import { PackagingService } from '../../services/packaging.service';
import { PricesService } from '../../services/prices.service';
import { Location } from '@angular/common';
import { ProductService } from '../../services/product.service';
import { FormControl, FormGroup } from '@angular/forms';
import { ToastModel } from 'src/app/shared/models/toast.model';
import { TranslateConfigService } from 'src/app/shared/services/translate-config.service';
import { Language } from 'src/app/shared/enum/language.enum';
import { UserCategory } from 'src/app/shared/enum/user-category.enum';
import { WholeSaleService } from 'src/app/shared/services/whole-sale.service';
import { commercialRegions } from 'src/app/shared/mocks/mocks';

@Component({
  selector: 'app-choice-suppliers',
  templateUrl: './choice-suppliers.page.html',
  styleUrls: ['./choice-suppliers.page.scss'],
})
export class ChoiceSuppliersPage implements OnInit {
  isLoading: boolean = true;
  suppliers: any[];
  selectedCompany: Company;
  user: BaseUser;
  isSelectedCompany: boolean = false;
  loyaltyProgramDistributor: Company[];
  filteredSuppliers: Company[] = [];
  selectedSupplier: Company;
  isEdit: boolean;
  showSuggestions: boolean = false;

  offset = 0;
  limit = 20;
  skeletons = [1, 2, 3, 4, 5, 6];
  filterData = {
    name: '',
  };

  otherSupplier = {
    id: "otherId",
    name: "Autre grossiste"
  }

  filterForm: FormGroup = new FormGroup({
    name: new FormControl(''),
  });


  initData: {
    _id?: string;
    label?: string;
    packaging: { label?: string; _id: string }[];
    address: { region: string, city: string, commercialRegions: string }[];
    storeRef: string;
  }[];

  constructor(
    private router: Router,
    private location: Location,
    private storageSrv: StorageService,
    public commonService: CommonService,
    protected companySrv: CompanyService,
    protected priceSrv: PricesService,
    public packagingSrv: PackagingService,
    private productSrv: ProductService,
    private translateService: TranslateConfigService,
    private wholeSaleService: WholeSaleService
  ) {
    this.user = this.storageSrv.getUserConnected();
  }

  async ngOnInit() {
    this.isLoading = true;

    try {
      const promises = [
        this.getDistributorLoyalityProgram(),
        this.getSuppliers()
      ];

      if (this.user.category !== UserCategory.Commercial) {
        promises.push(this.getStore());
      }

      await Promise.all(promises);
    } catch (error) {
      console.error('Error initializing component:', error);
    } finally {
      this.isLoading = false;
    }
  }

  async getSuppliers() {
    this.skeletons = [1, 2, 3, 4, 5, 6];
    this.isLoading = true;
    const query = {
      ... this.filterData,
      offset: this.offset,
      limit: this.limit,
    };

    // const animateDonutId = this.commonService?.user?.associatedCommercial?._id;
    // console.log('user', this.commonService?.user);

    const getWholeSale = await this.wholeSaleService.getWholeSale({
      ...query, commercialRegion: this.commonService.user?.address?.commercialRegion,
    });
    const associatedSupplier = this.commonService.user?.associatedSuppliers;
    if (associatedSupplier && Array.isArray(associatedSupplier) && associatedSupplier.length > 0) {

      this.suppliers = associatedSupplier;
    }
    else {
      const res = await this.companySrv.getParticularCompanies(query);
      this.suppliers = res.data;
      this.suppliers.unshift(this.otherSupplier);
      this.offset = this.offset + this.limit;
    }

    this.suppliers.unshift(...getWholeSale.data);
    console.log('suppliers', this.suppliers, associatedSupplier, getWholeSale.data);


    this.isLoading = false;
    this.skeletons = [];
  }

  async getDistributorLoyalityProgram() {
    this.isLoading = true;
    const query = {
      ... this.filterData,
      offset: this.offset,
      limit: this.limit,
      isLoyaltyProgDistributor: true
    };

    const res = await this.companySrv.getCompanies(query);
    this.loyaltyProgramDistributor = res.data;
    this.filteredSuppliers = [];
    this.offset = this.offset + this.limit;
    this.isLoading = false;
  }

  selectCompany(company: Company) {
    this.selectedCompany = company;
    this.isSelectedCompany = true;
  }

  async ionViewWillEnter(): Promise<void> {
  }

  async getStore(): Promise<void> {
    this.isLoading = true;
    const query = {
      companyId: this.companySrv.selectedCompanyForSalesOrderProcess?._id
    }
    const res = await this.priceSrv.getStores(query);
    if (res) {
      this.initData = res;
      this.storageSrv.store('stores', JSON.stringify(res));
    }
    this.isLoading = false;
  }

  async nextStep(company: any) {
    const manualInput = this.filterForm.value.name?.trim(); // Récupère la saisie manuelle

    // Crée une nouvelle compagnie si une saisie manuelle est faite et différente de l'option sélectionnée
    if (manualInput && (!company || company.name !== manualInput)) {
      company = {
        name: manualInput,
        address: {
          district: this.commonService.user.address.district,
          city: this.commonService.user.address.city,
          region: this.commonService.user.address.region,
          commercialRegion: this.commonService.getCommercialRegion(this.commonService.user.address.region),
        },
        tel: null,
      };

      try {
        this.wholeSaleService.createWholeSale(company);
      } catch (error) {
        const toast: ToastModel = {
          message: error.error?.message || 'Error creating new company',
          color: 'danger',
        };
        this.commonService.showToast(toast);
      }

    }

    // Si aucune compagnie n'est sélectionnée ou saisie, utiliser "Autres grossiste"
    if (!company) {
      company = { ...this.otherSupplier };
    }

    const cart = {
      company: company._id,
      items: [],
      amount: null,
    };

    await this.storageSrv.store('cart', JSON.stringify(cart));
    await this.storageSrv.store('supplier', JSON.stringify(company));
    this.router.navigate(['order/recap-scan']);
    this.productSrv.currentDataProductScan = [];
  }

  filterSuppliers(event: any) {
    const query = (event.target as HTMLInputElement).value.toLowerCase();
    this.filterForm.patchValue({ name: query }); // Met à jour le formulaire
    if (query.trim() === '') {
      this.showSuggestions = false;
    } else {
      this.filteredSuppliers = this.loyaltyProgramDistributor.filter(supplier =>
        supplier.name.toLowerCase().includes(query)
      );
      this.showSuggestions = true;
    }
  }

  selectSupplier(supplier: Company) {
    this.selectedSupplier = supplier;
    this.filterForm.patchValue({ name: supplier.name }); // Met à jour l'input avec le nom sélectionné
    this.filteredSuppliers = [];
    this.showSuggestions = false;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    this.showSuggestions = false;
  }

  back() {
    this.location.back();
  }

}
