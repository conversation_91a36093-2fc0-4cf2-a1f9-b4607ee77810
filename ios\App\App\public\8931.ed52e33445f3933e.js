"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8931],{98931:(L,b,o)=>{o.r(b),o.d(b,{EmployeesOrdersPageModule:()=>w});var a=o(77897),h=o(56610),O=o(74657),d=o(37222),p=o(77575),M=o(73308),D=o(35025),P=o.n(D),k=o(26409),C=o(99987),F=o(44444),f=o(18080),m=o(58133),_=o(88233),s=o(79898),t=o(2978),e=o(82571),i=o(81559),l=o(39316),u=o(14599),S=o(62049),E=o(2611),T=o(94440);function R(c,j){1&c&&t.nrm(0,"ion-progress-bar",16)}const v=function(c){return{active:c}};function A(c,j){if(1&c){const n=t.RV6();t.j41(0,"ion-tab-button",11),t.bIt("click",function(){t.eBV(n);const r=t.XpG();return r.filterForm.status=r.orderStatus.CREATED,r.orders=[],t.Njj(r.getOrders())}),t.j41(1,"ion-title"),t.EFF(2),t.nI1(3,"translate"),t.k0s()()}if(2&c){const n=t.XpG();t.Y8G("ngClass",t.eq3(4,v,n.filterForm.status===n.orderStatus.CREATED)),t.R7$(2),t.JRh(t.bMT(3,2,"history-page.tabs.in-progres"))}}function $(c,j){if(1&c){const n=t.RV6();t.j41(0,"ion-tab-button",11),t.bIt("click",function(){t.eBV(n);const r=t.XpG();return r.filterForm.status=r.orderStatus.CREDIT_IN_VALIDATION,r.orders=[],t.Njj(r.getOrders())}),t.j41(1,"ion-title"),t.EFF(2),t.nI1(3,"translate"),t.k0s()()}if(2&c){const n=t.XpG();t.Y8G("ngClass",t.eq3(4,v,n.filterForm.status===n.orderStatus.CREDIT_IN_VALIDATION)),t.R7$(2),t.JRh(t.bMT(3,2,"history-page.tabs.in-progres"))}}function U(c,j){if(1&c){const n=t.RV6();t.j41(0,"ion-tab-button",11),t.bIt("click",function(){t.eBV(n);const r=t.XpG();return r.filterForm.status=r.orderStatus.CREDIT_IN_VALIDATION,r.orders=[],t.Njj(r.getOrders())}),t.j41(1,"ion-title"),t.EFF(2),t.nI1(3,"translate"),t.k0s()()}if(2&c){const n=t.XpG();t.Y8G("ngClass",t.eq3(4,v,n.filterForm.status===n.orderStatus.CREDIT_IN_VALIDATION)),t.R7$(2),t.JRh(t.bMT(3,2,"history-page.tabs.prevalidate"))}}function x(c,j){if(1&c){const n=t.RV6();t.j41(0,"ion-card",17),t.bIt("click",function(){const y=t.eBV(n).$implicit,I=t.XpG();return t.Njj(I.orderService.order=y)}),t.j41(1,"ion-card-content")(2,"div",18)(3,"ion-label"),t.EFF(4),t.nI1(5,"translate"),t.j41(6,"strong"),t.EFF(7),t.k0s()(),t.j41(8,"ion-label"),t.EFF(9),t.nI1(10,"translate"),t.j41(11,"strong"),t.EFF(12),t.k0s()(),t.j41(13,"ion-label"),t.EFF(14),t.nI1(15,"translate"),t.j41(16,"strong"),t.EFF(17),t.nI1(18,"number"),t.k0s()(),t.j41(19,"ion-label"),t.EFF(20,"Date: "),t.j41(21,"strong"),t.EFF(22),t.nI1(23,"date"),t.k0s()(),t.j41(24,"ion-label"),t.EFF(25,"Statut: "),t.j41(26,"strong",19),t.nI1(27,"colorStatusOrder"),t.EFF(28),t.nI1(29,"statusOrder"),t.k0s()()(),t.j41(30,"div",20),t.nrm(31,"ion-icon",21),t.k0s()()()}if(2&c){const n=j.$implicit;t.Mz_("routerLink","/order/detail/",null==n?null:n._id,""),t.R7$(4),t.SpI("",t.bMT(5,10,"history-page.employee-name"),": "),t.R7$(3),t.JRh((null==n||null==n.user?null:n.user.firstName)+" "+(null==n||null==n.user?null:n.user.lastName)||"N/A"),t.R7$(2),t.SpI("",t.bMT(10,12,"history-page.reference"),": "),t.R7$(3),t.JRh((null==n?null:n.customerReference)||(null==n?null:n.appReference)||"N/A"),t.R7$(2),t.SpI("",t.bMT(15,14,"history-page.amount"),": "),t.R7$(3),t.SpI("",t.bMT(18,16,null==n||null==n.cart||null==n.cart.amount?null:n.cart.amount.TTC)," FCFA"),t.R7$(5),t.JRh(t.i5U(23,18,null==n?null:n.created_at,"dd/MM/YYYY \xe0 HH:mm")),t.R7$(4),t.Y8G("ngClass",t.bMT(27,21,n.status)),t.R7$(2),t.JRh(t.bMT(29,23,null==n?null:n.status))}}function B(c,j){1&c&&(t.j41(0,"div",22),t.nrm(1,"ion-img",23),t.j41(2,"ion-label"),t.EFF(3),t.nI1(4,"translate"),t.k0s()()),2&c&&(t.R7$(3),t.SpI(" ",t.bMT(4,1,"history-page.empty-order")," "))}const N=[{path:"",component:(()=>{class c{constructor(n,g,r,y,I,Y,G){this.location=n,this.commonSrv=g,this.orderService=r,this.modalCtrl=y,this.productService=I,this.storageService=Y,this.translateService=G,this.isLoading=!1,this.tabOption=_.Re.PAID,this.orderStatus=_.Re,this.employeeType=F.PB,this.orders=[],this.skeletons=[1,2,3,4,5,6],this.filterForm={status:this.commonSrv?.user?.employeeType===F.PB.DRH?_.Re.CREDIT_IN_VALIDATION:_.Re.CREATED,date:{start:P()().startOf("year"),end:P()().endOf("year")},userCategory:m.s.EmployeeLapasta,paymentMode:f.I.CREDIT,customer:"",product:"",validation:null,appReference:"",enable:!0},this.offset=0,this.limit=20}ionViewWillEnter(){var n=this;return(0,M.A)(function*(){n.isLoading=!0,n.storageService.getUserConnected(),n.orders=[],yield n.getOrders()})()}getOrderByUser(){var n=this;return(0,M.A)(function*(){n.skeletons=[1,2,3,4,5,6],n.isLoading=!0;const g={status:n.tabOption,limit:n.limit,offset:n.offset,...n.filterData},r=(yield n.orderService.getAllOrder(g)).data;n.orders=n.orders.concat(r),n.isLoading=!1,n.skeletons=[]})()}getOrders(){var n=this;return(0,M.A)(function*(){if(n.isLoading=!0,(n.filterForm.date.start||n.filterForm.date.end)&&n.filterForm.date.start>n.filterForm.date.end)return yield n.commonSrv.showToast({message:n.translateService.currentLang===C.T.French?"Incorrect time interval !!":"Intervale de temps incorrect !!",color:"warning"}),n.isLoading=!1;let g={...n.filterForm,limit:n.limit,offset:n.offset};n.commonSrv.user.employeeType===F.PB.CORDO_RH&&n.filterForm.status===_.Re.CREDIT_IN_VALIDATION&&(g.validation=_.Cs.CORDO_RH),n.commonSrv.user.employeeType===F.PB.CORDO_RH&&n.filterForm.status===_.Re.PAID&&(g.validation=_.Cs.DRH);const r=yield n.orderService.getOrders(g);return r instanceof k.yz?(yield n.commonSrv.showToast({message:n.translateService.currentLang===C.T.French?`One occurred.You do not have the authorization.${r?.message}`:`Une erreur est survenue.${r?.message}`,color:"warning"}),n.isLoading=!1):(n.orders=r?.data,n.orders.forEach(y=>y?.cart?.items.filter(Y=>Y.quantity)),n.isLoading=!1)})()}doRefresh(n){var g=this;return(0,M.A)(function*(){g.filterData=null,g.orders=[],yield g.getOrders(),n.target.complete()})()}getFlowOrder(n){var g=this;return(0,M.A)(function*(){g.offset=g.offset+g.limit+1,yield g.getOrders(),n.target.complete()})()}showFilter(){var n=this;return(0,M.A)(function*(){const g=yield n.modalCtrl.create({component:s.W,initialBreakpoint:.6,cssClass:"modal",breakpoints:[0,.6,.7],mode:"ios",componentProps:{filterData:n.filterData}});g.present();const r=(yield g.onWillDismiss()).data;r&&(n.filterForm.date.start=r?.startDate,n.filterForm.date.end=r?.endDate,n.filterForm.appReference=r?.customerReference,n.orders=[],yield n.getOrders())})()}back(){this.location.back()}static{this.\u0275fac=function(g){return new(g||c)(t.rXU(h.aZ),t.rXU(e.h),t.rXU(i.Q),t.rXU(a.W3),t.rXU(l.b),t.rXU(u.n),t.rXU(S.E))}}static{this.\u0275cmp=t.VBU({type:c,selectors:[["app-employees-orders"]],decls:28,vars:25,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],["slot","end","src","/assets/icons/funnel-outline.svg",3,"click"],[3,"fullscreen"],["type","indeterminate",4,"ngIf"],["id","container"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],["slot","top",1,"tab-container","ion-no-border"],[3,"ngClass","click",4,"ngIf"],[3,"ngClass","click"],[1,"order-list"],["class","order",3,"routerLink","click",4,"ngFor","ngForOf"],[3,"ionInfinite"],["class","empty-list",4,"ngIf"],["type","indeterminate"],[1,"order",3,"routerLink","click"],[1,"detail"],[3,"ngClass"],[1,"icon"],["src","/assets/icons/arrow-forward-green.svg"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"]],template:function(g,r){1&g&&(t.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),t.bIt("click",function(){return r.back()}),t.k0s(),t.j41(3,"ion-title",2),t.EFF(4),t.nI1(5,"truncateString"),t.nI1(6,"translate"),t.k0s(),t.j41(7,"ion-img",3),t.bIt("click",function(){return r.showFilter()}),t.k0s()()(),t.j41(8,"ion-content",4),t.DNE(9,R,1,0,"ion-progress-bar",5),t.j41(10,"div",6)(11,"ion-refresher",7),t.bIt("ionRefresh",function(I){return r.doRefresh(I)}),t.nrm(12,"ion-refresher-content",8),t.nI1(13,"translate"),t.nI1(14,"translate"),t.k0s(),t.j41(15,"ion-tab-bar",9),t.DNE(16,A,4,6,"ion-tab-button",10),t.DNE(17,$,4,6,"ion-tab-button",10),t.DNE(18,U,4,6,"ion-tab-button",10),t.j41(19,"ion-tab-button",11),t.bIt("click",function(){return r.filterForm.status=r.orderStatus.PAID,r.orders=[],r.getOrders()}),t.j41(20,"ion-title"),t.EFF(21),t.nI1(22,"translate"),t.k0s()()(),t.j41(23,"div",12),t.DNE(24,x,32,25,"ion-card",13),t.j41(25,"ion-infinite-scroll",14),t.bIt("ionInfinite",function(I){return r.getFlowOrder(I)}),t.nrm(26,"ion-infinite-scroll-content"),t.k0s()(),t.DNE(27,B,5,3,"div",15),t.k0s()()),2&g&&(t.R7$(4),t.JRh(t.i5U(5,12,t.bMT(6,15,"all-orders-page.employees-orders"),25)),t.R7$(4),t.Y8G("fullscreen",!0),t.R7$(1),t.Y8G("ngIf",r.isLoading),t.R7$(3),t.FS9("pullingText",t.bMT(13,17,"refresher.pull")),t.Mz_("refreshingText","",t.bMT(14,19,"refresher.refreshing"),"..."),t.R7$(4),t.Y8G("ngIf",(null==r.commonSrv||null==r.commonSrv.user?null:r.commonSrv.user.employeeType)===r.employeeType.CORDO_RH),t.R7$(1),t.Y8G("ngIf",(null==r.commonSrv||null==r.commonSrv.user?null:r.commonSrv.user.employeeType)===r.employeeType.DRH),t.R7$(1),t.Y8G("ngIf",(null==r.commonSrv||null==r.commonSrv.user?null:r.commonSrv.user.employeeType)===r.employeeType.CORDO_RH),t.R7$(1),t.Y8G("ngClass",t.eq3(23,v,r.filterForm.status===r.orderStatus.PAID)),t.R7$(2),t.JRh(t.bMT(22,21,"history-page.tabs.validate")),t.R7$(3),t.Y8G("ngForOf",r.orders),t.R7$(3),t.Y8G("ngIf",(null==r.orders?null:r.orders.length)<=0&&!r.isLoading))},dependencies:[h.YU,h.Sq,h.bT,a.b_,a.I9,a.W9,a.eU,a.iq,a.KW,a.Ax,a.Hp,a.he,a.FH,a.To,a.Ki,a.Jq,a.qW,a.BC,a.ai,a.N7,p.Wk,h.QX,h.vh,E.D3,T.c,E.qZ,O.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:calc(41 * var(--res));--border-color: transparent;--background: transparent;background:#fff;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding-top:0}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]{margin:auto;min-height:40px;margin-bottom:calc(50 * var(--res));border:none}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:calc(45 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#143c5d;border-bottom:3px solid #143c5d}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#143c5d}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block;line-height:initial;margin-top:calc(15.7 * var(--res));font-family:Mont Light;color:#000;font-size:calc(36 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-primary-400[_ngcontent-%COMP%]{color:#0d7d3d!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-success-200[_ngcontent-%COMP%]{background-color:#b8ddb6!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-info-500[_ngcontent-%COMP%]{color:#0af!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-info-100[_ngcontent-%COMP%]{background-color:#cef!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-default-400[_ngcontent-%COMP%]{color:#fff!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-info-500[_ngcontent-%COMP%]{background-color:#0af!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-tertiary-200[_ngcontent-%COMP%]{background-color:#f0efef!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:2rem;height:2rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]{height:4em;width:100%;margin-bottom:1rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:100%;height:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{margin-top:20vh;display:flex;align-items:center;flex-direction:column;justify-content:center;height:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}ion-infinite-scroll-content[_ngcontent-%COMP%]{min-height:15px}"]})}}return c})()}];let K=(()=>{class c{static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275mod=t.$C({type:c})}static{this.\u0275inj=t.G2t({imports:[p.iI.forChild(N),p.iI]})}}return c})();var W=o(93887);let w=(()=>{class c{static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275mod=t.$C({type:c})}static{this.\u0275inj=t.G2t({imports:[h.MD,d.YN,a.bv,W.G,O.h,d.X1,K]})}}return c})()},81559:(L,b,o)=>{o.d(b,{Q:()=>f});var a=o(73308),h=o(35025),O=o.n(h),d=o(94934),p=o(56610),M=o(45312),D=o(26409),P=o(2978),k=o(82571),C=o(33607),F=o(14599);let f=(()=>{class m{constructor(s,t,e,i){this.http=s,this.commonSrv=t,this.baseUrlService=e,this.storageSrv=i,this.url=this.baseUrlService.getOrigin()+M.c.basePath}create(s){var t=this;return(0,a.A)(function*(){try{return yield(0,d.s)(t.http.post(`${t.url}orders`,s))}catch(e){const l={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(l),e}})()}createOrderByCommercialForClient(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,d.s)(e.http.post(`${e.url}orders/${t}`,s))}catch(i){const u={message:e.commonSrv.getError("",i).message,color:"danger"};return yield e.commonSrv.showToast(u),i}})()}getAllOrder(s){var t=this;return(0,a.A)(function*(){try{let e=new D.Nl;const{num:i,commercialId:l,status:u,offset:S,limit:E,startDate:T,endDate:R,customerReference:v,selectedCompanyId:A}=s;return T&&R&&(e=e.append("startDate",new p.vh("fr").transform(T,"YYYY-MM-dd"))),R&&T&&(e=e.append("endDate",new p.vh("fr").transform(R,"YYYY-MM-dd"))),v&&(e=e.append("appReference",v)),A&&(e=e.append("selectedCompanyId",A)),l&&(e=e.append("commercial",l)),void 0!==S&&(e=e.append("offset",S)),E&&(e=e.append("limit",E)),u&&(e=e.append("status",u)),i&&(e=e.append("appReference",i)),yield(0,d.s)(t.http.get(`${t.url}orders/history`,{params:e}))}catch(e){const l={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(l),e}})()}getOrders(s){var t=this;return(0,a.A)(function*(){try{let e=new D.Nl;const{status:i,appReference:l,offset:u,limit:S,userCategory:E,paymentMode:T,validation:R,customer:v,product:A,date:$,enable:U=!0}=s;return u&&(e=e.append("offset",u)),S&&(e=e.append("limit",S)),i&&(e=e.append("status",i)),l&&(e=e.append("appReference",`${l}`)),T&&(e=e.append("payment.mode.id",T)),E&&(e=e.append("user.category",E)),v&&(e=e.append("user.email",v)),A&&(e=e.append("cart.items.product.label",A)),R&&(e=e.append("validation",R)),$.start&&$.end&&(e=e.append("startDate",O()($.start).format("YYYY-MM-DD")),e=e.append("endDate",O()($.end).format("YYYY-MM-DD"))),e=e.append("enable",U),yield(0,d.s)(t.http.get(`${t.url}orders`,{params:e}))}catch(e){return e}})()}updateOrders(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,d.s)(e.http.patch(`${e.url}orders/${s}`,t))}catch(i){const u={message:e.commonSrv.getError("",i).message,color:"danger"};return yield e.commonSrv.showToast(u),i}})()}RhValidatedOrder(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,d.s)(e.http.patch(`${e.url}orders/${s._id}/validate`,t))}catch(i){const u={message:e.commonSrv.getError("",i).message,color:"danger"};return yield e.commonSrv.showToast(u),i}})()}RhRejectOrder(s){var t=this;return(0,a.A)(function*(){try{return yield(0,d.s)(t.http.patch(`${t.url}orders/${s._id}/reject`,{}))}catch(e){const l={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(l),e}})()}sendOtp(s){var t=this;return(0,a.A)(function*(){try{return yield(0,d.s)(t.http.post(`${t.url}callback/afriland`,s))}catch(e){const l={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(l),e}})()}sendWallet(s){var t=this;return(0,a.A)(function*(){try{return yield(0,d.s)(t.http.post(`${t.url}orders/verify-Wallet-Nber`,s))}catch(e){const l={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(l),e}})()}ubaPayment(s){var t=this;return(0,a.A)(function*(){try{return yield(0,d.s)(t.http.post(`${t.url}orders/m2u-paymentRequest`,s))}catch(e){const l={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(l),e}})()}find(s){var t=this;return(0,a.A)(function*(){try{return yield(0,d.s)(t.http.get(t.url+"orders/"+s))}catch(e){const l={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(l),e}})()}getCardToken(){var s=this;return(0,a.A)(function*(){try{return yield(0,d.s)(s.http.post(`${s.url}orders/order-generate-visa-key`,{}))}catch(t){const i={message:s.commonSrv.getError("",t).message,color:"danger"};return yield s.commonSrv.showToast(i),t}})()}setupPayerAuthentication(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,d.s)(e.http.post(`${e.url}orders/order-setup-payer-auth`,{transientTokenJwt:s,order:t}))}catch(i){const u={message:e.commonSrv.getError("",i).message,color:"danger"};return yield e.commonSrv.showToast(u),i}})()}authorizationWithPAEnroll(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,d.s)(e.http.post(`${e.url}orders/order-authorization-pay-enroll`,{order:s,options:t}))}catch(i){const u={message:e.commonSrv.getError("",i).message,color:"danger"};return yield e.commonSrv.showToast(u),i}})()}checkIfOrderExist(s){var t=this;return(0,a.A)(function*(){try{return yield(0,d.s)(t.http.get(`${t.url}orders/${s}/exist`))}catch(e){const l={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(l),e}})()}generatePurchaseOrder(s){var t=this;return(0,a.A)(function*(){try{return yield(0,d.s)(t.http.get(`${t.url}orders/${s}/generate-purchase`))}catch(e){const l={message:t.commonSrv.getError("",e).message,color:"danger"};return yield t.commonSrv.showToast(l),e}})()}cancellationOrder(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,d.s)(e.http.patch(`${e.url}orders/${s}/cancellation-order`,t))}catch(i){const u={message:e.commonSrv.getError("",i).message,color:"danger"};return yield e.commonSrv.showToast(u),i}})()}updateCarrier(s,t){var e=this;return(0,a.A)(function*(){try{return yield(0,d.s)(e.http.patch(`${e.url}orders/${s}/add-carrier`,{carrier:t}))}catch(i){const u={message:e.commonSrv.getError("",i).message,color:"danger"};return yield e.commonSrv.showToast(u),i}})()}static{this.\u0275fac=function(t){return new(t||m)(P.KVO(D.Qq),P.KVO(k.h),P.KVO(C.K),P.KVO(F.n))}}static{this.\u0275prov=P.jDH({token:m,factory:m.\u0275fac,providedIn:"root"})}}return m})()},39316:(L,b,o)=>{o.d(b,{b:()=>k});var a=o(73308),h=o(26409),O=o(94934),d=o(45312),p=o(2978),M=o(82571),D=o(33607),P=o(77897);let k=(()=>{class C{constructor(f,m,_,s){this.http=f,this.commonSrv=m,this.baseUrlService=_,this.toastController=s,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+d.c.basePath+"products"}getProducts(f){var m=this;return(0,a.A)(function*(){try{let _=new h.Nl;return f?.limit&&(_=_.append("limit",f?.limit)),yield(0,O.s)(m.http.get(m.url,{params:_}))}catch(_){const t={message:m.commonSrv.getError("",_).message,color:"danger"};return yield m.commonSrv.showToast(t),_}})()}getProduct(f){var m=this;return(0,a.A)(function*(){try{return yield(0,O.s)(m.http.get(`${m.url}/${f}`))}catch(_){const t={message:m.commonSrv.getError("",_).message,color:"danger"};return yield m.commonSrv.showToast(t),_}})()}static{this.\u0275fac=function(m){return new(m||C)(p.KVO(h.Qq),p.KVO(M.h),p.KVO(D.K),p.KVO(P.K_))}}static{this.\u0275prov=p.jDH({token:C,factory:C.\u0275fac,providedIn:"root"})}}return C})()},94440:(L,b,o)=>{o.d(b,{c:()=>h});var a=o(2978);let h=(()=>{class O{transform(p,...M){return p?p.length>M[0]?`${p.substring(0,M[0]-3)}...`:p:""}static{this.\u0275fac=function(M){return new(M||O)}}static{this.\u0275pipe=a.EJ8({name:"truncateString",type:O,pure:!0})}}return O})()}}]);