import { Location } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { WholeSale } from 'src/app/shared/models/whole-sale';
import { CommonService } from 'src/app/shared/services/common.service';
import { WholeSaleService } from 'src/app/shared/services/whole-sale.service';
import { commercialRegions } from 'src/app/shared/mocks/mocks';
import { Language } from 'src/app/shared/enum/language.enum';


@Component({
  selector: 'app-create-whole-sale',
  templateUrl: './create-whole-sale.page.html',
  styleUrls: ['./create-whole-sale.page.scss'],
})
export class CreateWholeSalePage implements OnInit {

  commonSrv = inject(CommonService);
  translateSrv = inject(TranslateService);
  fb = inject(FormBuilder);
  location = inject(Location);
  wholeService = inject(WholeSaleService);
  route = inject(Router);

  form!: FormGroup;
  isLoading: boolean;
  cities: any[];
  regions: any[];
  wholeSale: WholeSale;

  constructor() { }

  ngOnInit() {
    this.wholeSale = this.wholeService.wholeSaleDetail;
    this.initForm();
    this.regions = this.commonSrv.getRegions();
    if (this.wholeSale) {
      this.patchForm(this.wholeSale);
    }
  }

  async onSubmit() {
    // Validation du formulaire
    if (this.form.invalid) {
      await this.commonSrv.showToast({
        color: 'warning',
        message: this.translateSrv.currentLang === Language.English
          ? 'Please fill in all required fields'
          : 'Veuillez remplir tous les champs obligatoires'
      });
      return;
    }

    this.isLoading = true;
    try {
      if (this.wholeSale) {

        this.wholeSale = {
          _id: this.wholeSale._id,
          name: this.form.value.name ?? this.wholeSale.name,
          tel: this.form.value.tel ?? this.form.value.tel,
          address: {
            district: this.form.value.district ?? this.wholeSale.address?.district,
            city: this.form.value.city ?? this.wholeSale.address?.city,
            region: this.form.value.region ?? this.wholeSale.address?.region,
            commercialRegion: this.getCommercialRegion(this.form.value.region) ?? this.wholeSale.address?.commercialRegion,
          },
          associatedDonutAnimator: this.wholeSale.associatedDonutAnimator,
        }

        const response = await this.wholeService.updateWholeSale(this.wholeSale);

        if (response?.status === 200 || response?.status === 201) {
          await this.commonSrv.showToast({
            color: 'success',
            message: this.translateSrv.currentLang === Language.English
              ? 'Wholesale user updated successfully'
              : 'Utilisateur grossiste modifié avec succès'
          });

          this.route.navigateByUrl('/navigation/indirect-user');
          this.wholeService.wholeSaleDetail = null;
          this.form.reset();
        } else {
          await this.commonSrv.showToast({
            color: 'danger',
            message: this.translateSrv.currentLang === Language.English
              ? 'Error updating wholesale user'
              : 'Erreur lors de la modification de l\'utilisateur grossiste'
          });
        }

      }

      else {
        const wholeSale = {
          name: this.form.value.name,
          address: {
            district: this.form.value.district,
            city: this.form.value.city,
            region: this.form.value.region,
            commercialRegion: this.getCommercialRegion(this.form.value.region),
          },
          tel: this.form.value.tel === "" ? null : this.form.value.tel,
        };

        if (wholeSale.tel && !this.commonSrv.verifyPhoneNumber(wholeSale.tel)) {
          return this.commonSrv.showToast({
            color: 'danger',
            message: this.translateSrv.currentLang === Language.English
              ? 'Phone nu;ber is not correct' : 'Le numéro de téléphone est incorrect'
          })
        }


        const response = await this.wholeService.createWholeSale(wholeSale);
        if (response?.status == 201) {
          await this.commonSrv.showToast({
            color: 'success',
            message: this.translateSrv.currentLang === Language.English
              ? 'Wholesale user created successfully'
              : 'Utilisateur grossiste créé avec succès'
          });

          this.wholeService.getWholeSaleBoolean = true;
          this.route.navigateByUrl('/navigation/indirect-user');
          this.form.reset();
        } else {
          await this.commonSrv.showToast({
            color: 'danger',
            message: this.translateSrv.currentLang === Language.English
              ? 'Error creating wholesale user'
              : 'Erreur lors de la création de l\'utilisateur grossiste'
          });
        }
      }

    } catch (error) {
      console.error('Erreur lors de l\'opération:', error);
      await this.commonSrv.showToast({
        color: 'danger',
        message: this.translateSrv.currentLang === Language.English
          ? 'An error occurred. Please try again.'
          : 'Une erreur s\'est produite. Veuillez réessayer.'
      });
    } finally {
      this.isLoading = false;
    }
  }

  initForm() {
    this.form = this.fb.group({
      name: ['', Validators.required],
      tel: [''],
      region: ['', Validators.required],
      city: ['', Validators.required],
      district: [''],
    });
  }


  getCommercialRegion(region: string): string | null {
    const data = commercialRegions
    const normalizedRegion = region.trim().toLowerCase();

    for (const [key, regions] of Object.entries(data)) {
      console.log('Checking key:', key, 'with regions:', regions);
      if (regions.some(r => r.trim().toLowerCase() === normalizedRegion)) {
        console.log('Match found:', key);
        return key;
      }
    }

    console.log('No match found');
    return null;
  }
  patchForm(wholeSale: WholeSale) {
    this.form.patchValue({
      name: wholeSale.name,
      tel: wholeSale.tel ?? null,
      region: wholeSale.address?.region,
      city: wholeSale.address?.city,
      district: wholeSale.address?.district,
    });
  }

  async getRegion($event: any) {
    const selectedRegion = $event.detail.value;

    // Mettez à jour les villes pour la région
    this.cities = this.commonSrv.getCities(selectedRegion);
  }

  back() {
    if (this.wholeService.getWholeSaleBoolean) {
      this.wholeService.getWholeSaleBoolean = false;
    }
    this.wholeService.wholeSaleDetail = null;
    this.form.reset();
    this.location.back();
  }
}
