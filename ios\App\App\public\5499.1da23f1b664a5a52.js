"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5499],{85499:(c,i,n)=>{n.r(i),n.d(i,{BarcodeScannerWeb:()=>o});var e=n(73308),r=n(22126);class o extends r.E_{startScan(t){var a=this;return(0,e.A)(function*(){throw a.createUnavailableException()})()}stopScan(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}readBarcodesFromImage(t){var a=this;return(0,e.A)(function*(){throw a.createUnavailableException()})()}scan(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}isSupported(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}enableTorch(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}disableTorch(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}toggleTorch(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}isTorchEnabled(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}isTorchAvailable(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}setZoomRatio(t){var a=this;return(0,e.A)(function*(){throw a.createUnavailableException()})()}getZoomRatio(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}getMinZoomRatio(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}getMaxZoomRatio(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}openSettings(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}isGoogleBarcodeScannerModuleAvailable(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}installGoogleBarcodeScannerModule(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}checkPermissions(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}requestPermissions(){var t=this;return(0,e.A)(function*(){throw t.createUnavailableException()})()}createUnavailableException(){return new r.I9("This Barcode Scanner plugin method is not available on this platform.",r.$c.Unavailable)}}}}]);