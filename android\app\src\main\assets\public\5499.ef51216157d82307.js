"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5499],{85499:(u,o,a)=>{a.r(o),a.d(o,{BarcodeScannerWeb:()=>l});var t=a(73308),i=a(22126),s=a(92391);class l extends i.E_{constructor(){super(...arguments),this._isSupported="BarcodeDetector"in window,this.errorVideoElementMissing="videoElement must be provided.",this.eventBarcodesScanned="barcodesScanned"}startScan(e){var r=this;return(0,t.A)(function*(){if(!r._isSupported)throw r.createUnavailableException();if(!e?.videoElement)throw new Error(r.errorVideoElementMissing);r.videoElement=e.videoElement,r.stream=yield navigator.mediaDevices.getUserMedia({video:{facingMode:{ideal:e?.lensFacing===s.Nn.Front?"user":"environment"}},audio:!1}),e.videoElement.srcObject=r.stream,yield e.videoElement.play();const n=new BarcodeDetector;r.intervalId=window.setInterval((0,t.A)(function*(){if(!e.videoElement)return;const c=yield n.detect(e.videoElement);0!==c.length&&r.handleScannedBarcodes(c)}),500)})()}stopScan(){var e=this;return(0,t.A)(function*(){if(!e._isSupported)throw e.createUnavailableException();e.intervalId&&(clearInterval(e.intervalId),e.intervalId=void 0),e.stream&&(e.stream.getTracks().forEach(r=>r.stop()),e.stream=void 0),e.videoElement&&(e.videoElement.srcObject=null,e.videoElement=void 0)})()}readBarcodesFromImage(e){var r=this;return(0,t.A)(function*(){throw r.createUnavailableException()})()}scan(){var e=this;return(0,t.A)(function*(){throw e.createUnavailableException()})()}isSupported(){var e=this;return(0,t.A)(function*(){return{supported:e._isSupported}})()}enableTorch(){var e=this;return(0,t.A)(function*(){throw e.createUnavailableException()})()}disableTorch(){var e=this;return(0,t.A)(function*(){throw e.createUnavailableException()})()}toggleTorch(){var e=this;return(0,t.A)(function*(){throw e.createUnavailableException()})()}isTorchEnabled(){var e=this;return(0,t.A)(function*(){throw e.createUnavailableException})()}isTorchAvailable(){var e=this;return(0,t.A)(function*(){throw e.createUnavailableException()})()}setZoomRatio(e){var r=this;return(0,t.A)(function*(){throw r.createUnavailableException()})()}getZoomRatio(){var e=this;return(0,t.A)(function*(){throw e.createUnavailableException()})()}getMinZoomRatio(){var e=this;return(0,t.A)(function*(){throw e.createUnavailableException()})()}getMaxZoomRatio(){var e=this;return(0,t.A)(function*(){throw e.createUnavailableException()})()}openSettings(){var e=this;return(0,t.A)(function*(){throw e.createUnavailableException()})()}isGoogleBarcodeScannerModuleAvailable(){var e=this;return(0,t.A)(function*(){throw e.createUnavailableException()})()}installGoogleBarcodeScannerModule(){var e=this;return(0,t.A)(function*(){throw e.createUnavailableException()})()}checkPermissions(){return(0,t.A)(function*(){try{return{camera:(yield navigator.permissions.query({name:"camera"})).state}}catch{return{camera:"prompt"}}})()}requestPermissions(){return(0,t.A)(function*(){try{return(yield navigator.mediaDevices.getUserMedia({video:!0})).getTracks().forEach(r=>r.stop()),{camera:"granted"}}catch{return{camera:"denied"}}})()}createUnavailableException(){return new i.I9("This plugin method is not available on this platform.",i.$c.Unavailable)}handleScannedBarcodes(e){const r={barcodes:e.map(n=>({cornerPoints:[[n.cornerPoints[0].x,n.cornerPoints[0].y],[n.cornerPoints[1].x,n.cornerPoints[1].y],[n.cornerPoints[2].x,n.cornerPoints[2].y],[n.cornerPoints[3].x,n.cornerPoints[3].y]],displayValue:n.rawValue,rawValue:n.rawValue,format:n.format.toUpperCase(),valueType:s.gH.Unknown}))};this.notifyListeners(this.eventBarcodesScanned,r)}}}}]);