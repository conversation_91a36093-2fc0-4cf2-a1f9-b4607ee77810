"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7186],{71604:(yo,oi,wt)=>{wt.d(oi,{D:()=>ci,Z:()=>li});var U=wt(56610),S=wt(2978),lt=wt(17438);function ct(R,gt){1&R&&S.eu8(0)}function Rt(R,gt){if(1&R&&(S.j41(0,"div",8),S.SdG(1,1),S.DNE(2,ct,1,0,"ng-container",6),S.k0s()),2&R){const B=S.XpG();S.R7$(2),S.Y8G("ngTemplateOutlet",B.headerTemplate)}}function Qi(R,gt){1&R&&S.eu8(0)}function ft(R,gt){if(1&R&&(S.j41(0,"div",9),<PERSON><PERSON>(1),<PERSON><PERSON><PERSON>(2,Qi,1,0,"ng-container",6),S.k0s()),2&R){const B=S.XpG();S.R7$(1),S.SpI(" ",B.header," "),S.R7$(1),S.Y8G("ngTemplateOutlet",B.titleTemplate)}}function ht(R,gt){1&R&&S.eu8(0)}function Pe(R,gt){if(1&R&&(S.j41(0,"div",10),S.EFF(1),S.DNE(2,ht,1,0,"ng-container",6),S.k0s()),2&R){const B=S.XpG();S.R7$(1),S.SpI(" ",B.subheader," "),S.R7$(1),S.Y8G("ngTemplateOutlet",B.subtitleTemplate)}}function it(R,gt){1&R&&S.eu8(0)}function se(R,gt){1&R&&S.eu8(0)}function ai(R,gt){if(1&R&&(S.j41(0,"div",11),S.SdG(1,2),S.DNE(2,se,1,0,"ng-container",6),S.k0s()),2&R){const B=S.XpG();S.R7$(2),S.Y8G("ngTemplateOutlet",B.footerTemplate)}}const ri=["*",[["p-header"]],[["p-footer"]]],Xt=["*","p-header","p-footer"];let li=(()=>{class R{el;header;subheader;style;styleClass;headerFacet;footerFacet;templates;headerTemplate;titleTemplate;subtitleTemplate;contentTemplate;footerTemplate;constructor(B){this.el=B}ngAfterContentInit(){this.templates.forEach(B=>{switch(B.getType()){case"header":this.headerTemplate=B.template;break;case"title":this.titleTemplate=B.template;break;case"subtitle":this.subtitleTemplate=B.template;break;case"content":default:this.contentTemplate=B.template;break;case"footer":this.footerTemplate=B.template}})}getBlockableElement(){return this.el.nativeElement.children[0]}static \u0275fac=function(rt){return new(rt||R)(S.rXU(S.aKT))};static \u0275cmp=S.VBU({type:R,selectors:[["p-card"]],contentQueries:function(rt,K,ne){if(1&rt&&(S.wni(ne,lt.Y9,5),S.wni(ne,lt.wi,5),S.wni(ne,lt.Ei,4)),2&rt){let Ct;S.mGM(Ct=S.lsd())&&(K.headerFacet=Ct.first),S.mGM(Ct=S.lsd())&&(K.footerFacet=Ct.first),S.mGM(Ct=S.lsd())&&(K.templates=Ct)}},hostAttrs:[1,"p-element"],inputs:{header:"header",subheader:"subheader",style:"style",styleClass:"styleClass"},ngContentSelectors:Xt,decls:9,vars:10,consts:[[3,"ngClass","ngStyle"],["class","p-card-header",4,"ngIf"],[1,"p-card-body"],["class","p-card-title",4,"ngIf"],["class","p-card-subtitle",4,"ngIf"],[1,"p-card-content"],[4,"ngTemplateOutlet"],["class","p-card-footer",4,"ngIf"],[1,"p-card-header"],[1,"p-card-title"],[1,"p-card-subtitle"],[1,"p-card-footer"]],template:function(rt,K){1&rt&&(S.NAR(ri),S.j41(0,"div",0),S.DNE(1,Rt,3,1,"div",1),S.j41(2,"div",2),S.DNE(3,ft,3,2,"div",3),S.DNE(4,Pe,3,2,"div",4),S.j41(5,"div",5),S.SdG(6),S.DNE(7,it,1,0,"ng-container",6),S.k0s(),S.DNE(8,ai,3,1,"div",7),S.k0s()()),2&rt&&(S.HbH(K.styleClass),S.Y8G("ngClass","p-card p-component")("ngStyle",K.style),S.BMQ("data-pc-name","card"),S.R7$(1),S.Y8G("ngIf",K.headerFacet||K.headerTemplate),S.R7$(2),S.Y8G("ngIf",K.header||K.titleTemplate),S.R7$(1),S.Y8G("ngIf",K.subheader||K.subtitleTemplate),S.R7$(3),S.Y8G("ngTemplateOutlet",K.contentTemplate),S.R7$(1),S.Y8G("ngIf",K.footerFacet||K.footerTemplate))},dependencies:[U.YU,U.bT,U.T3,U.B3],styles:["@layer primeng{.p-card-header img{width:100%}}\n"],encapsulation:2,changeDetection:0})}return R})(),ci=(()=>{class R{static \u0275fac=function(rt){return new(rt||R)};static \u0275mod=S.$C({type:R});static \u0275inj=S.G2t({imports:[U.MD,lt.Gg]})}return R})()},60787:(yo,oi,wt)=>{wt.d(oi,{F:()=>Lh,X:()=>Th});var U=wt(2978),S=wt(56610);function lt(e){return e+.5|0}const ct=(e,i,t)=>Math.max(Math.min(e,t),i);function Rt(e){return ct(lt(2.55*e),0,255)}function ft(e){return ct(lt(255*e),0,255)}function ht(e){return ct(lt(e/2.55)/100,0,1)}function Pe(e){return ct(lt(100*e),0,100)}const it={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},se=[..."0123456789ABCDEF"],ai=e=>se[15&e],ri=e=>se[(240&e)>>4]+se[15&e],Xt=e=>(240&e)>>4==(15&e);const B=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function rt(e,i,t){const s=i*Math.min(t,1-t),n=(o,a=(o+e/30)%12)=>t-s*Math.max(Math.min(a-3,9-a,1),-1);return[n(0),n(8),n(4)]}function K(e,i,t){const s=(n,o=(n+e/60)%6)=>t-t*i*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function ne(e,i,t){const s=rt(e,1,.5);let n;for(i+t>1&&(n=1/(i+t),i*=n,t*=n),n=0;n<3;n++)s[n]*=1-i-t,s[n]+=i;return s}function hi(e){const t=e.r/255,s=e.g/255,n=e.b/255,o=Math.max(t,s,n),a=Math.min(t,s,n),r=(o+a)/2;let l,c,h;return o!==a&&(h=o-a,c=r>.5?h/(2-o-a):h/(o+a),l=function Ct(e,i,t,s,n){return e===n?(i-t)/s+(i<t?6:0):i===n?(t-e)/s+2:(e-i)/s+4}(t,s,n,h,o),l=60*l+.5),[0|l,c||0,r]}function di(e,i,t,s){return(Array.isArray(i)?e(i[0],i[1],i[2]):e(i,t,s)).map(ft)}function ui(e,i,t){return di(rt,e,i,t)}function Ji(e){return(e%360+360)%360}const ts={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},es={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};let De;const Do=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,fi=e=>e<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055,Gt=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function Ae(e,i,t){if(e){let s=hi(e);s[i]=Math.max(0,Math.min(s[i]+s[i]*t,0===i?360:1)),s=ui(s),e.r=s[0],e.g=s[1],e.b=s[2]}}function is(e,i){return e&&Object.assign(i||{},e)}function ss(e){var i={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(i={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(i.a=ft(e[3]))):(i=is(e,{r:0,g:0,b:0,a:1})).a=ft(i.a),i}function Lo(e){return"r"===e.charAt(0)?function Ao(e){const i=Do.exec(e);let s,n,o,t=255;if(i){if(i[7]!==s){const a=+i[7];t=i[8]?Rt(a):ct(255*a,0,255)}return s=+i[1],n=+i[3],o=+i[5],s=255&(i[2]?Rt(s):ct(s,0,255)),n=255&(i[4]?Rt(n):ct(n,0,255)),o=255&(i[6]?Rt(o):ct(o,0,255)),{r:s,g:n,b:o,a:t}}}(e):function ko(e){const i=B.exec(e);let s,t=255;if(!i)return;i[5]!==s&&(t=i[6]?Rt(+i[5]):ft(+i[5]));const n=Ji(+i[2]),o=+i[3]/100,a=+i[4]/100;return s="hwb"===i[1]?function vo(e,i,t){return di(ne,e,i,t)}(n,o,a):"hsv"===i[1]?function Mo(e,i,t){return di(K,e,i,t)}(n,o,a):ui(n,o,a),{r:s[0],g:s[1],b:s[2],a:t}}(e)}class Kt{constructor(i){if(i instanceof Kt)return i;const t=typeof i;let s;"object"===t?s=ss(i):"string"===t&&(s=function ci(e){var t,i=e.length;return"#"===e[0]&&(4===i||5===i?t={r:255&17*it[e[1]],g:255&17*it[e[2]],b:255&17*it[e[3]],a:5===i?17*it[e[4]]:255}:(7===i||9===i)&&(t={r:it[e[1]]<<4|it[e[2]],g:it[e[3]]<<4|it[e[4]],b:it[e[5]]<<4|it[e[6]],a:9===i?it[e[7]]<<4|it[e[8]]:255})),t}(i)||function Po(e){De||(De=function Co(){const e={},i=Object.keys(es),t=Object.keys(ts);let s,n,o,a,r;for(s=0;s<i.length;s++){for(a=r=i[s],n=0;n<t.length;n++)o=t[n],r=r.replace(o,ts[o]);o=parseInt(es[a],16),e[r]=[o>>16&255,o>>8&255,255&o]}return e}(),De.transparent=[0,0,0,0]);const i=De[e.toLowerCase()];return i&&{r:i[0],g:i[1],b:i[2],a:4===i.length?i[3]:255}}(i)||Lo(i)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var i=is(this._rgb);return i&&(i.a=ht(i.a)),i}set rgb(i){this._rgb=ss(i)}rgbString(){return this._valid?function Oo(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${ht(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}(this._rgb):void 0}hexString(){return this._valid?function gt(e){var i=(e=>Xt(e.r)&&Xt(e.g)&&Xt(e.b)&&Xt(e.a))(e)?ai:ri;return e?"#"+i(e.r)+i(e.g)+i(e.b)+((e,i)=>e<255?i(e):"")(e.a,i):void 0}(this._rgb):void 0}hslString(){return this._valid?function wo(e){if(!e)return;const i=hi(e),t=i[0],s=Pe(i[1]),n=Pe(i[2]);return e.a<255?`hsla(${t}, ${s}%, ${n}%, ${ht(e.a)})`:`hsl(${t}, ${s}%, ${n}%)`}(this._rgb):void 0}mix(i,t){if(i){const s=this.rgb,n=i.rgb;let o;const a=t===o?.5:t,r=2*a-1,l=s.a-n.a,c=((r*l==-1?r:(r+l)/(1+r*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=a*s.a+(1-a)*n.a,this.rgb=s}return this}interpolate(i,t){return i&&(this._rgb=function To(e,i,t){const s=Gt(ht(e.r)),n=Gt(ht(e.g)),o=Gt(ht(e.b));return{r:ft(fi(s+t*(Gt(ht(i.r))-s))),g:ft(fi(n+t*(Gt(ht(i.g))-n))),b:ft(fi(o+t*(Gt(ht(i.b))-o))),a:e.a+t*(i.a-e.a)}}(this._rgb,i._rgb,t)),this}clone(){return new Kt(this.rgb)}alpha(i){return this._rgb.a=ft(i),this}clearer(i){return this._rgb.a*=1-i,this}greyscale(){const i=this._rgb,t=lt(.3*i.r+.59*i.g+.11*i.b);return i.r=i.g=i.b=t,this}opaquer(i){return this._rgb.a*=1+i,this}negate(){const i=this._rgb;return i.r=255-i.r,i.g=255-i.g,i.b=255-i.b,this}lighten(i){return Ae(this._rgb,2,i),this}darken(i){return Ae(this._rgb,2,-i),this}saturate(i){return Ae(this._rgb,1,i),this}desaturate(i){return Ae(this._rgb,1,-i),this}rotate(i){return function So(e,i){var t=hi(e);t[0]=Ji(t[0]+i),t=ui(t),e.r=t[0],e.g=t[1],e.b=t[2]}(this._rgb,i),this}}function _t(){}const Ro=(()=>{let e=0;return()=>e++})();function O(e){return null==e}function V(e){if(Array.isArray&&Array.isArray(e))return!0;const i=Object.prototype.toString.call(e);return"[object"===i.slice(0,7)&&"Array]"===i.slice(-6)}function T(e){return null!==e&&"[object Object]"===Object.prototype.toString.call(e)}function j(e){return("number"==typeof e||e instanceof Number)&&isFinite(+e)}function ot(e,i){return j(e)?e:i}function P(e,i){return typeof e>"u"?i:e}const ns=(e,i)=>"string"==typeof e&&e.endsWith("%")?parseFloat(e)/100*i:+e;function z(e,i,t){if(e&&"function"==typeof e.call)return e.apply(t,i)}function I(e,i,t,s){let n,o,a;if(V(e))if(o=e.length,s)for(n=o-1;n>=0;n--)i.call(t,e[n],n);else for(n=0;n<o;n++)i.call(t,e[n],n);else if(T(e))for(a=Object.keys(e),o=a.length,n=0;n<o;n++)i.call(t,e[a[n]],a[n])}function Oe(e,i){let t,s,n,o;if(!e||!i||e.length!==i.length)return!1;for(t=0,s=e.length;t<s;++t)if(n=e[t],o=i[t],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function Te(e){if(V(e))return e.map(Te);if(T(e)){const i=Object.create(null),t=Object.keys(e),s=t.length;let n=0;for(;n<s;++n)i[t[n]]=Te(e[t[n]]);return i}return e}function os(e){return-1===["__proto__","prototype","constructor"].indexOf(e)}function Io(e,i,t,s){if(!os(e))return;const n=i[e],o=t[e];T(n)&&T(o)?oe(n,o,s):i[e]=Te(o)}function oe(e,i,t){const s=V(i)?i:[i],n=s.length;if(!T(e))return e;const o=(t=t||{}).merger||Io;let a;for(let r=0;r<n;++r){if(a=s[r],!T(a))continue;const l=Object.keys(a);for(let c=0,h=l.length;c<h;++c)o(l[c],e,a,t)}return e}function ae(e,i){return oe(e,i,{merger:Fo})}function Fo(e,i,t){if(!os(e))return;const s=i[e],n=t[e];T(s)&&T(n)?ae(s,n):Object.prototype.hasOwnProperty.call(i,e)||(i[e]=Te(n))}const as={"":e=>e,x:e=>e.x,y:e=>e.y};function Pt(e,i){return(as[i]||(as[i]=function Bo(e){const i=function zo(e){const i=e.split("."),t=[];let s="";for(const n of i)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(t.push(s),s="");return t}(e);return t=>{for(const s of i){if(""===s)break;t=t&&t[s]}return t}}(i)))(e)}function gi(e){return e.charAt(0).toUpperCase()+e.slice(1)}const re=e=>typeof e<"u",Dt=e=>"function"==typeof e,rs=(e,i)=>{if(e.size!==i.size)return!1;for(const t of e)if(!i.has(t))return!1;return!0},E=Math.PI,W=2*E,Wo=W+E,Le=Number.POSITIVE_INFINITY,No=E/180,$=E/2,Et=E/4,ls=2*E/3,At=Math.log10,pt=Math.sign;function le(e,i,t){return Math.abs(e-i)<t}function cs(e){const i=Math.round(e);e=le(e,i,e/1e3)?i:e;const t=Math.pow(10,Math.floor(At(e))),s=e/t;return(s<=1?1:s<=2?2:s<=5?5:10)*t}function qt(e){return!function jo(e){return"symbol"==typeof e||"object"==typeof e&&null!==e&&!(Symbol.toPrimitive in e||"toString"in e||"valueOf"in e)}(e)&&!isNaN(parseFloat(e))&&isFinite(e)}function hs(e,i,t){let s,n,o;for(s=0,n=e.length;s<n;s++)o=e[s][t],isNaN(o)||(i.min=Math.min(i.min,o),i.max=Math.max(i.max,o))}function dt(e){return e*(E/180)}function pi(e){return e*(180/E)}function ds(e){if(!j(e))return;let i=1,t=0;for(;Math.round(e*i)/i!==e;)i*=10,t++;return t}function us(e,i){const t=i.x-e.x,s=i.y-e.y,n=Math.sqrt(t*t+s*s);let o=Math.atan2(s,t);return o<-.5*E&&(o+=W),{angle:o,distance:n}}function mi(e,i){return Math.sqrt(Math.pow(i.x-e.x,2)+Math.pow(i.y-e.y,2))}function Yo(e,i){return(e-i+Wo)%W-E}function Z(e){return(e%W+W)%W}function ce(e,i,t,s){const n=Z(e),o=Z(i),a=Z(t),r=Z(o-n),l=Z(a-n),c=Z(n-o),h=Z(n-a);return n===o||n===a||s&&o===a||r>l&&c<h}function G(e,i,t){return Math.max(i,Math.min(t,e))}function xt(e,i,t,s=1e-6){return e>=Math.min(i,t)-s&&e<=Math.max(i,t)+s}function bi(e,i,t){t=t||(a=>e[a]<i);let o,s=e.length-1,n=0;for(;s-n>1;)o=n+s>>1,t(o)?n=o:s=o;return{lo:n,hi:s}}const yt=(e,i,t,s)=>bi(e,t,s?n=>{const o=e[n][i];return o<t||o===t&&e[n+1][i]===t}:n=>e[n][i]<t),Xo=(e,i,t)=>bi(e,t,s=>e[s][i]>=t),fs=["push","pop","shift","splice","unshift"];function gs(e,i){const t=e._chartjs;if(!t)return;const s=t.listeners,n=s.indexOf(i);-1!==n&&s.splice(n,1),!(s.length>0)&&(fs.forEach(o=>{delete e[o]}),delete e._chartjs)}function ps(e){const i=new Set(e);return i.size===e.length?e:Array.from(i)}const ms=typeof window>"u"?function(e){return e()}:window.requestAnimationFrame;function bs(e,i){let t=[],s=!1;return function(...n){t=n,s||(s=!0,ms.call(window,()=>{s=!1,e.apply(i,t)}))}}const _i=e=>"start"===e?"left":"end"===e?"right":"center",Q=(e,i,t)=>"start"===e?i:"end"===e?t:(i+t)/2;function _s(e,i,t){const s=i.length;let n=0,o=s;if(e._sorted){const{iScale:a,vScale:r,_parsed:l}=e,c=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null,h=a.axis,{min:d,max:u,minDefined:f,maxDefined:p}=a.getUserBounds();if(f){if(n=Math.min(yt(l,h,d).lo,t?s:yt(i,h,a.getPixelForValue(d)).lo),c){const g=l.slice(0,n+1).reverse().findIndex(m=>!O(m[r.axis]));n-=Math.max(0,g)}n=G(n,0,s-1)}if(p){let g=Math.max(yt(l,a.axis,u,!0).hi+1,t?0:yt(i,h,a.getPixelForValue(u),!0).hi+1);if(c){const m=l.slice(g-1).findIndex(b=>!O(b[r.axis]));g+=Math.max(0,m)}o=G(g,n,s)-n}else o=s-n}return{start:n,count:o}}function xs(e){const{xScale:i,yScale:t,_scaleRanges:s}=e,n={xmin:i.min,xmax:i.max,ymin:t.min,ymax:t.max};if(!s)return e._scaleRanges=n,!0;const o=s.xmin!==i.min||s.xmax!==i.max||s.ymin!==t.min||s.ymax!==t.max;return Object.assign(s,n),o}const Re=e=>0===e||1===e,ys=(e,i,t)=>-Math.pow(2,10*(e-=1))*Math.sin((e-i)*W/t),vs=(e,i,t)=>Math.pow(2,-10*e)*Math.sin((e-i)*W/t)+1,he={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>(e-=1)*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-((e-=1)*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>(e-=1)*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>1-Math.cos(e*$),easeOutSine:e=>Math.sin(e*$),easeInOutSine:e=>-.5*(Math.cos(E*e)-1),easeInExpo:e=>0===e?0:Math.pow(2,10*(e-1)),easeOutExpo:e=>1===e?1:1-Math.pow(2,-10*e),easeInOutExpo:e=>Re(e)?e:e<.5?.5*Math.pow(2,10*(2*e-1)):.5*(2-Math.pow(2,-10*(2*e-1))),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1-(e-=1)*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>Re(e)?e:ys(e,.075,.3),easeOutElastic:e=>Re(e)?e:vs(e,.075,.3),easeInOutElastic:e=>Re(e)?e:e<.5?.5*ys(2*e,.1125,.45):.5+.5*vs(2*e-1,.1125,.45),easeInBack:e=>e*e*(2.70158*e-1.70158),easeOutBack:e=>(e-=1)*e*(2.70158*e********)+1,easeInOutBack(e){let i=1.70158;return(e/=.5)<1?e*e*((1+(i*=1.525))*e-i)*.5:.5*((e-=2)*e*((1+(i*=1.525))*e+i)+2)},easeInBounce:e=>1-he.easeOutBounce(1-e),easeOutBounce:e=>e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375,easeInOutBounce:e=>e<.5?.5*he.easeInBounce(2*e):.5*he.easeOutBounce(2*e-1)+.5};function xi(e){if(e&&"object"==typeof e){const i=e.toString();return"[object CanvasPattern]"===i||"[object CanvasGradient]"===i}return!1}function Ms(e){return xi(e)?e:new Kt(e)}function yi(e){return xi(e)?e:new Kt(e).saturate(.5).darken(.1).hexString()}const Qo=["x","y","borderWidth","radius","tension"],Jo=["color","borderColor","backgroundColor"],ks=new Map;function de(e,i,t){return function ia(e,i){i=i||{};const t=e+JSON.stringify(i);let s=ks.get(t);return s||(s=new Intl.NumberFormat(e,i),ks.set(t,s)),s}(i,t).format(e)}const Ss={values:e=>V(e)?e:""+e,numeric(e,i,t){if(0===e)return"0";const s=this.chart.options.locale;let n,o=e;if(t.length>1){const c=Math.max(Math.abs(t[0].value),Math.abs(t[t.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=function sa(e,i){let t=i.length>3?i[2].value-i[1].value:i[1].value-i[0].value;return Math.abs(t)>=1&&e!==Math.floor(e)&&(t=e-Math.floor(e)),t}(e,t)}const a=At(Math.abs(o)),r=isNaN(a)?1:Math.max(Math.min(-1*Math.floor(a),20),0),l={notation:n,minimumFractionDigits:r,maximumFractionDigits:r};return Object.assign(l,this.options.ticks.format),de(e,s,l)},logarithmic(e,i,t){if(0===e)return"0";const s=t[i].significand||e/Math.pow(10,Math.floor(At(e)));return[1,2,3,5,10,15].includes(s)||i>.8*t.length?Ss.numeric.call(this,e,i,t):""}};var Ee={formatters:Ss};const It=Object.create(null),vi=Object.create(null);function ue(e,i){if(!i)return e;const t=i.split(".");for(let s=0,n=t.length;s<n;++s){const o=t[s];e=e[o]||(e[o]=Object.create(null))}return e}function Mi(e,i,t){return"string"==typeof i?oe(ue(e,i),t):oe(ue(e,""),i)}class oa{constructor(i,t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>yi(n.backgroundColor),this.hoverBorderColor=(s,n)=>yi(n.borderColor),this.hoverColor=(s,n)=>yi(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(i),this.apply(t)}set(i,t){return Mi(this,i,t)}get(i){return ue(this,i)}describe(i,t){return Mi(vi,i,t)}override(i,t){return Mi(It,i,t)}route(i,t,s,n){const o=ue(this,i),a=ue(this,s),r="_"+t;Object.defineProperties(o,{[r]:{value:o[t],writable:!0},[t]:{enumerable:!0,get(){const l=this[r],c=a[n];return T(l)?Object.assign({},c,l):P(l,c)},set(l){this[r]=l}}})}apply(i){i.forEach(t=>t(this))}}var N=new oa({_scriptable:e=>!e.startsWith("on"),_indexable:e=>"events"!==e,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function ta(e){e.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:i=>"onProgress"!==i&&"onComplete"!==i&&"fn"!==i}),e.set("animations",{colors:{type:"color",properties:Jo},numbers:{type:"number",properties:Qo}}),e.describe("animations",{_fallback:"animation"}),e.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:i=>0|i}}}})},function ea(e){e.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function na(e){e.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(i,t)=>t.lineWidth,tickColor:(i,t)=>t.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Ee.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),e.route("scale.ticks","color","","color"),e.route("scale.grid","color","","borderColor"),e.route("scale.border","color","","borderColor"),e.route("scale.title","color","","color"),e.describe("scale",{_fallback:!1,_scriptable:i=>!i.startsWith("before")&&!i.startsWith("after")&&"callback"!==i&&"parser"!==i,_indexable:i=>"borderDash"!==i&&"tickBorderDash"!==i&&"dash"!==i}),e.describe("scales",{_fallback:"scale"}),e.describe("scale.ticks",{_scriptable:i=>"backdropPadding"!==i&&"callback"!==i,_indexable:i=>"backdropPadding"!==i})}]);function Ie(e,i,t,s,n){let o=i[n];return o||(o=i[n]=e.measureText(n).width,t.push(n)),o>s&&(s=o),s}function ra(e,i,t,s){let n=(s=s||{}).data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==i&&(n=s.data={},o=s.garbageCollect=[],s.font=i),e.save(),e.font=i;let a=0;const r=t.length;let l,c,h,d,u;for(l=0;l<r;l++)if(d=t[l],null==d||V(d)){if(V(d))for(c=0,h=d.length;c<h;c++)u=d[c],null!=u&&!V(u)&&(a=Ie(e,n,o,a,u))}else a=Ie(e,n,o,a,d);e.restore();const f=o.length/2;if(f>t.length){for(l=0;l<f;l++)delete n[o[l]];o.splice(0,f)}return a}function Ft(e,i,t){const s=e.currentDevicePixelRatio,n=0!==t?Math.max(t/2,.5):0;return Math.round((i-n)*s)/s+n}function ws(e,i){!i&&!e||((i=i||e.getContext("2d")).save(),i.resetTransform(),i.clearRect(0,0,e.width,e.height),i.restore())}function ki(e,i,t,s){Cs(e,i,t,s,null)}function Cs(e,i,t,s,n){let o,a,r,l,c,h,d,u;const f=i.pointStyle,p=i.rotation,g=i.radius;let m=(p||0)*No;if(f&&"object"==typeof f&&(o=f.toString(),"[object HTMLImageElement]"===o||"[object HTMLCanvasElement]"===o))return e.save(),e.translate(t,s),e.rotate(m),e.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),void e.restore();if(!(isNaN(g)||g<=0)){switch(e.beginPath(),f){default:n?e.ellipse(t,s,n/2,g,0,0,W):e.arc(t,s,g,0,W),e.closePath();break;case"triangle":h=n?n/2:g,e.moveTo(t+Math.sin(m)*h,s-Math.cos(m)*g),m+=ls,e.lineTo(t+Math.sin(m)*h,s-Math.cos(m)*g),m+=ls,e.lineTo(t+Math.sin(m)*h,s-Math.cos(m)*g),e.closePath();break;case"rectRounded":c=.516*g,l=g-c,a=Math.cos(m+Et)*l,d=Math.cos(m+Et)*(n?n/2-c:l),r=Math.sin(m+Et)*l,u=Math.sin(m+Et)*(n?n/2-c:l),e.arc(t-d,s-r,c,m-E,m-$),e.arc(t+u,s-a,c,m-$,m),e.arc(t+d,s+r,c,m,m+$),e.arc(t-u,s+a,c,m+$,m+E),e.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*g,h=n?n/2:l,e.rect(t-h,s-l,2*h,2*l);break}m+=Et;case"rectRot":d=Math.cos(m)*(n?n/2:g),a=Math.cos(m)*g,r=Math.sin(m)*g,u=Math.sin(m)*(n?n/2:g),e.moveTo(t-d,s-r),e.lineTo(t+u,s-a),e.lineTo(t+d,s+r),e.lineTo(t-u,s+a),e.closePath();break;case"crossRot":m+=Et;case"cross":d=Math.cos(m)*(n?n/2:g),a=Math.cos(m)*g,r=Math.sin(m)*g,u=Math.sin(m)*(n?n/2:g),e.moveTo(t-d,s-r),e.lineTo(t+d,s+r),e.moveTo(t+u,s-a),e.lineTo(t-u,s+a);break;case"star":d=Math.cos(m)*(n?n/2:g),a=Math.cos(m)*g,r=Math.sin(m)*g,u=Math.sin(m)*(n?n/2:g),e.moveTo(t-d,s-r),e.lineTo(t+d,s+r),e.moveTo(t+u,s-a),e.lineTo(t-u,s+a),m+=Et,d=Math.cos(m)*(n?n/2:g),a=Math.cos(m)*g,r=Math.sin(m)*g,u=Math.sin(m)*(n?n/2:g),e.moveTo(t-d,s-r),e.lineTo(t+d,s+r),e.moveTo(t+u,s-a),e.lineTo(t-u,s+a);break;case"line":a=n?n/2:Math.cos(m)*g,r=Math.sin(m)*g,e.moveTo(t-a,s-r),e.lineTo(t+a,s+r);break;case"dash":e.moveTo(t,s),e.lineTo(t+Math.cos(m)*(n?n/2:g),s+Math.sin(m)*g);break;case!1:e.closePath()}e.fill(),i.borderWidth>0&&e.stroke()}}function vt(e,i,t){return t=t||.5,!i||e&&e.x>i.left-t&&e.x<i.right+t&&e.y>i.top-t&&e.y<i.bottom+t}function Fe(e,i){e.save(),e.beginPath(),e.rect(i.left,i.top,i.right-i.left,i.bottom-i.top),e.clip()}function ze(e){e.restore()}function la(e,i,t,s,n){if(!i)return e.lineTo(t.x,t.y);if("middle"===n){const o=(i.x+t.x)/2;e.lineTo(o,i.y),e.lineTo(o,t.y)}else"after"===n!=!!s?e.lineTo(i.x,t.y):e.lineTo(t.x,i.y);e.lineTo(t.x,t.y)}function ca(e,i,t,s){if(!i)return e.lineTo(t.x,t.y);e.bezierCurveTo(s?i.cp1x:i.cp2x,s?i.cp1y:i.cp2y,s?t.cp2x:t.cp1x,s?t.cp2y:t.cp1y,t.x,t.y)}function da(e,i,t,s,n){if(n.strikethrough||n.underline){const o=e.measureText(s),a=i-o.actualBoundingBoxLeft,r=i+o.actualBoundingBoxRight,c=t+o.actualBoundingBoxDescent,h=n.strikethrough?(t-o.actualBoundingBoxAscent+c)/2:c;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=n.decorationWidth||2,e.moveTo(a,h),e.lineTo(r,h),e.stroke()}}function ua(e,i){const t=e.fillStyle;e.fillStyle=i.color,e.fillRect(i.left,i.top,i.width,i.height),e.fillStyle=t}function zt(e,i,t,s,n,o={}){const a=V(i)?i:[i],r=o.strokeWidth>0&&""!==o.strokeColor;let l,c;for(e.save(),e.font=n.string,function ha(e,i){i.translation&&e.translate(i.translation[0],i.translation[1]),O(i.rotation)||e.rotate(i.rotation),i.color&&(e.fillStyle=i.color),i.textAlign&&(e.textAlign=i.textAlign),i.textBaseline&&(e.textBaseline=i.textBaseline)}(e,o),l=0;l<a.length;++l)c=a[l],o.backdrop&&ua(e,o.backdrop),r&&(o.strokeColor&&(e.strokeStyle=o.strokeColor),O(o.strokeWidth)||(e.lineWidth=o.strokeWidth),e.strokeText(c,t,s,o.maxWidth)),e.fillText(c,t,s,o.maxWidth),da(e,t,s,c,o),s+=Number(n.lineHeight);e.restore()}function fe(e,i){const{x:t,y:s,w:n,h:o,radius:a}=i;e.arc(t+a.topLeft,s+a.topLeft,a.topLeft,1.5*E,E,!0),e.lineTo(t,s+o-a.bottomLeft),e.arc(t+a.bottomLeft,s+o-a.bottomLeft,a.bottomLeft,E,$,!0),e.lineTo(t+n-a.bottomRight,s+o),e.arc(t+n-a.bottomRight,s+o-a.bottomRight,a.bottomRight,$,0,!0),e.lineTo(t+n,s+a.topRight),e.arc(t+n-a.topRight,s+a.topRight,a.topRight,0,-$,!0),e.lineTo(t+a.topLeft,s)}const fa=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,ga=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function pa(e,i){const t=(""+e).match(fa);if(!t||"normal"===t[1])return 1.2*i;switch(e=+t[2],t[3]){case"px":return e;case"%":e/=100}return i*e}const ma=e=>+e||0;function Si(e,i){const t={},s=T(i),n=s?Object.keys(i):i,o=T(e)?s?a=>P(e[a],e[i[a]]):a=>e[a]:()=>e;for(const a of n)t[a]=ma(o(a));return t}function Ps(e){return Si(e,{top:"y",right:"x",bottom:"y",left:"x"})}function Bt(e){return Si(e,["topLeft","topRight","bottomLeft","bottomRight"])}function J(e){const i=Ps(e);return i.width=i.left+i.right,i.height=i.top+i.bottom,i}function X(e,i){let t=P((e=e||{}).size,(i=i||N.font).size);"string"==typeof t&&(t=parseInt(t,10));let s=P(e.style,i.style);s&&!(""+s).match(ga)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:P(e.family,i.family),lineHeight:pa(P(e.lineHeight,i.lineHeight),t),size:t,style:s,weight:P(e.weight,i.weight),string:""};return n.string=function aa(e){return!e||O(e.size)||O(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family}(n),n}function ge(e,i,t,s){let o,a,r,n=!0;for(o=0,a=e.length;o<a;++o)if(r=e[o],void 0!==r&&(void 0!==i&&"function"==typeof r&&(r=r(i),n=!1),void 0!==t&&V(r)&&(r=r[t%r.length],n=!1),void 0!==r))return s&&!n&&(s.cacheable=!1),r}function Ot(e,i){return Object.assign(Object.create(e),i)}function wi(e,i=[""],t,s,n=(()=>e[0])){const o=t||e;typeof s>"u"&&(s=Ls("_fallback",e));const a={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:e,_rootScopes:o,_fallback:s,_getTarget:n,override:r=>wi([r,...e],i,o,s)};return new Proxy(a,{deleteProperty:(r,l)=>(delete r[l],delete r._keys,delete e[0][l],!0),get:(r,l)=>As(r,l,()=>function wa(e,i,t,s){let n;for(const o of i)if(n=Ls(_a(o,e),t),typeof n<"u")return Ci(e,n)?Pi(t,s,e,n):n}(l,i,e,r)),getOwnPropertyDescriptor:(r,l)=>Reflect.getOwnPropertyDescriptor(r._scopes[0],l),getPrototypeOf:()=>Reflect.getPrototypeOf(e[0]),has:(r,l)=>Rs(r).includes(l),ownKeys:r=>Rs(r),set(r,l,c){const h=r._storage||(r._storage=n());return r[l]=h[l]=c,delete r._keys,!0}})}function Zt(e,i,t,s){const n={_cacheable:!1,_proxy:e,_context:i,_subProxy:t,_stack:new Set,_descriptors:Ds(e,s),setContext:o=>Zt(e,o,t,s),override:o=>Zt(e.override(o),i,t,s)};return new Proxy(n,{deleteProperty:(o,a)=>(delete o[a],delete e[a],!0),get:(o,a,r)=>As(o,a,()=>function xa(e,i,t){const{_proxy:s,_context:n,_subProxy:o,_descriptors:a}=e;let r=s[i];return Dt(r)&&a.isScriptable(i)&&(r=function ya(e,i,t,s){const{_proxy:n,_context:o,_subProxy:a,_stack:r}=t;if(r.has(e))throw new Error("Recursion detected: "+Array.from(r).join("->")+"->"+e);r.add(e);let l=i(o,a||s);return r.delete(e),Ci(e,l)&&(l=Pi(n._scopes,n,e,l)),l}(i,r,e,t)),V(r)&&r.length&&(r=function va(e,i,t,s){const{_proxy:n,_context:o,_subProxy:a,_descriptors:r}=t;if(typeof o.index<"u"&&s(e))return i[o.index%i.length];if(T(i[0])){const l=i,c=n._scopes.filter(h=>h!==l);i=[];for(const h of l){const d=Pi(c,n,e,h);i.push(Zt(d,o,a&&a[e],r))}}return i}(i,r,e,a.isIndexable)),Ci(i,r)&&(r=Zt(r,n,o&&o[i],a)),r}(o,a,r)),getOwnPropertyDescriptor:(o,a)=>o._descriptors.allKeys?Reflect.has(e,a)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,a),getPrototypeOf:()=>Reflect.getPrototypeOf(e),has:(o,a)=>Reflect.has(e,a),ownKeys:()=>Reflect.ownKeys(e),set:(o,a,r)=>(e[a]=r,delete o[a],!0)})}function Ds(e,i={scriptable:!0,indexable:!0}){const{_scriptable:t=i.scriptable,_indexable:s=i.indexable,_allKeys:n=i.allKeys}=e;return{allKeys:n,scriptable:t,indexable:s,isScriptable:Dt(t)?t:()=>t,isIndexable:Dt(s)?s:()=>s}}const _a=(e,i)=>e?e+gi(i):i,Ci=(e,i)=>T(i)&&"adapters"!==e&&(null===Object.getPrototypeOf(i)||i.constructor===Object);function As(e,i,t){if(Object.prototype.hasOwnProperty.call(e,i)||"constructor"===i)return e[i];const s=t();return e[i]=s,s}function Os(e,i,t){return Dt(e)?e(i,t):e}const Ma=(e,i)=>!0===e?i:"string"==typeof e?Pt(i,e):void 0;function ka(e,i,t,s,n){for(const o of i){const a=Ma(t,o);if(a){e.add(a);const r=Os(a._fallback,t,n);if(typeof r<"u"&&r!==t&&r!==s)return r}else if(!1===a&&typeof s<"u"&&t!==s)return null}return!1}function Pi(e,i,t,s){const n=i._rootScopes,o=Os(i._fallback,t,s),a=[...e,...n],r=new Set;r.add(s);let l=Ts(r,a,t,o||t,s);return!(null===l||typeof o<"u"&&o!==t&&(l=Ts(r,a,o,l,s),null===l))&&wi(Array.from(r),[""],n,o,()=>function Sa(e,i,t){const s=e._getTarget();i in s||(s[i]={});const n=s[i];return V(n)&&T(t)?t:n||{}}(i,t,s))}function Ts(e,i,t,s,n){for(;t;)t=ka(e,i,t,s,n);return t}function Ls(e,i){for(const t of i){if(!t)continue;const s=t[e];if(typeof s<"u")return s}}function Rs(e){let i=e._keys;return i||(i=e._keys=function Ca(e){const i=new Set;for(const t of e)for(const s of Object.keys(t).filter(n=>!n.startsWith("_")))i.add(s);return Array.from(i)}(e._scopes)),i}function Es(e,i,t,s){const{iScale:n}=e,{key:o="r"}=this._parsing,a=new Array(s);let r,l,c,h;for(r=0,l=s;r<l;++r)c=r+t,h=i[c],a[r]={r:n.parse(Pt(h,o),c)};return a}const Pa=Number.EPSILON||1e-14,Qt=(e,i)=>i<e.length&&!e[i].skip&&e[i],Is=e=>"x"===e?"y":"x";function Da(e,i,t,s){const n=e.skip?i:e,o=i,a=t.skip?i:t,r=mi(o,n),l=mi(a,o);let c=r/(r+l),h=l/(r+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const d=s*c,u=s*h;return{previous:{x:o.x-d*(a.x-n.x),y:o.y-d*(a.y-n.y)},next:{x:o.x+u*(a.x-n.x),y:o.y+u*(a.y-n.y)}}}function Be(e,i,t){return Math.max(Math.min(e,t),i)}function Ra(e,i,t,s,n){let o,a,r,l;if(i.spanGaps&&(e=e.filter(c=>!c.skip)),"monotone"===i.cubicInterpolationMode)!function Ta(e,i="x"){const t=Is(i),s=e.length,n=Array(s).fill(0),o=Array(s);let a,r,l,c=Qt(e,0);for(a=0;a<s;++a)if(r=l,l=c,c=Qt(e,a+1),l){if(c){const h=c[i]-l[i];n[a]=0!==h?(c[t]-l[t])/h:0}o[a]=r?c?pt(n[a-1])!==pt(n[a])?0:(n[a-1]+n[a])/2:n[a-1]:n[a]}(function Aa(e,i,t){const s=e.length;let n,o,a,r,l,c=Qt(e,0);for(let h=0;h<s-1;++h)if(l=c,c=Qt(e,h+1),l&&c){if(le(i[h],0,Pa)){t[h]=t[h+1]=0;continue}n=t[h]/i[h],o=t[h+1]/i[h],r=Math.pow(n,2)+Math.pow(o,2),!(r<=9)&&(a=3/Math.sqrt(r),t[h]=n*a*i[h],t[h+1]=o*a*i[h])}})(e,n,o),function Oa(e,i,t="x"){const s=Is(t),n=e.length;let o,a,r,l=Qt(e,0);for(let c=0;c<n;++c){if(a=r,r=l,l=Qt(e,c+1),!r)continue;const h=r[t],d=r[s];a&&(o=(h-a[t])/3,r[`cp1${t}`]=h-o,r[`cp1${s}`]=d-o*i[c]),l&&(o=(l[t]-h)/3,r[`cp2${t}`]=h+o,r[`cp2${s}`]=d+o*i[c])}}(e,o,i)}(e,n);else{let c=s?e[e.length-1]:e[0];for(o=0,a=e.length;o<a;++o)r=e[o],l=Da(c,r,e[Math.min(o+1,a-(s?0:1))%a],i.tension),r.cp1x=l.previous.x,r.cp1y=l.previous.y,r.cp2x=l.next.x,r.cp2y=l.next.y,c=r}i.capBezierPoints&&function La(e,i){let t,s,n,o,a,r=vt(e[0],i);for(t=0,s=e.length;t<s;++t)a=o,o=r,r=t<s-1&&vt(e[t+1],i),o&&(n=e[t],a&&(n.cp1x=Be(n.cp1x,i.left,i.right),n.cp1y=Be(n.cp1y,i.top,i.bottom)),r&&(n.cp2x=Be(n.cp2x,i.left,i.right),n.cp2y=Be(n.cp2y,i.top,i.bottom)))}(e,t)}function Di(){return typeof window<"u"&&typeof document<"u"}function Ai(e){let i=e.parentNode;return i&&"[object ShadowRoot]"===i.toString()&&(i=i.host),i}function Ve(e,i,t){let s;return"string"==typeof e?(s=parseInt(e,10),-1!==e.indexOf("%")&&(s=s/100*i.parentNode[t])):s=e,s}const We=e=>e.ownerDocument.defaultView.getComputedStyle(e,null),Ia=["top","right","bottom","left"];function Vt(e,i,t){const s={};t=t?"-"+t:"";for(let n=0;n<4;n++){const o=Ia[n];s[o]=parseFloat(e[i+"-"+o+t])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const Fa=(e,i,t)=>(e>0||i>0)&&(!t||!t.shadowRoot);function Wt(e,i){if("native"in e)return e;const{canvas:t,currentDevicePixelRatio:s}=i,n=We(t),o="border-box"===n.boxSizing,a=Vt(n,"padding"),r=Vt(n,"border","width"),{x:l,y:c,box:h}=function za(e,i){const t=e.touches,s=t&&t.length?t[0]:e,{offsetX:n,offsetY:o}=s;let r,l,a=!1;if(Fa(n,o,e.target))r=n,l=o;else{const c=i.getBoundingClientRect();r=s.clientX-c.left,l=s.clientY-c.top,a=!0}return{x:r,y:l,box:a}}(e,t),d=a.left+(h&&r.left),u=a.top+(h&&r.top);let{width:f,height:p}=i;return o&&(f-=a.width+r.width,p-=a.height+r.height),{x:Math.round((l-d)/f*t.width/s),y:Math.round((c-u)/p*t.height/s)}}const Ne=e=>Math.round(10*e)/10;function Fs(e,i,t){const s=i||1,n=Math.floor(e.height*s),o=Math.floor(e.width*s);e.height=Math.floor(e.height),e.width=Math.floor(e.width);const a=e.canvas;return a.style&&(t||!a.style.height&&!a.style.width)&&(a.style.height=`${e.height}px`,a.style.width=`${e.width}px`),(e.currentDevicePixelRatio!==s||a.height!==n||a.width!==o)&&(e.currentDevicePixelRatio=s,a.height=n,a.width=o,e.ctx.setTransform(s,0,0,s,0,0),!0)}const Wa=function(){let e=!1;try{const i={get passive(){return e=!0,!1}};Di()&&(window.addEventListener("test",null,i),window.removeEventListener("test",null,i))}catch{}return e}();function zs(e,i){const t=function Ea(e,i){return We(e).getPropertyValue(i)}(e,i),s=t&&t.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function Nt(e,i,t,s){return{x:e.x+t*(i.x-e.x),y:e.y+t*(i.y-e.y)}}function Na(e,i,t,s){return{x:e.x+t*(i.x-e.x),y:"middle"===s?t<.5?e.y:i.y:"after"===s?t<1?e.y:i.y:t>0?i.y:e.y}}function Ha(e,i,t,s){const n={x:e.cp2x,y:e.cp2y},o={x:i.cp1x,y:i.cp1y},a=Nt(e,n,t),r=Nt(n,o,t),l=Nt(o,i,t),c=Nt(a,r,t),h=Nt(r,l,t);return Nt(c,h,t)}function Jt(e,i,t){return e?function(e,i){return{x:t=>e+e+i-t,setWidth(t){i=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,s)=>t-s,leftForLtr:(t,s)=>t-s}}(i,t):{x:e=>e,setWidth(e){},textAlign:e=>e,xPlus:(e,i)=>e+i,leftForLtr:(e,i)=>e}}function Bs(e,i){let t,s;("ltr"===i||"rtl"===i)&&(t=e.canvas.style,s=[t.getPropertyValue("direction"),t.getPropertyPriority("direction")],t.setProperty("direction",i,"important"),e.prevTextDirection=s)}function Vs(e,i){void 0!==i&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",i[0],i[1]))}function Ws(e){return"angle"===e?{between:ce,compare:Yo,normalize:Z}:{between:xt,compare:(i,t)=>i-t,normalize:i=>i}}function Ns({start:e,end:i,count:t,loop:s,style:n}){return{start:e%t,end:i%t,loop:s&&(i-e+1)%t==0,style:n}}function Hs(e,i,t){if(!t)return[e];const{property:s,start:n,end:o}=t,a=i.length,{compare:r,between:l,normalize:c}=Ws(s),{start:h,end:d,loop:u,style:f}=function Ya(e,i,t){const{property:s,start:n,end:o}=t,{between:a,normalize:r}=Ws(s),l=i.length;let u,f,{start:c,end:h,loop:d}=e;if(d){for(c+=l,h+=l,u=0,f=l;u<f&&a(r(i[c%l][s]),n,o);++u)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:e.style}}(e,i,t),p=[];let b,_,y,g=!1,m=null;for(let k=h,C=h;k<=d;++k)_=i[k%a],!_.skip&&(b=c(_[s]),b!==y&&(g=l(b,n,o),null===m&&(g||l(n,y,b)&&0!==r(n,y))&&(m=0===r(b,n)?k:C),null!==m&&(!g||0===r(o,b)||l(o,y,b))&&(p.push(Ns({start:m,end:k,loop:u,count:a,style:f})),m=null),C=k,y=b));return null!==m&&p.push(Ns({start:m,end:d,loop:u,count:a,style:f})),p}function js(e,i){const t=[],s=e.segments;for(let n=0;n<s.length;n++){const o=Hs(s[n],e.points,i);o.length&&t.push(...o)}return t}function Ys(e){return{backgroundColor:e.backgroundColor,borderCapStyle:e.borderCapStyle,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderJoinStyle:e.borderJoinStyle,borderWidth:e.borderWidth,borderColor:e.borderColor}}function qa(e,i){if(!i)return!1;const t=[],s=function(n,o){return xi(o)?(t.includes(o)||t.push(o),t.indexOf(o)):o};return JSON.stringify(e,s)!==JSON.stringify(i,s)}function He(e,i,t){return e.options.clip?e[t]:i[t]}function Us(e,i){const t=i._clip;if(t.disabled)return!1;const s=function Za(e,i){const{xScale:t,yScale:s}=e;return t&&s?{left:He(t,i,"left"),right:He(t,i,"right"),top:He(s,i,"top"),bottom:He(s,i,"bottom")}:i}(i,e.chartArea);return{left:!1===t.left?0:s.left-(!0===t.left?0:t.left),right:!1===t.right?e.width:s.right+(!0===t.right?0:t.right),top:!1===t.top?0:s.top-(!0===t.top?0:t.top),bottom:!1===t.bottom?e.height:s.bottom+(!0===t.bottom?0:t.bottom)}}class Qa{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(i,t,s,n){const a=t.duration;t.listeners[n].forEach(r=>r({chart:i,initial:t.initial,numSteps:a,currentStep:Math.min(s-t.start,a)}))}_refresh(){this._request||(this._running=!0,this._request=ms.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(i=Date.now()){let t=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let l,a=o.length-1,r=!1;for(;a>=0;--a)l=o[a],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(i),r=!0):(o[a]=o[o.length-1],o.pop());r&&(n.draw(),this._notify(n,s,i,"progress")),o.length||(s.running=!1,this._notify(n,s,i,"complete"),s.initial=!1),t+=o.length}),this._lastDate=i,0===t&&(this._running=!1)}_getAnims(i){const t=this._charts;let s=t.get(i);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},t.set(i,s)),s}listen(i,t,s){this._getAnims(i).listeners[t].push(s)}add(i,t){!t||!t.length||this._getAnims(i).items.push(...t)}has(i){return this._getAnims(i).items.length>0}start(i){const t=this._charts.get(i);t&&(t.running=!0,t.start=Date.now(),t.duration=t.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(i){if(!this._running)return!1;const t=this._charts.get(i);return!(!t||!t.running||!t.items.length)}stop(i){const t=this._charts.get(i);if(!t||!t.items.length)return;const s=t.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();t.items=[],this._notify(i,t,Date.now(),"complete")}remove(i){return this._charts.delete(i)}}var Mt=new Qa;const Xs="transparent",Ja={boolean:(e,i,t)=>t>.5?i:e,color(e,i,t){const s=Ms(e||Xs),n=s.valid&&Ms(i||Xs);return n&&n.valid?n.mix(s,t).hexString():i},number:(e,i,t)=>e+(i-e)*t};class tr{constructor(i,t,s,n){const o=t[s];n=ge([i.to,n,o,i.from]);const a=ge([i.from,o,n]);this._active=!0,this._fn=i.fn||Ja[i.type||typeof a],this._easing=he[i.easing]||he.linear,this._start=Math.floor(Date.now()+(i.delay||0)),this._duration=this._total=Math.floor(i.duration),this._loop=!!i.loop,this._target=t,this._prop=s,this._from=a,this._to=n,this._promises=void 0}active(){return this._active}update(i,t,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,a=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(a,i.duration)),this._total+=o,this._loop=!!i.loop,this._to=ge([i.to,t,n,i.from]),this._from=ge([i.from,n,t])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(i){const t=i-this._start,s=this._duration,n=this._prop,o=this._from,a=this._loop,r=this._to;let l;if(this._active=o!==r&&(a||t<s),!this._active)return this._target[n]=r,void this._notify(!0);t<0?this._target[n]=o:(l=t/s%2,l=a&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,r,l))}wait(){const i=this._promises||(this._promises=[]);return new Promise((t,s)=>{i.push({res:t,rej:s})})}_notify(i){const t=i?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][t]()}}class Gs{constructor(i,t){this._chart=i,this._properties=new Map,this.configure(t)}configure(i){if(!T(i))return;const t=Object.keys(N.animation),s=this._properties;Object.getOwnPropertyNames(i).forEach(n=>{const o=i[n];if(!T(o))return;const a={};for(const r of t)a[r]=o[r];(V(o.properties)&&o.properties||[n]).forEach(r=>{(r===n||!s.has(r))&&s.set(r,a)})})}_animateOptions(i,t){const s=t.options,n=function ir(e,i){if(!i)return;let t=e.options;if(t)return t.$shared&&(e.options=t=Object.assign({},t,{$shared:!1,$animations:{}})),t;e.options=i}(i,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&function er(e,i){const t=[],s=Object.keys(i);for(let n=0;n<s.length;n++){const o=e[s[n]];o&&o.active()&&t.push(o.wait())}return Promise.all(t)}(i.options.$animations,s).then(()=>{i.options=s},()=>{}),o}_createAnimations(i,t){const s=this._properties,n=[],o=i.$animations||(i.$animations={}),a=Object.keys(t),r=Date.now();let l;for(l=a.length-1;l>=0;--l){const c=a[l];if("$"===c.charAt(0))continue;if("options"===c){n.push(...this._animateOptions(i,t));continue}const h=t[c];let d=o[c];const u=s.get(c);if(d){if(u&&d.active()){d.update(u,h,r);continue}d.cancel()}u&&u.duration?(o[c]=d=new tr(u,i,c,h),n.push(d)):i[c]=h}return n}update(i,t){if(0===this._properties.size)return void Object.assign(i,t);const s=this._createAnimations(i,t);return s.length?(Mt.add(this._chart,s),!0):void 0}}function Ks(e,i){const t=e&&e.options||{},s=t.reverse,n=void 0===t.min?i:0,o=void 0===t.max?i:0;return{start:s?o:n,end:s?n:o}}function qs(e,i){const t=[],s=e._getSortedDatasetMetas(i);let n,o;for(n=0,o=s.length;n<o;++n)t.push(s[n].index);return t}function Zs(e,i,t,s={}){const n=e.keys,o="single"===s.mode;let a,r,l,c;if(null===i)return;let h=!1;for(a=0,r=n.length;a<r;++a){if(l=+n[a],l===t){if(h=!0,s.all)continue;break}c=e.values[l],j(c)&&(o||0===i||pt(i)===pt(c))&&(i+=c)}return h||s.all?i:0}function Oi(e,i){const t=e&&e.options.stacked;return t||void 0===t&&void 0!==i.stack}function lr(e,i,t){const s=e[i]||(e[i]={});return s[t]||(s[t]={})}function Qs(e,i,t,s){for(const n of i.getMatchingVisibleMetas(s).reverse()){const o=e[n.index];if(t&&o>0||!t&&o<0)return n.index}return null}function Js(e,i){const{chart:t,_cachedMeta:s}=e,n=t._stacks||(t._stacks={}),{iScale:o,vScale:a,index:r}=s,l=o.axis,c=a.axis,h=function ar(e,i,t){return`${e.id}.${i.id}.${t.stack||t.type}`}(o,a,s),d=i.length;let u;for(let f=0;f<d;++f){const p=i[f],{[l]:g,[c]:m}=p;u=(p._stacks||(p._stacks={}))[c]=lr(n,h,g),u[r]=m,u._top=Qs(u,a,!0,s.type),u._bottom=Qs(u,a,!1,s.type),(u._visualValues||(u._visualValues={}))[r]=m}}function Ti(e,i){const t=e.scales;return Object.keys(t).filter(s=>t[s].axis===i).shift()}function pe(e,i){const t=e.controller.index,s=e.vScale&&e.vScale.axis;if(s){i=i||e._parsed;for(const n of i){const o=n._stacks;if(!o||void 0===o[s]||void 0===o[s][t])return;delete o[s][t],void 0!==o[s]._visualValues&&void 0!==o[s]._visualValues[t]&&delete o[s]._visualValues[t]}}}const Li=e=>"reset"===e||"none"===e,tn=(e,i)=>i?e:Object.assign({},e);let Tt=(()=>class e{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,s){this.chart=t,this._ctx=t.ctx,this.index=s,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Oi(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&pe(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,s=this._cachedMeta,n=this.getDataset(),o=(u,f,p,g)=>"x"===u?f:"r"===u?g:p,a=s.xAxisID=P(n.xAxisID,Ti(t,"x")),r=s.yAxisID=P(n.yAxisID,Ti(t,"y")),l=s.rAxisID=P(n.rAxisID,Ti(t,"r")),c=s.indexAxis,h=s.iAxisID=o(c,a,r,l),d=s.vAxisID=o(c,r,a,l);s.xScale=this.getScaleForId(a),s.yScale=this.getScaleForId(r),s.rScale=this.getScaleForId(l),s.iScale=this.getScaleForId(h),s.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const s=this._cachedMeta;return t===s.iScale?s.vScale:s.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&gs(this._data,this),t._stacked&&pe(t)}_dataCheck(){const t=this.getDataset(),s=t.data||(t.data=[]),n=this._data;if(T(s))this._data=function or(e,i){const{iScale:t,vScale:s}=i,n="x"===t.axis?"x":"y",o="x"===s.axis?"x":"y",a=Object.keys(e),r=new Array(a.length);let l,c,h;for(l=0,c=a.length;l<c;++l)h=a[l],r[l]={[n]:h,[o]:e[h]};return r}(s,this._cachedMeta);else if(n!==s){if(n){gs(n,this);const o=this._cachedMeta;pe(o),o._parsed=[]}s&&Object.isExtensible(s)&&function Ko(e,i){e._chartjs?e._chartjs.listeners.push(i):(Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[i]}}),fs.forEach(t=>{const s="_onData"+gi(t),n=e[t];Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value(...o){const a=n.apply(this,o);return e._chartjs.listeners.forEach(r=>{"function"==typeof r[s]&&r[s](...o)}),a}})}))}(s,this),this._syncList=[],this._data=s}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const s=this._cachedMeta,n=this.getDataset();let o=!1;this._dataCheck();const a=s._stacked;s._stacked=Oi(s.vScale,s),s.stack!==n.stack&&(o=!0,pe(s),s.stack=n.stack),this._resyncElements(t),(o||a!==s._stacked)&&(Js(this,s._parsed),s._stacked=Oi(s.vScale,s))}configure(){const t=this.chart.config,s=t.datasetScopeKeys(this._type),n=t.getOptionScopes(this.getDataset(),s,!0);this.options=t.createResolver(n,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,s){const{_cachedMeta:n,_data:o}=this,{iScale:a,_stacked:r}=n,l=a.axis;let d,u,f,c=0===t&&s===o.length||n._sorted,h=t>0&&n._parsed[t-1];if(!1===this._parsing)n._parsed=o,n._sorted=!0,f=o;else{f=V(o[t])?this.parseArrayData(n,o,t,s):T(o[t])?this.parseObjectData(n,o,t,s):this.parsePrimitiveData(n,o,t,s);const p=()=>null===u[l]||h&&u[l]<h[l];for(d=0;d<s;++d)n._parsed[d+t]=u=f[d],c&&(p()&&(c=!1),h=u);n._sorted=c}r&&Js(this,f)}parsePrimitiveData(t,s,n,o){const{iScale:a,vScale:r}=t,l=a.axis,c=r.axis,h=a.getLabels(),d=a===r,u=new Array(o);let f,p,g;for(f=0,p=o;f<p;++f)g=f+n,u[f]={[l]:d||a.parse(h[g],g),[c]:r.parse(s[g],g)};return u}parseArrayData(t,s,n,o){const{xScale:a,yScale:r}=t,l=new Array(o);let c,h,d,u;for(c=0,h=o;c<h;++c)d=c+n,u=s[d],l[c]={x:a.parse(u[0],d),y:r.parse(u[1],d)};return l}parseObjectData(t,s,n,o){const{xScale:a,yScale:r}=t,{xAxisKey:l="x",yAxisKey:c="y"}=this._parsing,h=new Array(o);let d,u,f,p;for(d=0,u=o;d<u;++d)f=d+n,p=s[f],h[d]={x:a.parse(Pt(p,l),f),y:r.parse(Pt(p,c),f)};return h}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,s,n){const a=this._cachedMeta,r=s[t.axis];return Zs({keys:qs(this.chart,!0),values:s._stacks[t.axis]._visualValues},r,a.index,{mode:n})}updateRangeFromParsed(t,s,n,o){const a=n[s.axis];let r=null===a?NaN:a;const l=o&&n._stacks[s.axis];o&&l&&(o.values=l,r=Zs(o,a,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,s){const n=this._cachedMeta,o=n._parsed,a=n._sorted&&t===n.iScale,r=o.length,l=this._getOtherScale(t),c=((e,i,t)=>e&&!i.hidden&&i._stacked&&{keys:qs(this.chart,!0),values:null})(s,n),h={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:u}=function rr(e){const{min:i,max:t,minDefined:s,maxDefined:n}=e.getUserBounds();return{min:s?i:Number.NEGATIVE_INFINITY,max:n?t:Number.POSITIVE_INFINITY}}(l);let f,p;function g(){p=o[f];const m=p[l.axis];return!j(p[t.axis])||d>m||u<m}for(f=0;f<r&&(g()||(this.updateRangeFromParsed(h,t,p,c),!a));++f);if(a)for(f=r-1;f>=0;--f)if(!g()){this.updateRangeFromParsed(h,t,p,c);break}return h}getAllParsedValues(t){const s=this._cachedMeta._parsed,n=[];let o,a,r;for(o=0,a=s.length;o<a;++o)r=s[o][t.axis],j(r)&&n.push(r);return n}getMaxOverflow(){return!1}getLabelAndValue(t){const s=this._cachedMeta,n=s.iScale,o=s.vScale,a=this.getParsed(t);return{label:n?""+n.getLabelForValue(a[n.axis]):"",value:o?""+o.getLabelForValue(a[o.axis]):""}}_update(t){const s=this._cachedMeta;this.update(t||"default"),s._clip=function nr(e){let i,t,s,n;return T(e)?(i=e.top,t=e.right,s=e.bottom,n=e.left):i=t=s=n=e,{top:i,right:t,bottom:s,left:n,disabled:!1===e}}(P(this.options.clip,function sr(e,i,t){if(!1===t)return!1;const s=Ks(e,t),n=Ks(i,t);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}(s.xScale,s.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,n=this._cachedMeta,o=n.data||[],a=this.chart.chartArea,r=[],l=this._drawStart||0,c=this._drawCount||o.length-l,h=this.options.drawActiveElementsOnTop;let d;for(n.dataset&&n.dataset.draw(t,a,l,c),d=l;d<l+c;++d){const u=o[d];u.hidden||(u.active&&h?r.push(u):u.draw(t,a))}for(d=0;d<r.length;++d)r[d].draw(t,a)}getStyle(t,s){const n=s?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(n):this.resolveDataElementOptions(t||0,n)}getContext(t,s,n){const o=this.getDataset();let a;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];a=r.$context||(r.$context=function hr(e,i,t){return Ot(e,{active:!1,dataIndex:i,parsed:void 0,raw:void 0,element:t,index:i,mode:"default",type:"data"})}(this.getContext(),t,r)),a.parsed=this.getParsed(t),a.raw=o.data[t],a.index=a.dataIndex=t}else a=this.$context||(this.$context=function cr(e,i){return Ot(e,{active:!1,dataset:void 0,datasetIndex:i,index:i,mode:"default",type:"dataset"})}(this.chart.getContext(),this.index)),a.dataset=o,a.index=a.datasetIndex=this.index;return a.active=!!s,a.mode=n,a}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,s){return this._resolveElementOptions(this.dataElementType.id,s,t)}_resolveElementOptions(t,s="default",n){const o="active"===s,a=this._cachedDataOpts,r=t+"-"+s,l=a[r],c=this.enableOptionSharing&&re(n);if(l)return tn(l,c);const h=this.chart.config,d=h.datasetElementScopeKeys(this._type,t),u=o?[`${t}Hover`,"hover",t,""]:[t,""],f=h.getOptionScopes(this.getDataset(),d),p=Object.keys(N.elements[t]),m=h.resolveNamedOptions(f,p,()=>this.getContext(n,o,s),u);return m.$shared&&(m.$shared=c,a[r]=Object.freeze(tn(m,c))),m}_resolveAnimations(t,s,n){const o=this.chart,a=this._cachedDataOpts,r=`animation-${s}`,l=a[r];if(l)return l;let c;if(!1!==o.options.animation){const d=this.chart.config,u=d.datasetAnimationScopeKeys(this._type,s),f=d.getOptionScopes(this.getDataset(),u);c=d.createResolver(f,this.getContext(t,n,s))}const h=new Gs(o,c&&c.animations);return c&&c._cacheable&&(a[r]=Object.freeze(h)),h}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,s){return!s||Li(t)||this.chart._animationsDisabled}_getSharedOptions(t,s){const n=this.resolveDataElementOptions(t,s),o=this._sharedOptions,a=this.getSharedOptions(n),r=this.includeOptions(s,a)||a!==o;return this.updateSharedOptions(a,s,n),{sharedOptions:a,includeOptions:r}}updateElement(t,s,n,o){Li(o)?Object.assign(t,n):this._resolveAnimations(s,o).update(t,n)}updateSharedOptions(t,s,n){t&&!Li(s)&&this._resolveAnimations(void 0,s).update(t,n)}_setStyle(t,s,n,o){t.active=o;const a=this.getStyle(s,o);this._resolveAnimations(s,n,o).update(t,{options:!o&&this.getSharedOptions(a)||a})}removeHoverStyle(t,s,n){this._setStyle(t,n,"active",!1)}setHoverStyle(t,s,n){this._setStyle(t,n,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const s=this._data,n=this._cachedMeta.data;for(const[l,c,h]of this._syncList)this[l](c,h);this._syncList=[];const o=n.length,a=s.length,r=Math.min(a,o);r&&this.parse(0,r),a>o?this._insertElements(o,a-o,t):a<o&&this._removeElements(a,o-a)}_insertElements(t,s,n=!0){const o=this._cachedMeta,a=o.data,r=t+s;let l;const c=h=>{for(h.length+=s,l=h.length-1;l>=r;l--)h[l]=h[l-s]};for(c(a),l=t;l<r;++l)a[l]=new this.dataElementType;this._parsing&&c(o._parsed),this.parse(t,s),n&&this.updateElements(a,t,s,"reset")}updateElements(t,s,n,o){}_removeElements(t,s){const n=this._cachedMeta;if(this._parsing){const o=n._parsed.splice(t,s);n._stacked&&pe(n,o)}n.data.splice(t,s)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[s,n,o]=t;this[s](n,o)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,s){s&&this._sync(["_removeElements",t,s]);const n=arguments.length-2;n&&this._sync(["_insertElements",t,n])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}})();function fr(e){const i=e.iScale,t=function ur(e,i){if(!e._cache.$bar){const t=e.getMatchingVisibleMetas(i);let s=[];for(let n=0,o=t.length;n<o;n++)s=s.concat(t[n].controller.getAllParsedValues(e));e._cache.$bar=ps(s.sort((n,o)=>n-o))}return e._cache.$bar}(i,e.type);let n,o,a,r,s=i._length;const l=()=>{32767===a||-32768===a||(re(r)&&(s=Math.min(s,Math.abs(a-r)||s)),r=a)};for(n=0,o=t.length;n<o;++n)a=i.getPixelForValue(t[n]),l();for(r=void 0,n=0,o=i.ticks.length;n<o;++n)a=i.getPixelForTick(n),l();return s}function en(e,i,t,s){return V(e)?function mr(e,i,t,s){const n=t.parse(e[0],s),o=t.parse(e[1],s),a=Math.min(n,o),r=Math.max(n,o);let l=a,c=r;Math.abs(a)>Math.abs(r)&&(l=r,c=a),i[t.axis]=c,i._custom={barStart:l,barEnd:c,start:n,end:o,min:a,max:r}}(e,i,t,s):i[t.axis]=t.parse(e,s),i}function sn(e,i,t,s){const n=e.iScale,o=e.vScale,a=n.getLabels(),r=n===o,l=[];let c,h,d,u;for(c=t,h=t+s;c<h;++c)u=i[c],d={},d[n.axis]=r||n.parse(a[c],c),l.push(en(u,d,o,c));return l}function Ri(e){return e&&void 0!==e.barStart&&void 0!==e.barEnd}function xr(e,i,t,s){let n=i.borderSkipped;const o={};if(!n)return void(e.borderSkipped=o);if(!0===n)return void(e.borderSkipped={top:!0,right:!0,bottom:!0,left:!0});const{start:a,end:r,reverse:l,top:c,bottom:h}=function _r(e){let i,t,s,n,o;return e.horizontal?(i=e.base>e.x,t="left",s="right"):(i=e.base<e.y,t="bottom",s="top"),i?(n="end",o="start"):(n="start",o="end"),{start:t,end:s,reverse:i,top:n,bottom:o}}(e);"middle"===n&&t&&(e.enableBorderRadius=!0,(t._top||0)===s?n=c:(t._bottom||0)===s?n=h:(o[nn(h,a,r,l)]=!0,n=c)),o[nn(n,a,r,l)]=!0,e.borderSkipped=o}function nn(e,i,t,s){return s?(e=function yr(e,i,t){return e===i?t:e===t?i:e}(e,i,t),e=on(e,t,i)):e=on(e,i,t),e}function on(e,i,t){return"start"===e?i:"end"===e?t:e}function vr(e,{inflateAmount:i},t){e.inflateAmount="auto"===i?1===t?.33:0:i}let Mr=(()=>class e extends Tt{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,s,n,o){return sn(t,s,n,o)}parseArrayData(t,s,n,o){return sn(t,s,n,o)}parseObjectData(t,s,n,o){const{iScale:a,vScale:r}=t,{xAxisKey:l="x",yAxisKey:c="y"}=this._parsing,h="x"===a.axis?l:c,d="x"===r.axis?l:c,u=[];let f,p,g,m;for(f=n,p=n+o;f<p;++f)m=s[f],g={},g[a.axis]=a.parse(Pt(m,h),f),u.push(en(Pt(m,d),g,r,f));return u}updateRangeFromParsed(t,s,n,o){super.updateRangeFromParsed(t,s,n,o);const a=n._custom;a&&s===this._cachedMeta.vScale&&(t.min=Math.min(t.min,a.min),t.max=Math.max(t.max,a.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const s=this._cachedMeta,{iScale:n,vScale:o}=s,a=this.getParsed(t),r=a._custom,l=Ri(r)?"["+r.start+", "+r.end+"]":""+o.getLabelForValue(a[o.axis]);return{label:""+n.getLabelForValue(a[n.axis]),value:l}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){const s=this._cachedMeta;this.updateElements(s.data,0,s.data.length,t)}updateElements(t,s,n,o){const a="reset"===o,{index:r,_cachedMeta:{vScale:l}}=this,c=l.getBasePixel(),h=l.isHorizontal(),d=this._getRuler(),{sharedOptions:u,includeOptions:f}=this._getSharedOptions(s,o);for(let p=s;p<s+n;p++){const g=this.getParsed(p),m=a||O(g[l.axis])?{base:c,head:c}:this._calculateBarValuePixels(p),b=this._calculateBarIndexPixels(p,d),_=(g._stacks||{})[l.axis],y={horizontal:h,base:m.base,enableBorderRadius:!_||Ri(g._custom)||r===_._top||r===_._bottom,x:h?m.head:b.center,y:h?b.center:m.head,height:h?b.size:Math.abs(m.size),width:h?Math.abs(m.size):b.size};f&&(y.options=u||this.resolveDataElementOptions(p,t[p].active?"active":o));const M=y.options||t[p].options;xr(y,M,_,r),vr(y,M,d.ratio),this.updateElement(t[p],p,y,o)}}_getStacks(t,s){const{iScale:n}=this._cachedMeta,o=n.getMatchingVisibleMetas(this._type).filter(d=>d.controller.options.grouped),a=n.options.stacked,r=[],l=this._cachedMeta.controller.getParsed(s),c=l&&l[n.axis],h=d=>{const u=d._parsed.find(p=>p[n.axis]===c),f=u&&u[d.vScale.axis];if(O(f)||isNaN(f))return!0};for(const d of o)if((void 0===s||!h(d))&&((!1===a||-1===r.indexOf(d.stack)||void 0===a&&void 0===d.stack)&&r.push(d.stack),d.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){const t=this.chart.scales,s=this.chart.options.indexAxis;return Object.keys(t).filter(n=>t[n].axis===s).shift()}_getAxis(){const t={},s=this.getFirstScaleIdForIndexAxis();for(const n of this.chart.data.datasets)t[P("x"===this.chart.options.indexAxis?n.xAxisID:n.yAxisID,s)]=!0;return Object.keys(t)}_getStackIndex(t,s,n){const o=this._getStacks(t,n),a=void 0!==s?o.indexOf(s):-1;return-1===a?o.length-1:a}_getRuler(){const t=this.options,s=this._cachedMeta,n=s.iScale,o=[];let a,r;for(a=0,r=s.data.length;a<r;++a)o.push(n.getPixelForValue(this.getParsed(a)[n.axis],a));const l=t.barThickness;return{min:l||fr(s),pixels:o,start:n._startPixel,end:n._endPixel,stackCount:this._getStackCount(),scale:n,grouped:t.grouped,ratio:l?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:s,_stacked:n,index:o},options:{base:a,minBarLength:r}}=this,l=a||0,c=this.getParsed(t),h=c._custom,d=Ri(h);let g,m,u=c[s.axis],f=0,p=n?this.applyStack(s,c,n):u;p!==u&&(f=p-u,p=u),d&&(u=h.barStart,p=h.barEnd-h.barStart,0!==u&&pt(u)!==pt(h.barEnd)&&(f=0),f+=u);const b=O(a)||d?f:a;let _=s.getPixelForValue(b);if(g=this.chart.getDataVisibility(t)?s.getPixelForValue(f+p):_,m=g-_,Math.abs(m)<r){m=function br(e,i,t){return 0!==e?pt(e):(i.isHorizontal()?1:-1)*(i.min>=t?1:-1)}(m,s,l)*r,u===l&&(_-=m/2);const y=s.getPixelForDecimal(0),M=s.getPixelForDecimal(1),x=Math.min(y,M),v=Math.max(y,M);_=Math.max(Math.min(_,v),x),g=_+m,n&&!d&&(c._stacks[s.axis]._visualValues[o]=s.getValueForPixel(g)-s.getValueForPixel(_))}if(_===s.getPixelForValue(l)){const y=pt(m)*s.getLineWidthForValue(l)/2;_+=y,m-=y}return{size:m,base:_,head:g,center:g+m/2}}_calculateBarIndexPixels(t,s){const n=s.scale,o=this.options,a=o.skipNull,r=P(o.maxBarThickness,1/0);let l,c;const h=this._getAxisCount();if(s.grouped){const d=a?this._getStackCount(t):s.stackCount,u="flex"===o.barThickness?function pr(e,i,t,s){const n=i.pixels,o=n[e];let a=e>0?n[e-1]:null,r=e<n.length-1?n[e+1]:null;const l=t.categoryPercentage;null===a&&(a=o-(null===r?i.end-i.start:r-o)),null===r&&(r=o+o-a);const c=o-(o-Math.min(a,r))/2*l;return{chunk:Math.abs(r-a)/2*l/s,ratio:t.barPercentage,start:c}}(t,s,o,d*h):function gr(e,i,t,s){const n=t.barThickness;let o,a;return O(n)?(o=i.min*t.categoryPercentage,a=t.barPercentage):(o=n*s,a=1),{chunk:o/s,ratio:a,start:i.pixels[e]-o/2}}(t,s,o,d*h),f="x"===this.chart.options.indexAxis?this.getDataset().xAxisID:this.getDataset().yAxisID,p=this._getAxis().indexOf(P(f,this.getFirstScaleIdForIndexAxis())),g=this._getStackIndex(this.index,this._cachedMeta.stack,a?t:void 0)+p;l=u.start+u.chunk*g+u.chunk/2,c=Math.min(r,u.chunk*u.ratio)}else l=n.getPixelForValue(this.getParsed(t)[n.axis],t),c=Math.min(r,s.min*s.ratio);return{base:l-c/2,head:l+c/2,center:l,size:c}}draw(){const t=this._cachedMeta,s=t.vScale,n=t.data,o=n.length;let a=0;for(;a<o;++a)null!==this.getParsed(a)[s.axis]&&!n[a].hidden&&n[a].draw(this._ctx)}})(),kr=(()=>class e extends Tt{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,s,n,o){const a=super.parsePrimitiveData(t,s,n,o);for(let r=0;r<a.length;r++)a[r]._custom=this.resolveDataElementOptions(r+n).radius;return a}parseArrayData(t,s,n,o){const a=super.parseArrayData(t,s,n,o);for(let r=0;r<a.length;r++)a[r]._custom=P(s[n+r][2],this.resolveDataElementOptions(r+n).radius);return a}parseObjectData(t,s,n,o){const a=super.parseObjectData(t,s,n,o);for(let r=0;r<a.length;r++){const l=s[n+r];a[r]._custom=P(l&&l.r&&+l.r,this.resolveDataElementOptions(r+n).radius)}return a}getMaxOverflow(){const t=this._cachedMeta.data;let s=0;for(let n=t.length-1;n>=0;--n)s=Math.max(s,t[n].size(this.resolveDataElementOptions(n))/2);return s>0&&s}getLabelAndValue(t){const s=this._cachedMeta,n=this.chart.data.labels||[],{xScale:o,yScale:a}=s,r=this.getParsed(t),l=o.getLabelForValue(r.x),c=a.getLabelForValue(r.y),h=r._custom;return{label:n[t]||"",value:"("+l+", "+c+(h?", "+h:"")+")"}}update(t){const s=this._cachedMeta.data;this.updateElements(s,0,s.length,t)}updateElements(t,s,n,o){const a="reset"===o,{iScale:r,vScale:l}=this._cachedMeta,{sharedOptions:c,includeOptions:h}=this._getSharedOptions(s,o),d=r.axis,u=l.axis;for(let f=s;f<s+n;f++){const p=t[f],g=!a&&this.getParsed(f),m={},b=m[d]=a?r.getPixelForDecimal(.5):r.getPixelForValue(g[d]),_=m[u]=a?l.getBasePixel():l.getPixelForValue(g[u]);m.skip=isNaN(b)||isNaN(_),h&&(m.options=c||this.resolveDataElementOptions(f,p.active?"active":o),a&&(m.options.radius=0)),this.updateElement(p,f,m,o)}}resolveDataElementOptions(t,s){const n=this.getParsed(t);let o=super.resolveDataElementOptions(t,s);o.$shared&&(o=Object.assign({},o,{$shared:!1}));const a=o.radius;return"active"!==s&&(o.radius=0),o.radius+=P(n&&n._custom,a),o}})(),Ei=(()=>class e extends Tt{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const s=t.data;if(s.labels.length&&s.datasets.length){const{labels:{pointStyle:n,color:o}}=t.legend.options;return s.labels.map((a,r)=>{const c=t.getDatasetMeta(0).controller.getStyle(r);return{text:a,fillStyle:c.backgroundColor,strokeStyle:c.borderColor,fontColor:o,lineWidth:c.borderWidth,pointStyle:n,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,s,n){n.chart.toggleDataVisibility(s.index),n.chart.update()}}}};constructor(t,s){super(t,s),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,s){const n=this.getDataset().data,o=this._cachedMeta;if(!1===this._parsing)o._parsed=n;else{let r,l,a=c=>+n[c];if(T(n[t])){const{key:c="value"}=this._parsing;a=h=>+Pt(n[h],c)}for(r=t,l=t+s;r<l;++r)o._parsed[r]=a(r)}}_getRotation(){return dt(this.options.rotation-90)}_getCircumference(){return dt(this.options.circumference)}_getRotationExtents(){let t=W,s=-W;for(let n=0;n<this.chart.data.datasets.length;++n)if(this.chart.isDatasetVisible(n)&&this.chart.getDatasetMeta(n).type===this._type){const o=this.chart.getDatasetMeta(n).controller,a=o._getRotation(),r=o._getCircumference();t=Math.min(t,a),s=Math.max(s,a+r)}return{rotation:t,circumference:s-t}}update(t){const s=this.chart,{chartArea:n}=s,o=this._cachedMeta,a=o.data,r=this.getMaxBorderWidth()+this.getMaxOffset(a)+this.options.spacing,l=Math.max((Math.min(n.width,n.height)-r)/2,0),c=Math.min(((e,i)=>"string"==typeof e&&e.endsWith("%")?parseFloat(e)/100:+e/i)(this.options.cutout,l),1),h=this._getRingWeight(this.index),{circumference:d,rotation:u}=this._getRotationExtents(),{ratioX:f,ratioY:p,offsetX:g,offsetY:m}=function Sr(e,i,t){let s=1,n=1,o=0,a=0;if(i<W){const r=e,l=r+i,c=Math.cos(r),h=Math.sin(r),d=Math.cos(l),u=Math.sin(l),f=(y,M,x)=>ce(y,r,l,!0)?1:Math.max(M,M*t,x,x*t),p=(y,M,x)=>ce(y,r,l,!0)?-1:Math.min(M,M*t,x,x*t),g=f(0,c,d),m=f($,h,u),b=p(E,c,d),_=p(E+$,h,u);s=(g-b)/2,n=(m-_)/2,o=-(g+b)/2,a=-(m+_)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:a}}(u,d,c),y=Math.max(Math.min((n.width-r)/f,(n.height-r)/p)/2,0),M=ns(this.options.radius,y),v=(M-Math.max(M*c,0))/this._getVisibleDatasetWeightTotal();this.offsetX=g*M,this.offsetY=m*M,o.total=this.calculateTotal(),this.outerRadius=M-v*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-v*h,0),this.updateElements(a,0,a.length,t)}_circumference(t,s){const n=this.options,o=this._cachedMeta,a=this._getCircumference();return s&&n.animation.animateRotate||!this.chart.getDataVisibility(t)||null===o._parsed[t]||o.data[t].hidden?0:this.calculateCircumference(o._parsed[t]*a/W)}updateElements(t,s,n,o){const a="reset"===o,r=this.chart,l=r.chartArea,d=(l.left+l.right)/2,u=(l.top+l.bottom)/2,f=a&&r.options.animation.animateScale,p=f?0:this.innerRadius,g=f?0:this.outerRadius,{sharedOptions:m,includeOptions:b}=this._getSharedOptions(s,o);let y,_=this._getRotation();for(y=0;y<s;++y)_+=this._circumference(y,a);for(y=s;y<s+n;++y){const M=this._circumference(y,a),x=t[y],v={x:d+this.offsetX,y:u+this.offsetY,startAngle:_,endAngle:_+M,circumference:M,outerRadius:g,innerRadius:p};b&&(v.options=m||this.resolveDataElementOptions(y,x.active?"active":o)),_+=M,this.updateElement(x,y,v,o)}}calculateTotal(){const t=this._cachedMeta,s=t.data;let o,n=0;for(o=0;o<s.length;o++){const a=t._parsed[o];null!==a&&!isNaN(a)&&this.chart.getDataVisibility(o)&&!s[o].hidden&&(n+=Math.abs(a))}return n}calculateCircumference(t){const s=this._cachedMeta.total;return s>0&&!isNaN(t)?W*(Math.abs(t)/s):0}getLabelAndValue(t){const n=this.chart,o=n.data.labels||[],a=de(this._cachedMeta._parsed[t],n.options.locale);return{label:o[t]||"",value:a}}getMaxBorderWidth(t){let s=0;const n=this.chart;let o,a,r,l,c;if(!t)for(o=0,a=n.data.datasets.length;o<a;++o)if(n.isDatasetVisible(o)){r=n.getDatasetMeta(o),t=r.data,l=r.controller;break}if(!t)return 0;for(o=0,a=t.length;o<a;++o)c=l.resolveDataElementOptions(o),"inner"!==c.borderAlign&&(s=Math.max(s,c.borderWidth||0,c.hoverBorderWidth||0));return s}getMaxOffset(t){let s=0;for(let n=0,o=t.length;n<o;++n){const a=this.resolveDataElementOptions(n);s=Math.max(s,a.offset||0,a.hoverOffset||0)}return s}_getRingWeightOffset(t){let s=0;for(let n=0;n<t;++n)this.chart.isDatasetVisible(n)&&(s+=this._getRingWeight(n));return s}_getRingWeight(t){return Math.max(P(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}})(),wr=(()=>class e extends Tt{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const s=this._cachedMeta,{dataset:n,data:o=[],_dataset:a}=s,r=this.chart._animationsDisabled;let{start:l,count:c}=_s(s,o,r);this._drawStart=l,this._drawCount=c,xs(s)&&(l=0,c=o.length),n._chart=this.chart,n._datasetIndex=this.index,n._decimated=!!a._decimated,n.points=o;const h=this.resolveDatasetElementOptions(t);this.options.showLine||(h.borderWidth=0),h.segment=this.options.segment,this.updateElement(n,void 0,{animated:!r,options:h},t),this.updateElements(o,l,c,t)}updateElements(t,s,n,o){const a="reset"===o,{iScale:r,vScale:l,_stacked:c,_dataset:h}=this._cachedMeta,{sharedOptions:d,includeOptions:u}=this._getSharedOptions(s,o),f=r.axis,p=l.axis,{spanGaps:g,segment:m}=this.options,b=qt(g)?g:Number.POSITIVE_INFINITY,_=this.chart._animationsDisabled||a||"none"===o,y=s+n,M=t.length;let x=s>0&&this.getParsed(s-1);for(let v=0;v<M;++v){const w=t[v],k=_?w:{};if(v<s||v>=y){k.skip=!0;continue}const C=this.getParsed(v),A=O(C[p]),D=k[f]=r.getPixelForValue(C[f],v),L=k[p]=a||A?l.getBasePixel():l.getPixelForValue(c?this.applyStack(l,C,c):C[p],v);k.skip=isNaN(D)||isNaN(L)||A,k.stop=v>0&&Math.abs(C[f]-x[f])>b,m&&(k.parsed=C,k.raw=h.data[v]),u&&(k.options=d||this.resolveDataElementOptions(v,w.active?"active":o)),_||this.updateElement(w,v,k,o),x=C}}getMaxOverflow(){const t=this._cachedMeta,s=t.dataset,n=s.options&&s.options.borderWidth||0,o=t.data||[];if(!o.length)return n;const a=o[0].size(this.resolveDataElementOptions(0)),r=o[o.length-1].size(this.resolveDataElementOptions(o.length-1));return Math.max(n,a,r)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}})(),an=(()=>class e extends Tt{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const s=t.data;if(s.labels.length&&s.datasets.length){const{labels:{pointStyle:n,color:o}}=t.legend.options;return s.labels.map((a,r)=>{const c=t.getDatasetMeta(0).controller.getStyle(r);return{text:a,fillStyle:c.backgroundColor,strokeStyle:c.borderColor,fontColor:o,lineWidth:c.borderWidth,pointStyle:n,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,s,n){n.chart.toggleDataVisibility(s.index),n.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,s){super(t,s),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const n=this.chart,o=n.data.labels||[],a=de(this._cachedMeta._parsed[t].r,n.options.locale);return{label:o[t]||"",value:a}}parseObjectData(t,s,n,o){return Es.bind(this)(t,s,n,o)}update(t){const s=this._cachedMeta.data;this._updateRadius(),this.updateElements(s,0,s.length,t)}getMinMax(){const s={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return this._cachedMeta.data.forEach((n,o)=>{const a=this.getParsed(o).r;!isNaN(a)&&this.chart.getDataVisibility(o)&&(a<s.min&&(s.min=a),a>s.max&&(s.max=a))}),s}_updateRadius(){const t=this.chart,s=t.chartArea,n=t.options,o=Math.min(s.right-s.left,s.bottom-s.top),a=Math.max(o/2,0),l=(a-Math.max(n.cutoutPercentage?a/100*n.cutoutPercentage:1,0))/t.getVisibleDatasetCount();this.outerRadius=a-l*this.index,this.innerRadius=this.outerRadius-l}updateElements(t,s,n,o){const a="reset"===o,r=this.chart,c=r.options.animation,h=this._cachedMeta.rScale,d=h.xCenter,u=h.yCenter,f=h.getIndexAngle(0)-.5*E;let g,p=f;const m=360/this.countVisibleElements();for(g=0;g<s;++g)p+=this._computeAngle(g,o,m);for(g=s;g<s+n;g++){const b=t[g];let _=p,y=p+this._computeAngle(g,o,m),M=r.getDataVisibility(g)?h.getDistanceFromCenterForValue(this.getParsed(g).r):0;p=y,a&&(c.animateScale&&(M=0),c.animateRotate&&(_=y=f));const x={x:d,y:u,innerRadius:0,outerRadius:M,startAngle:_,endAngle:y,options:this.resolveDataElementOptions(g,b.active?"active":o)};this.updateElement(b,g,x,o)}}countVisibleElements(){let s=0;return this._cachedMeta.data.forEach((n,o)=>{!isNaN(this.getParsed(o).r)&&this.chart.getDataVisibility(o)&&s++}),s}_computeAngle(t,s,n){return this.chart.getDataVisibility(t)?dt(this.resolveDataElementOptions(t,s).angle||n):0}})();var Cr=Object.freeze({__proto__:null,BarController:Mr,BubbleController:kr,DoughnutController:Ei,LineController:wr,PieController:(()=>class e extends Ei{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}})(),PolarAreaController:an,RadarController:(()=>class e extends Tt{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){const s=this._cachedMeta.vScale,n=this.getParsed(t);return{label:s.getLabels()[t],value:""+s.getLabelForValue(n[s.axis])}}parseObjectData(t,s,n,o){return Es.bind(this)(t,s,n,o)}update(t){const s=this._cachedMeta,n=s.dataset,o=s.data||[],a=s.iScale.getLabels();if(n.points=o,"resize"!==t){const r=this.resolveDatasetElementOptions(t);this.options.showLine||(r.borderWidth=0),this.updateElement(n,void 0,{_loop:!0,_fullLoop:a.length===o.length,options:r},t)}this.updateElements(o,0,o.length,t)}updateElements(t,s,n,o){const a=this._cachedMeta.rScale,r="reset"===o;for(let l=s;l<s+n;l++){const c=t[l],h=this.resolveDataElementOptions(l,c.active?"active":o),d=a.getPointPositionForValue(l,this.getParsed(l).r),u=r?a.xCenter:d.x,f=r?a.yCenter:d.y,p={x:u,y:f,angle:d.angle,skip:isNaN(u)||isNaN(f),options:h};this.updateElement(c,l,p,o)}}})(),ScatterController:(()=>class e extends Tt{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){const s=this._cachedMeta,n=this.chart.data.labels||[],{xScale:o,yScale:a}=s,r=this.getParsed(t),l=o.getLabelForValue(r.x),c=a.getLabelForValue(r.y);return{label:n[t]||"",value:"("+l+", "+c+")"}}update(t){const s=this._cachedMeta,{data:n=[]}=s,o=this.chart._animationsDisabled;let{start:a,count:r}=_s(s,n,o);if(this._drawStart=a,this._drawCount=r,xs(s)&&(a=0,r=n.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:l,_dataset:c}=s;l._chart=this.chart,l._datasetIndex=this.index,l._decimated=!!c._decimated,l.points=n;const h=this.resolveDatasetElementOptions(t);h.segment=this.options.segment,this.updateElement(l,void 0,{animated:!o,options:h},t)}else this.datasetElementType&&(delete s.dataset,this.datasetElementType=!1);this.updateElements(n,a,r,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,s,n,o){const a="reset"===o,{iScale:r,vScale:l,_stacked:c,_dataset:h}=this._cachedMeta,d=this.resolveDataElementOptions(s,o),u=this.getSharedOptions(d),f=this.includeOptions(o,u),p=r.axis,g=l.axis,{spanGaps:m,segment:b}=this.options,_=qt(m)?m:Number.POSITIVE_INFINITY,y=this.chart._animationsDisabled||a||"none"===o;let M=s>0&&this.getParsed(s-1);for(let x=s;x<s+n;++x){const v=t[x],w=this.getParsed(x),k=y?v:{},C=O(w[g]),A=k[p]=r.getPixelForValue(w[p],x),D=k[g]=a||C?l.getBasePixel():l.getPixelForValue(c?this.applyStack(l,w,c):w[g],x);k.skip=isNaN(A)||isNaN(D)||C,k.stop=x>0&&Math.abs(w[p]-M[p])>_,b&&(k.parsed=w,k.raw=h.data[x]),f&&(k.options=u||this.resolveDataElementOptions(x,v.active?"active":o)),y||this.updateElement(v,x,k,o),M=w}this.updateSharedOptions(u,o,d)}getMaxOverflow(){const t=this._cachedMeta,s=t.data||[];if(!this.options.showLine){let l=0;for(let c=s.length-1;c>=0;--c)l=Math.max(l,s[c].size(this.resolveDataElementOptions(c))/2);return l>0&&l}const n=t.dataset,o=n.options&&n.options.borderWidth||0;if(!s.length)return o;const a=s[0].size(this.resolveDataElementOptions(0)),r=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(o,a,r)/2}})()});function Ht(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Ii{static override(i){Object.assign(Ii.prototype,i)}options;constructor(i){this.options=i||{}}init(){}formats(){return Ht()}parse(){return Ht()}format(){return Ht()}add(){return Ht()}diff(){return Ht()}startOf(){return Ht()}endOf(){return Ht()}}var Pr__date=Ii;function Dr(e,i,t,s){const{controller:n,data:o,_sorted:a}=e,r=n._cachedMeta.iScale,l=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null;if(r&&i===r.axis&&"r"!==i&&a&&o.length){const c=r._reversePixels?Xo:yt;if(!s){const h=c(o,i,t);if(l){const{vScale:d}=n._cachedMeta,{_parsed:u}=e,f=u.slice(0,h.lo+1).reverse().findIndex(g=>!O(g[d.axis]));h.lo-=Math.max(0,f);const p=u.slice(h.hi).findIndex(g=>!O(g[d.axis]));h.hi+=Math.max(0,p)}return h}if(n._sharedOptions){const h=o[0],d="function"==typeof h.getRange&&h.getRange(i);if(d){const u=c(o,i,t-d),f=c(o,i,t+d);return{lo:u.lo,hi:f.hi}}}}return{lo:0,hi:o.length-1}}function me(e,i,t,s,n){const o=e.getSortedVisibleDatasetMetas(),a=t[i];for(let r=0,l=o.length;r<l;++r){const{index:c,data:h}=o[r],{lo:d,hi:u}=Dr(o[r],i,a,n);for(let f=d;f<=u;++f){const p=h[f];p.skip||s(p,c,f)}}}function Fi(e,i,t,s,n){const o=[];return!n&&!e.isPointInArea(i)||me(e,t,i,function(r,l,c){!n&&!vt(r,e.chartArea,0)||r.inRange(i.x,i.y,s)&&o.push({element:r,datasetIndex:l,index:c})},!0),o}function zi(e,i,t,s,n,o){return o||e.isPointInArea(i)?"r"!==t||s?function Tr(e,i,t,s,n,o){let a=[];const r=function Ar(e){const i=-1!==e.indexOf("x"),t=-1!==e.indexOf("y");return function(s,n){const o=i?Math.abs(s.x-n.x):0,a=t?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(a,2))}}(t);let l=Number.POSITIVE_INFINITY;return me(e,t,i,function c(h,d,u){const f=h.inRange(i.x,i.y,n);if(s&&!f)return;const p=h.getCenterPoint(n);if(!o&&!e.isPointInArea(p)&&!f)return;const m=r(i,p);m<l?(a=[{element:h,datasetIndex:d,index:u}],l=m):m===l&&a.push({element:h,datasetIndex:d,index:u})}),a}(e,i,t,s,n,o):function Or(e,i,t,s){let n=[];return me(e,t,i,function o(a,r,l){const{startAngle:c,endAngle:h}=a.getProps(["startAngle","endAngle"],s),{angle:d}=us(a,{x:i.x,y:i.y});ce(d,c,h)&&n.push({element:a,datasetIndex:r,index:l})}),n}(e,i,t,n):[]}function rn(e,i,t,s,n){const o=[],a="x"===t?"inXRange":"inYRange";let r=!1;return me(e,t,i,(l,c,h)=>{l[a]&&l[a](i[t],n)&&(o.push({element:l,datasetIndex:c,index:h}),r=r||l.inRange(i.x,i.y,n))}),s&&!r?[]:o}var Lr={evaluateInteractionItems:me,modes:{index(e,i,t,s){const n=Wt(i,e),o=t.axis||"x",a=t.includeInvisible||!1,r=t.intersect?Fi(e,n,o,s,a):zi(e,n,o,!1,s,a),l=[];return r.length?(e.getSortedVisibleDatasetMetas().forEach(c=>{const h=r[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(e,i,t,s){const n=Wt(i,e),o=t.axis||"xy",a=t.includeInvisible||!1;let r=t.intersect?Fi(e,n,o,s,a):zi(e,n,o,!1,s,a);if(r.length>0){const l=r[0].datasetIndex,c=e.getDatasetMeta(l).data;r=[];for(let h=0;h<c.length;++h)r.push({element:c[h],datasetIndex:l,index:h})}return r},point:(e,i,t,s)=>Fi(e,Wt(i,e),t.axis||"xy",s,t.includeInvisible||!1),nearest:(e,i,t,s)=>zi(e,Wt(i,e),t.axis||"xy",t.intersect,s,t.includeInvisible||!1),x:(e,i,t,s)=>rn(e,Wt(i,e),"x",t.intersect,s),y:(e,i,t,s)=>rn(e,Wt(i,e),"y",t.intersect,s)}};const ln=["left","top","right","bottom"];function be(e,i){return e.filter(t=>t.pos===i)}function cn(e,i){return e.filter(t=>-1===ln.indexOf(t.pos)&&t.box.axis===i)}function _e(e,i){return e.sort((t,s)=>{const n=i?s:t,o=i?t:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function hn(e,i,t,s){return Math.max(e[t],i[t])+Math.max(e[s],i[s])}function dn(e,i){e.top=Math.max(e.top,i.top),e.left=Math.max(e.left,i.left),e.bottom=Math.max(e.bottom,i.bottom),e.right=Math.max(e.right,i.right)}function zr(e,i,t,s){const{pos:n,box:o}=t,a=e.maxPadding;if(!T(n)){t.size&&(e[n]-=t.size);const d=s[t.stack]||{size:0,count:1};d.size=Math.max(d.size,t.horizontal?o.height:o.width),t.size=d.size/d.count,e[n]+=t.size}o.getPadding&&dn(a,o.getPadding());const r=Math.max(0,i.outerWidth-hn(a,e,"left","right")),l=Math.max(0,i.outerHeight-hn(a,e,"top","bottom")),c=r!==e.w,h=l!==e.h;return e.w=r,e.h=l,t.horizontal?{same:c,other:h}:{same:h,other:c}}function Vr(e,i){const t=i.maxPadding;return function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(a=>{o[a]=Math.max(i[a],t[a])}),o}(e?["left","right"]:["top","bottom"])}function xe(e,i,t,s){const n=[];let o,a,r,l,c,h;for(o=0,a=e.length,c=0;o<a;++o){r=e[o],l=r.box,l.update(r.width||i.w,r.height||i.h,Vr(r.horizontal,i));const{same:d,other:u}=zr(i,t,r,s);c|=d&&n.length,h=h||u,l.fullSize||n.push(r)}return c&&xe(n,i,t,s)||h}function je(e,i,t,s,n){e.top=t,e.left=i,e.right=i+s,e.bottom=t+n,e.width=s,e.height=n}function un(e,i,t,s){const n=t.padding;let{x:o,y:a}=i;for(const r of e){const l=r.box,c=s[r.stack]||{count:1,placed:0,weight:1},h=r.stackWeight/c.weight||1;if(r.horizontal){const d=i.w*h,u=c.size||l.height;re(c.start)&&(a=c.start),l.fullSize?je(l,n.left,a,t.outerWidth-n.right-n.left,u):je(l,i.left+c.placed,a,d,u),c.start=a,c.placed+=d,a=l.bottom}else{const d=i.h*h,u=c.size||l.width;re(c.start)&&(o=c.start),l.fullSize?je(l,o,n.top,u,t.outerHeight-n.bottom-n.top):je(l,o,i.top+c.placed,u,d),c.start=o,c.placed+=d,o=l.right}}i.x=o,i.y=a}var tt={addBox(e,i){e.boxes||(e.boxes=[]),i.fullSize=i.fullSize||!1,i.position=i.position||"top",i.weight=i.weight||0,i._layers=i._layers||function(){return[{z:0,draw(t){i.draw(t)}}]},e.boxes.push(i)},removeBox(e,i){const t=e.boxes?e.boxes.indexOf(i):-1;-1!==t&&e.boxes.splice(t,1)},configure(e,i,t){i.fullSize=t.fullSize,i.position=t.position,i.weight=t.weight},update(e,i,t,s){if(!e)return;const n=J(e.options.layout.padding),o=Math.max(i-n.width,0),a=Math.max(t-n.height,0),r=function Fr(e){const i=function Rr(e){const i=[];let t,s,n,o,a,r;for(t=0,s=(e||[]).length;t<s;++t)n=e[t],({position:o,options:{stack:a,stackWeight:r=1}}=n),i.push({index:t,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:a&&o+a,stackWeight:r});return i}(e),t=_e(i.filter(c=>c.box.fullSize),!0),s=_e(be(i,"left"),!0),n=_e(be(i,"right")),o=_e(be(i,"top"),!0),a=_e(be(i,"bottom")),r=cn(i,"x"),l=cn(i,"y");return{fullSize:t,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(a).concat(r),chartArea:be(i,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(a).concat(r)}}(e.boxes),l=r.vertical,c=r.horizontal;I(e.boxes,g=>{"function"==typeof g.beforeLayout&&g.beforeLayout()});const h=l.reduce((g,m)=>m.box.options&&!1===m.box.options.display?g:g+1,0)||1,d=Object.freeze({outerWidth:i,outerHeight:t,padding:n,availableWidth:o,availableHeight:a,vBoxMaxWidth:o/2/h,hBoxMaxHeight:a/2}),u=Object.assign({},n);dn(u,J(s));const f=Object.assign({maxPadding:u,w:o,h:a,x:n.left,y:n.top},n),p=function Ir(e,i){const t=function Er(e){const i={};for(const t of e){const{stack:s,pos:n,stackWeight:o}=t;if(!s||!ln.includes(n))continue;const a=i[s]||(i[s]={count:0,placed:0,weight:0,size:0});a.count++,a.weight+=o}return i}(e),{vBoxMaxWidth:s,hBoxMaxHeight:n}=i;let o,a,r;for(o=0,a=e.length;o<a;++o){r=e[o];const{fullSize:l}=r.box,c=t[r.stack],h=c&&r.stackWeight/c.weight;r.horizontal?(r.width=h?h*s:l&&i.availableWidth,r.height=n):(r.width=s,r.height=h?h*n:l&&i.availableHeight)}return t}(l.concat(c),d);xe(r.fullSize,f,d,p),xe(l,f,d,p),xe(c,f,d,p)&&xe(l,f,d,p),function Br(e){const i=e.maxPadding;function t(s){const n=Math.max(i[s]-e[s],0);return e[s]+=n,n}e.y+=t("top"),e.x+=t("left"),t("right"),t("bottom")}(f),un(r.leftAndTop,f,d,p),f.x+=f.w,f.y+=f.h,un(r.rightAndBottom,f,d,p),e.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},I(r.chartArea,g=>{const m=g.box;Object.assign(m,e.chartArea),m.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class fn{acquireContext(i,t){}releaseContext(i){return!1}addEventListener(i,t,s){}removeEventListener(i,t,s){}getDevicePixelRatio(){return 1}getMaximumSize(i,t,s,n){return t=Math.max(0,t||i.width),s=s||i.height,{width:t,height:Math.max(0,n?Math.floor(t/n):s)}}isAttached(i){return!0}updateConfig(i){}}class Wr extends fn{acquireContext(i){return i&&i.getContext&&i.getContext("2d")||null}updateConfig(i){i.options.animation=!1}}const $e="$chartjs",Nr={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},gn=e=>null===e||""===e,pn=!!Wa&&{passive:!0};function $r(e,i,t){e&&e.canvas&&e.canvas.removeEventListener(i,t,pn)}function Ye(e,i){for(const t of e)if(t===i||t.contains(i))return!0}function Ur(e,i,t){const s=e.canvas,n=new MutationObserver(o=>{let a=!1;for(const r of o)a=a||Ye(r.addedNodes,s),a=a&&!Ye(r.removedNodes,s);a&&t()});return n.observe(document,{childList:!0,subtree:!0}),n}function Xr(e,i,t){const s=e.canvas,n=new MutationObserver(o=>{let a=!1;for(const r of o)a=a||Ye(r.removedNodes,s),a=a&&!Ye(r.addedNodes,s);a&&t()});return n.observe(document,{childList:!0,subtree:!0}),n}const ye=new Map;let mn=0;function bn(){const e=window.devicePixelRatio;e!==mn&&(mn=e,ye.forEach((i,t)=>{t.currentDevicePixelRatio!==e&&i()}))}function qr(e,i,t){const s=e.canvas,n=s&&Ai(s);if(!n)return;const o=bs((r,l)=>{const c=n.clientWidth;t(r,l),c<n.clientWidth&&t()},window),a=new ResizeObserver(r=>{const l=r[0],c=l.contentRect.width,h=l.contentRect.height;0===c&&0===h||o(c,h)});return a.observe(n),function Gr(e,i){ye.size||window.addEventListener("resize",bn),ye.set(e,i)}(e,o),a}function Bi(e,i,t){t&&t.disconnect(),"resize"===i&&function Kr(e){ye.delete(e),ye.size||window.removeEventListener("resize",bn)}(e)}function Zr(e,i,t){const s=e.canvas,n=bs(o=>{null!==e.ctx&&t(function Yr(e,i){const t=Nr[e.type]||e.type,{x:s,y:n}=Wt(e,i);return{type:t,chart:i,native:e,x:void 0!==s?s:null,y:void 0!==n?n:null}}(o,e))},e);return function jr(e,i,t){e&&e.addEventListener(i,t,pn)}(s,i,n),n}class Qr extends fn{acquireContext(i,t){const s=i&&i.getContext&&i.getContext("2d");return s&&s.canvas===i?(function Hr(e,i){const t=e.style,s=e.getAttribute("height"),n=e.getAttribute("width");if(e[$e]={initial:{height:s,width:n,style:{display:t.display,height:t.height,width:t.width}}},t.display=t.display||"block",t.boxSizing=t.boxSizing||"border-box",gn(n)){const o=zs(e,"width");void 0!==o&&(e.width=o)}if(gn(s))if(""===e.style.height)e.height=e.width/(i||2);else{const o=zs(e,"height");void 0!==o&&(e.height=o)}}(i,t),s):null}releaseContext(i){const t=i.canvas;if(!t[$e])return!1;const s=t[$e].initial;["height","width"].forEach(o=>{const a=s[o];O(a)?t.removeAttribute(o):t.setAttribute(o,a)});const n=s.style||{};return Object.keys(n).forEach(o=>{t.style[o]=n[o]}),t.width=t.width,delete t[$e],!0}addEventListener(i,t,s){this.removeEventListener(i,t),(i.$proxies||(i.$proxies={}))[t]=({attach:Ur,detach:Xr,resize:qr}[t]||Zr)(i,t,s)}removeEventListener(i,t){const s=i.$proxies||(i.$proxies={}),n=s[t];n&&(({attach:Bi,detach:Bi,resize:Bi}[t]||$r)(i,t,n),s[t]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(i,t,s,n){return function Va(e,i,t,s){const n=We(e),o=Vt(n,"margin"),a=Ve(n.maxWidth,e,"clientWidth")||Le,r=Ve(n.maxHeight,e,"clientHeight")||Le,l=function Ba(e,i,t){let s,n;if(void 0===i||void 0===t){const o=e&&Ai(e);if(o){const a=o.getBoundingClientRect(),r=We(o),l=Vt(r,"border","width"),c=Vt(r,"padding");i=a.width-c.width-l.width,t=a.height-c.height-l.height,s=Ve(r.maxWidth,o,"clientWidth"),n=Ve(r.maxHeight,o,"clientHeight")}else i=e.clientWidth,t=e.clientHeight}return{width:i,height:t,maxWidth:s||Le,maxHeight:n||Le}}(e,i,t);let{width:c,height:h}=l;if("content-box"===n.boxSizing){const u=Vt(n,"border","width"),f=Vt(n,"padding");c-=f.width+u.width,h-=f.height+u.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=Ne(Math.min(c,a,l.maxWidth)),h=Ne(Math.min(h,r,l.maxHeight)),c&&!h&&(h=Ne(c/2)),(void 0!==i||void 0!==t)&&s&&l.height&&h>l.height&&(h=l.height,c=Ne(Math.floor(h*s))),{width:c,height:h}}(i,t,s,n)}isAttached(i){const t=i&&Ai(i);return!(!t||!t.isConnected)}}class kt{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(i){const{x:t,y:s}=this.getProps(["x","y"],i);return{x:t,y:s}}hasValue(){return qt(this.x)&&qt(this.y)}getProps(i,t){const s=this.$animations;if(!t||!s)return this;const n={};return i.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}function Ue(e,i,t,s,n){const o=P(s,0),a=Math.min(P(n,e.length),e.length);let l,c,h,r=0;for(t=Math.ceil(t),n&&(l=n-s,t=l/Math.floor(l/t)),h=o;h<0;)r++,h=Math.round(o+r*t);for(c=Math.max(o,0);c<a;c++)c===h&&(i.push(e[c]),r++,h=Math.round(o+r*t))}const _n=(e,i,t)=>"top"===i||"left"===i?e[i]+t:e[i]-t,xn=(e,i)=>Math.min(i||e,e);function yn(e,i){const t=[],s=e.length/i,n=e.length;let o=0;for(;o<n;o+=s)t.push(e[Math.floor(o)]);return t}function rl(e,i,t){const s=e.ticks.length,n=Math.min(i,s-1),o=e._startPixel,a=e._endPixel,r=1e-6;let c,l=e.getPixelForTick(n);if(!(t&&(c=1===s?Math.max(l-o,a-l):0===i?(e.getPixelForTick(1)-l)/2:(l-e.getPixelForTick(n-1))/2,l+=n<i?c:-c,l<o-r||l>a+r)))return l}function ve(e){return e.drawTicks?e.tickLength:0}function vn(e,i){if(!e.display)return 0;const t=X(e.font,i),s=J(e.padding);return(V(e.text)?e.text.length:1)*t.lineHeight+s.height}function dl(e,i,t){let s=_i(e);return(t&&"right"!==i||!t&&"right"===i)&&(s=(e=>"left"===e?"right":"right"===e?"left":e)(s)),s}class jt extends kt{constructor(i){super(),this.id=i.id,this.type=i.type,this.options=void 0,this.ctx=i.ctx,this.chart=i.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(i){this.options=i.setContext(this.getContext()),this.axis=i.axis,this._userMin=this.parse(i.min),this._userMax=this.parse(i.max),this._suggestedMin=this.parse(i.suggestedMin),this._suggestedMax=this.parse(i.suggestedMax)}parse(i,t){return i}getUserBounds(){let{_userMin:i,_userMax:t,_suggestedMin:s,_suggestedMax:n}=this;return i=ot(i,Number.POSITIVE_INFINITY),t=ot(t,Number.NEGATIVE_INFINITY),s=ot(s,Number.POSITIVE_INFINITY),n=ot(n,Number.NEGATIVE_INFINITY),{min:ot(i,s),max:ot(t,n),minDefined:j(i),maxDefined:j(t)}}getMinMax(i){let a,{min:t,max:s,minDefined:n,maxDefined:o}=this.getUserBounds();if(n&&o)return{min:t,max:s};const r=this.getMatchingVisibleMetas();for(let l=0,c=r.length;l<c;++l)a=r[l].controller.getMinMax(this,i),n||(t=Math.min(t,a.min)),o||(s=Math.max(s,a.max));return t=o&&t>s?s:t,s=n&&t>s?t:s,{min:ot(t,ot(s,t)),max:ot(s,ot(t,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const i=this.chart.data;return this.options.labels||(this.isHorizontal()?i.xLabels:i.yLabels)||i.labels||[]}getLabelItems(i=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(i))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){z(this.options.beforeUpdate,[this])}update(i,t,s){const{beginAtZero:n,grace:o,ticks:a}=this.options,r=a.sampleSize;this.beforeUpdate(),this.maxWidth=i,this.maxHeight=t,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function ba(e,i,t){const{min:s,max:n}=e,o=ns(i,(n-s)/2),a=(r,l)=>t&&0===r?0:r+l;return{min:a(s,-Math.abs(o)),max:a(n,o)}}(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=r<this.ticks.length;this._convertTicksToLabels(l?yn(this.ticks,r):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),a.display&&(a.autoSkip||"auto"===a.source)&&(this.ticks=function tl(e,i){const t=e.options.ticks,s=function el(e){const i=e.options.offset,t=e._tickSize();return Math.floor(Math.min(e._length/t+(i?0:1),e._maxLength/t))}(e),n=Math.min(t.maxTicksLimit||s,s),o=t.major.enabled?function sl(e){const i=[];let t,s;for(t=0,s=e.length;t<s;t++)e[t].major&&i.push(t);return i}(i):[],a=o.length,r=o[0],l=o[a-1],c=[];if(a>n)return function nl(e,i,t,s){let a,n=0,o=t[0];for(s=Math.ceil(s),a=0;a<e.length;a++)a===o&&(i.push(e[a]),n++,o=t[n*s])}(i,c,o,a/n),c;const h=function il(e,i,t){const s=function ol(e){const i=e.length;let t,s;if(i<2)return!1;for(s=e[0],t=1;t<i;++t)if(e[t]-e[t-1]!==s)return!1;return s}(e),n=i.length/t;if(!s)return Math.max(n,1);const o=function Ho(e){const i=[],t=Math.sqrt(e);let s;for(s=1;s<t;s++)e%s==0&&(i.push(s),i.push(e/s));return t===(0|t)&&i.push(t),i.sort((n,o)=>n-o).pop(),i}(s);for(let a=0,r=o.length-1;a<r;a++){const l=o[a];if(l>n)return l}return Math.max(n,1)}(o,i,n);if(a>0){let d,u;const f=a>1?Math.round((l-r)/(a-1)):null;for(Ue(i,c,h,O(f)?0:r-f,r),d=0,u=a-1;d<u;d++)Ue(i,c,h,o[d],o[d+1]);return Ue(i,c,h,l,O(f)?i.length:l+f),c}return Ue(i,c,h),c}(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,s,i=this.options.reverse;this.isHorizontal()?(t=this.left,s=this.right):(t=this.top,s=this.bottom,i=!i),this._startPixel=t,this._endPixel=s,this._reversePixels=i,this._length=s-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){z(this.options.afterUpdate,[this])}beforeSetDimensions(){z(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){z(this.options.afterSetDimensions,[this])}_callHooks(i){this.chart.notifyPlugins(i,this.getContext()),z(this.options[i],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){z(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(i){const t=this.options.ticks;let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s],o.label=z(t.callback,[o.value,s,i],this)}afterTickToLabelConversion(){z(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){z(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const i=this.options,t=i.ticks,s=xn(this.ticks.length,i.ticks.maxTicksLimit),n=t.minRotation||0,o=t.maxRotation;let r,l,c,a=n;if(!this._isVisible()||!t.display||n>=o||s<=1||!this.isHorizontal())return void(this.labelRotation=n);const h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=G(this.chart.width-d,0,this.maxWidth);r=i.offset?this.maxWidth/s:f/(s-1),d+6>r&&(r=f/(s-(i.offset?.5:1)),l=this.maxHeight-ve(i.grid)-t.padding-vn(i.title,this.chart.options.font),c=Math.sqrt(d*d+u*u),a=pi(Math.min(Math.asin(G((h.highest.height+6)/r,-1,1)),Math.asin(G(l/c,-1,1))-Math.asin(G(u/c,-1,1)))),a=Math.max(n,Math.min(o,a))),this.labelRotation=a}afterCalculateLabelRotation(){z(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){z(this.options.beforeFit,[this])}fit(){const i={width:0,height:0},{chart:t,options:{ticks:s,title:n,grid:o}}=this,a=this._isVisible(),r=this.isHorizontal();if(a){const l=vn(n,t.options.font);if(r?(i.width=this.maxWidth,i.height=ve(o)+l):(i.height=this.maxHeight,i.width=ve(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:u}=this._getLabelSizes(),f=2*s.padding,p=dt(this.labelRotation),g=Math.cos(p),m=Math.sin(p);r?i.height=Math.min(this.maxHeight,i.height+(s.mirror?0:m*d.width+g*u.height)+f):i.width=Math.min(this.maxWidth,i.width+(s.mirror?0:g*d.width+m*u.height)+f),this._calculatePadding(c,h,m,g)}}this._handleMargins(),r?(this.width=this._length=t.width-this._margins.left-this._margins.right,this.height=i.height):(this.width=i.width,this.height=this._length=t.height-this._margins.top-this._margins.bottom)}_calculatePadding(i,t,s,n){const{ticks:{align:o,padding:a},position:r}=this.options,l=0!==this.labelRotation,c="top"!==r&&"x"===this.axis;if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let u=0,f=0;l?c?(u=n*i.width,f=s*t.height):(u=s*i.height,f=n*t.width):"start"===o?f=t.width:"end"===o?u=i.width:"inner"!==o&&(u=i.width/2,f=t.width/2),this.paddingLeft=Math.max((u-h+a)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+a)*this.width/(this.width-d),0)}else{let h=t.height/2,d=i.height/2;"start"===o?(h=0,d=i.height):"end"===o&&(h=t.height,d=0),this.paddingTop=h+a,this.paddingBottom=d+a}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){z(this.options.afterFit,[this])}isHorizontal(){const{axis:i,position:t}=this.options;return"top"===t||"bottom"===t||"x"===i}isFullSize(){return this.options.fullSize}_convertTicksToLabels(i){let t,s;for(this.beforeTickToLabelConversion(),this.generateTickLabels(i),t=0,s=i.length;t<s;t++)O(i[t].label)&&(i.splice(t,1),s--,t--);this.afterTickToLabelConversion()}_getLabelSizes(){let i=this._labelSizes;if(!i){const t=this.options.ticks.sampleSize;let s=this.ticks;t<s.length&&(s=yn(s,t)),this._labelSizes=i=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return i}_computeLabelSizes(i,t,s){const{ctx:n,_longestTextCache:o}=this,a=[],r=[],l=Math.floor(t/xn(t,s));let d,u,f,p,g,m,b,_,y,M,x,c=0,h=0;for(d=0;d<t;d+=l){if(p=i[d].label,g=this._resolveTickFontOptions(d),n.font=m=g.string,b=o[m]=o[m]||{data:{},gc:[]},_=g.lineHeight,y=M=0,O(p)||V(p)){if(V(p))for(u=0,f=p.length;u<f;++u)x=p[u],!O(x)&&!V(x)&&(y=Ie(n,b.data,b.gc,y,x),M+=_)}else y=Ie(n,b.data,b.gc,y,p),M=_;a.push(y),r.push(M),c=Math.max(y,c),h=Math.max(M,h)}!function ll(e,i){I(e,t=>{const s=t.gc,n=s.length/2;let o;if(n>i){for(o=0;o<n;++o)delete t.data[s[o]];s.splice(0,n)}})}(o,t);const v=a.indexOf(c),w=r.indexOf(h),k=C=>({width:a[C]||0,height:r[C]||0});return{first:k(0),last:k(t-1),widest:k(v),highest:k(w),widths:a,heights:r}}getLabelForValue(i){return i}getPixelForValue(i,t){return NaN}getValueForPixel(i){}getPixelForTick(i){const t=this.ticks;return i<0||i>t.length-1?null:this.getPixelForValue(t[i].value)}getPixelForDecimal(i){this._reversePixels&&(i=1-i);const t=this._startPixel+i*this._length;return function Uo(e){return G(e,-32768,32767)}(this._alignToPixels?Ft(this.chart,t,0):t)}getDecimalForPixel(i){const t=(i-this._startPixel)/this._length;return this._reversePixels?1-t:t}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:i,max:t}=this;return i<0&&t<0?t:i>0&&t>0?i:0}getContext(i){const t=this.ticks||[];if(i>=0&&i<t.length){const s=t[i];return s.$context||(s.$context=function hl(e,i,t){return Ot(e,{tick:t,index:i,type:"tick"})}(this.getContext(),i,s))}return this.$context||(this.$context=function cl(e,i){return Ot(e,{scale:i,type:"scale"})}(this.chart.getContext(),this))}_tickSize(){const i=this.options.ticks,t=dt(this.labelRotation),s=Math.abs(Math.cos(t)),n=Math.abs(Math.sin(t)),o=this._getLabelSizes(),a=i.autoSkipPadding||0,r=o?o.widest.width+a:0,l=o?o.highest.height+a:0;return this.isHorizontal()?l*s>r*n?r/s:l/n:l*n<r*s?l/s:r/n}_isVisible(){const i=this.options.display;return"auto"!==i?!!i:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(i){const t=this.axis,s=this.chart,n=this.options,{grid:o,position:a,border:r}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),u=ve(o),f=[],p=r.setContext(this.getContext()),g=p.display?p.width:0,m=g/2,b=function(H){return Ft(s,H,g)};let _,y,M,x,v,w,k,C,A,D,L,q;if("top"===a)_=b(this.bottom),w=this.bottom-u,C=_-m,D=b(i.top)+m,q=i.bottom;else if("bottom"===a)_=b(this.top),D=i.top,q=b(i.bottom)-m,w=_+m,C=this.top+u;else if("left"===a)_=b(this.right),v=this.right-u,k=_-m,A=b(i.left)+m,L=i.right;else if("right"===a)_=b(this.left),A=i.left,L=b(i.right)-m,v=_+m,k=this.left+u;else if("x"===t){if("center"===a)_=b((i.top+i.bottom)/2+.5);else if(T(a)){const H=Object.keys(a)[0];_=b(this.chart.scales[H].getPixelForValue(a[H]))}D=i.top,q=i.bottom,w=_+m,C=w+u}else if("y"===t){if("center"===a)_=b((i.left+i.right)/2);else if(T(a)){const H=Object.keys(a)[0];_=b(this.chart.scales[H].getPixelForValue(a[H]))}v=_-m,k=v-u,A=i.left,L=i.right}const at=P(n.ticks.maxTicksLimit,d),F=Math.max(1,Math.ceil(d/at));for(y=0;y<d;y+=F){const H=this.getContext(y),Y=o.setContext(H),ut=r.setContext(H),et=Y.lineWidth,ee=Y.color,ni=ut.dash||[],ie=ut.dashOffset,we=Y.tickWidth,Yt=Y.tickColor,Ce=Y.tickBorderDash||[],Ut=Y.tickBorderDashOffset;M=rl(this,y,l),void 0!==M&&(x=Ft(s,M,et),c?v=k=A=L=x:w=C=D=q=x,f.push({tx1:v,ty1:w,tx2:k,ty2:C,x1:A,y1:D,x2:L,y2:q,width:et,color:ee,borderDash:ni,borderDashOffset:ie,tickWidth:we,tickColor:Yt,tickBorderDash:Ce,tickBorderDashOffset:Ut}))}return this._ticksLength=d,this._borderValue=_,f}_computeLabelItems(i){const t=this.axis,s=this.options,{position:n,ticks:o}=s,a=this.isHorizontal(),r=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,u=ve(s.grid),f=u+h,p=d?-h:f,g=-dt(this.labelRotation),m=[];let b,_,y,M,x,v,w,k,C,A,D,L,q="middle";if("top"===n)v=this.bottom-p,w=this._getXAxisLabelAlignment();else if("bottom"===n)v=this.top+p,w=this._getXAxisLabelAlignment();else if("left"===n){const F=this._getYAxisLabelAlignment(u);w=F.textAlign,x=F.x}else if("right"===n){const F=this._getYAxisLabelAlignment(u);w=F.textAlign,x=F.x}else if("x"===t){if("center"===n)v=(i.top+i.bottom)/2+f;else if(T(n)){const F=Object.keys(n)[0];v=this.chart.scales[F].getPixelForValue(n[F])+f}w=this._getXAxisLabelAlignment()}else if("y"===t){if("center"===n)x=(i.left+i.right)/2-f;else if(T(n)){const F=Object.keys(n)[0];x=this.chart.scales[F].getPixelForValue(n[F])}w=this._getYAxisLabelAlignment(u).textAlign}"y"===t&&("start"===l?q="top":"end"===l&&(q="bottom"));const at=this._getLabelSizes();for(b=0,_=r.length;b<_;++b){y=r[b],M=y.label;const F=o.setContext(this.getContext(b));k=this.getPixelForTick(b)+o.labelOffset,C=this._resolveTickFontOptions(b),A=C.lineHeight,D=V(M)?M.length:1;const H=D/2,Y=F.color,ut=F.textStrokeColor,et=F.textStrokeWidth;let ni,ee=w;if(a?(x=k,"inner"===w&&(ee=b===_-1?this.options.reverse?"left":"right":0===b?this.options.reverse?"right":"left":"center"),L="top"===n?"near"===c||0!==g?-D*A+A/2:"center"===c?-at.highest.height/2-H*A+A:A/2-at.highest.height:"near"===c||0!==g?A/2:"center"===c?at.highest.height/2-H*A:at.highest.height-D*A,d&&(L*=-1),0!==g&&!F.showLabelBackdrop&&(x+=A/2*Math.sin(g))):(v=k,L=(1-D)*A/2),F.showLabelBackdrop){const ie=J(F.backdropPadding),we=at.heights[b],Yt=at.widths[b];let Ce=L-ie.top,Ut=0-ie.left;switch(q){case"middle":Ce-=we/2;break;case"bottom":Ce-=we}switch(w){case"center":Ut-=Yt/2;break;case"right":Ut-=Yt;break;case"inner":b===_-1?Ut-=Yt:b>0&&(Ut-=Yt/2)}ni={left:Ut,top:Ce,width:Yt+ie.width,height:we+ie.height,color:F.backdropColor}}m.push({label:M,font:C,textOffset:L,options:{rotation:g,color:Y,strokeColor:ut,strokeWidth:et,textAlign:ee,textBaseline:q,translation:[x,v],backdrop:ni}})}return m}_getXAxisLabelAlignment(){const{position:i,ticks:t}=this.options;if(-dt(this.labelRotation))return"top"===i?"left":"right";let n="center";return"start"===t.align?n="left":"end"===t.align?n="right":"inner"===t.align&&(n="inner"),n}_getYAxisLabelAlignment(i){const{position:t,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=i+o,l=this._getLabelSizes().widest.width;let c,h;return"left"===t?n?(h=this.right+o,"near"===s?c="left":"center"===s?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-r,"near"===s?c="right":"center"===s?(c="center",h-=l/2):(c="left",h=this.left)):"right"===t?n?(h=this.left+o,"near"===s?c="right":"center"===s?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+r,"near"===s?c="left":"center"===s?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const i=this.chart,t=this.options.position;return"left"===t||"right"===t?{top:0,left:this.left,bottom:i.height,right:this.right}:"top"===t||"bottom"===t?{top:this.top,left:0,bottom:this.bottom,right:i.width}:void 0}drawBackground(){const{ctx:i,options:{backgroundColor:t},left:s,top:n,width:o,height:a}=this;t&&(i.save(),i.fillStyle=t,i.fillRect(s,n,o,a),i.restore())}getLineWidthForValue(i){const t=this.options.grid;if(!this._isVisible()||!t.display)return 0;const n=this.ticks.findIndex(o=>o.value===i);return n>=0?t.setContext(this.getContext(n)).lineWidth:0}drawGrid(i){const t=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(i));let o,a;const r=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(t.display)for(o=0,a=n.length;o<a;++o){const l=n[o];t.drawOnChartArea&&r({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),t.drawTicks&&r({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:i,ctx:t,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),a=s.display?o.width:0;if(!a)return;const r=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,u;this.isHorizontal()?(c=Ft(i,this.left,a)-a/2,h=Ft(i,this.right,r)+r/2,d=u=l):(d=Ft(i,this.top,a)-a/2,u=Ft(i,this.bottom,r)+r/2,c=h=l),t.save(),t.lineWidth=o.width,t.strokeStyle=o.color,t.beginPath(),t.moveTo(c,d),t.lineTo(h,u),t.stroke(),t.restore()}drawLabels(i){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&Fe(s,n);const o=this.getLabelItems(i);for(const a of o)zt(s,a.label,0,a.textOffset,a.font,a.options);n&&ze(s)}drawTitle(){const{ctx:i,options:{position:t,title:s,reverse:n}}=this;if(!s.display)return;const o=X(s.font),a=J(s.padding),r=s.align;let l=o.lineHeight/2;"bottom"===t||"center"===t||T(t)?(l+=a.bottom,V(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=a.top;const{titleX:c,titleY:h,maxWidth:d,rotation:u}=function ul(e,i,t,s){const{top:n,left:o,bottom:a,right:r,chart:l}=e,{chartArea:c,scales:h}=l;let u,f,p,d=0;const g=a-n,m=r-o;if(e.isHorizontal()){if(f=Q(s,o,r),T(t)){const b=Object.keys(t)[0];p=h[b].getPixelForValue(t[b])+g-i}else p="center"===t?(c.bottom+c.top)/2+g-i:_n(e,t,i);u=r-o}else{if(T(t)){const b=Object.keys(t)[0];f=h[b].getPixelForValue(t[b])-m+i}else f="center"===t?(c.left+c.right)/2-m+i:_n(e,t,i);p=Q(s,a,n),d="left"===t?-$:$}return{titleX:f,titleY:p,maxWidth:u,rotation:d}}(this,l,t,r);zt(i,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:u,textAlign:dl(r,t,n),textBaseline:"middle",translation:[c,h]})}draw(i){this._isVisible()&&(this.drawBackground(),this.drawGrid(i),this.drawBorder(),this.drawTitle(),this.drawLabels(i))}_layers(){const i=this.options,t=i.ticks&&i.ticks.z||0,s=P(i.grid&&i.grid.z,-1),n=P(i.border&&i.border.z,0);return this._isVisible()&&this.draw===jt.prototype.draw?[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:t,draw:o=>{this.drawLabels(o)}}]:[{z:t,draw:o=>{this.draw(o)}}]}getMatchingVisibleMetas(i){const t=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,a;for(o=0,a=t.length;o<a;++o){const r=t[o];r[s]===this.id&&(!i||r.type===i)&&n.push(r)}return n}_resolveTickFontOptions(i){return X(this.options.ticks.setContext(this.getContext(i)).font)}_maxDigits(){const i=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/i}}class Xe{constructor(i,t,s){this.type=i,this.scope=t,this.override=s,this.items=Object.create(null)}isForType(i){return Object.prototype.isPrototypeOf.call(this.type.prototype,i.prototype)}register(i){const t=Object.getPrototypeOf(i);let s;(function pl(e){return"id"in e&&"defaults"in e})(t)&&(s=this.register(t));const n=this.items,o=i.id,a=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+i);return o in n||(n[o]=i,function fl(e,i,t){const s=oe(Object.create(null),[t?N.get(t):{},N.get(i),e.defaults]);N.set(i,s),e.defaultRoutes&&function gl(e,i){Object.keys(i).forEach(t=>{const s=t.split("."),n=s.pop(),o=[e].concat(s).join("."),a=i[t].split("."),r=a.pop(),l=a.join(".");N.route(o,n,l,r)})}(i,e.defaultRoutes),e.descriptors&&N.describe(i,e.descriptors)}(i,a,s),this.override&&N.override(i.id,i.overrides)),a}get(i){return this.items[i]}unregister(i){const t=this.items,s=i.id,n=this.scope;s in t&&delete t[s],n&&s in N[n]&&(delete N[n][s],this.override&&delete It[s])}}class ml{constructor(){this.controllers=new Xe(Tt,"datasets",!0),this.elements=new Xe(kt,"elements"),this.plugins=new Xe(Object,"plugins"),this.scales=new Xe(jt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...i){this._each("register",i)}remove(...i){this._each("unregister",i)}addControllers(...i){this._each("register",i,this.controllers)}addElements(...i){this._each("register",i,this.elements)}addPlugins(...i){this._each("register",i,this.plugins)}addScales(...i){this._each("register",i,this.scales)}getController(i){return this._get(i,this.controllers,"controller")}getElement(i){return this._get(i,this.elements,"element")}getPlugin(i){return this._get(i,this.plugins,"plugin")}getScale(i){return this._get(i,this.scales,"scale")}removeControllers(...i){this._each("unregister",i,this.controllers)}removeElements(...i){this._each("unregister",i,this.elements)}removePlugins(...i){this._each("unregister",i,this.plugins)}removeScales(...i){this._each("unregister",i,this.scales)}_each(i,t,s){[...t].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(i,o,n):I(n,a=>{const r=s||this._getRegistryForType(a);this._exec(i,r,a)})})}_exec(i,t,s){const n=gi(i);z(s["before"+n],[],s),t[i](s),z(s["after"+n],[],s)}_getRegistryForType(i){for(let t=0;t<this._typedRegistries.length;t++){const s=this._typedRegistries[t];if(s.isForType(i))return s}return this.plugins}_get(i,t,s){const n=t.get(i);if(void 0===n)throw new Error('"'+i+'" is not a registered '+s+".");return n}}var mt=new ml;class bl{constructor(){this._init=[]}notify(i,t,s,n){"beforeInit"===t&&(this._init=this._createDescriptors(i,!0),this._notify(this._init,i,"install"));const o=n?this._descriptors(i).filter(n):this._descriptors(i),a=this._notify(o,i,t,s);return"afterDestroy"===t&&(this._notify(o,i,"stop"),this._notify(this._init,i,"uninstall")),a}_notify(i,t,s,n){n=n||{};for(const o of i){const a=o.plugin;if(!1===z(a[s],[t,n,o.options],a)&&n.cancelable)return!1}return!0}invalidate(){O(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(i){if(this._cache)return this._cache;const t=this._cache=this._createDescriptors(i);return this._notifyStateChanges(i),t}_createDescriptors(i,t){const s=i&&i.config,n=P(s.options&&s.options.plugins,{}),o=function _l(e){const i={},t=[],s=Object.keys(mt.plugins.items);for(let o=0;o<s.length;o++)t.push(mt.getPlugin(s[o]));const n=e.plugins||[];for(let o=0;o<n.length;o++){const a=n[o];-1===t.indexOf(a)&&(t.push(a),i[a.id]=!0)}return{plugins:t,localIds:i}}(s);return!1!==n||t?function yl(e,{plugins:i,localIds:t},s,n){const o=[],a=e.getContext();for(const r of i){const l=r.id,c=xl(s[l],n);null!==c&&o.push({plugin:r,options:vl(e.config,{plugin:r,local:t[l]},c,a)})}return o}(i,o,n,t):[]}_notifyStateChanges(i){const t=this._oldCache||[],s=this._cache,n=(o,a)=>o.filter(r=>!a.some(l=>r.plugin.id===l.plugin.id));this._notify(n(t,s),i,"stop"),this._notify(n(s,t),i,"start")}}function xl(e,i){return i||!1!==e?!0===e?{}:e:null}function vl(e,{plugin:i,local:t},s,n){const o=e.pluginScopeKeys(i),a=e.getOptionScopes(s,o);return t&&i.defaults&&a.push(i.defaults),e.createResolver(a,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Vi(e,i){return((i.datasets||{})[e]||{}).indexAxis||i.indexAxis||(N.datasets[e]||{}).indexAxis||"x"}function Mn(e){if("x"===e||"y"===e||"r"===e)return e}function Sl(e){return"top"===e||"bottom"===e?"x":"left"===e||"right"===e?"y":void 0}function Wi(e,...i){if(Mn(e))return e;for(const t of i){const s=t.axis||Sl(t.position)||e.length>1&&Mn(e[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${e}' axis. Please provide 'axis' or 'position' option.`)}function kn(e,i,t){if(t[i+"AxisID"]===e)return{axis:i}}function Sn(e){const i=e.options||(e.options={});i.plugins=P(i.plugins,{}),i.scales=function Cl(e,i){const t=It[e.type]||{scales:{}},s=i.scales||{},n=Vi(e.type,i),o=Object.create(null);return Object.keys(s).forEach(a=>{const r=s[a];if(!T(r))return console.error(`Invalid scale configuration for scale: ${a}`);if(r._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${a}`);const l=Wi(a,r,function wl(e,i){if(i.data&&i.data.datasets){const t=i.data.datasets.filter(s=>s.xAxisID===e||s.yAxisID===e);if(t.length)return kn(e,"x",t[0])||kn(e,"y",t[0])}return{}}(a,e),N.scales[r.type]),c=function kl(e,i){return e===i?"_index_":"_value_"}(l,n),h=t.scales||{};o[a]=ae(Object.create(null),[{axis:l},r,h[l],h[c]])}),e.data.datasets.forEach(a=>{const r=a.type||e.type,l=a.indexAxis||Vi(r,i),h=(It[r]||{}).scales||{};Object.keys(h).forEach(d=>{const u=function Ml(e,i){let t=e;return"_index_"===e?t=i:"_value_"===e&&(t="x"===i?"y":"x"),t}(d,l),f=a[u+"AxisID"]||u;o[f]=o[f]||Object.create(null),ae(o[f],[{axis:u},s[f],h[d]])})}),Object.keys(o).forEach(a=>{const r=o[a];ae(r,[N.scales[r.type],N.scale])}),o}(e,i)}function wn(e){return(e=e||{}).datasets=e.datasets||[],e.labels=e.labels||[],e}const Cn=new Map,Pn=new Set;function Ge(e,i){let t=Cn.get(e);return t||(t=i(),Cn.set(e,t),Pn.add(t)),t}const Me=(e,i,t)=>{const s=Pt(i,t);void 0!==s&&e.add(s)};class Dl{constructor(i){this._config=function Pl(e){return(e=e||{}).data=wn(e.data),Sn(e),e}(i),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(i){this._config.type=i}get data(){return this._config.data}set data(i){this._config.data=wn(i)}get options(){return this._config.options}set options(i){this._config.options=i}get plugins(){return this._config.plugins}update(){const i=this._config;this.clearCache(),Sn(i)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(i){return Ge(i,()=>[[`datasets.${i}`,""]])}datasetAnimationScopeKeys(i,t){return Ge(`${i}.transition.${t}`,()=>[[`datasets.${i}.transitions.${t}`,`transitions.${t}`],[`datasets.${i}`,""]])}datasetElementScopeKeys(i,t){return Ge(`${i}-${t}`,()=>[[`datasets.${i}.elements.${t}`,`datasets.${i}`,`elements.${t}`,""]])}pluginScopeKeys(i){const t=i.id;return Ge(`${this.type}-plugin-${t}`,()=>[[`plugins.${t}`,...i.additionalOptionScopes||[]]])}_cachedScopes(i,t){const s=this._scopeCache;let n=s.get(i);return(!n||t)&&(n=new Map,s.set(i,n)),n}getOptionScopes(i,t,s){const{options:n,type:o}=this,a=this._cachedScopes(i,s),r=a.get(t);if(r)return r;const l=new Set;t.forEach(h=>{i&&(l.add(i),h.forEach(d=>Me(l,i,d))),h.forEach(d=>Me(l,n,d)),h.forEach(d=>Me(l,It[o]||{},d)),h.forEach(d=>Me(l,N,d)),h.forEach(d=>Me(l,vi,d))});const c=Array.from(l);return 0===c.length&&c.push(Object.create(null)),Pn.has(t)&&a.set(t,c),c}chartOptionScopes(){const{options:i,type:t}=this;return[i,It[t]||{},N.datasets[t]||{},{type:t},N,vi]}resolveNamedOptions(i,t,s,n=[""]){const o={$shared:!0},{resolver:a,subPrefixes:r}=Dn(this._resolverCache,i,n);let l=a;(function Ol(e,i){const{isScriptable:t,isIndexable:s}=Ds(e);for(const n of i){const o=t(n),a=s(n),r=(a||o)&&e[n];if(o&&(Dt(r)||Al(r))||a&&V(r))return!0}return!1})(a,t)&&(o.$shared=!1,l=Zt(a,s=Dt(s)?s():s,this.createResolver(i,s,r)));for(const c of t)o[c]=l[c];return o}createResolver(i,t,s=[""],n){const{resolver:o}=Dn(this._resolverCache,i,s);return T(t)?Zt(o,t,void 0,n):o}}function Dn(e,i,t){let s=e.get(i);s||(s=new Map,e.set(i,s));const n=t.join();let o=s.get(n);return o||(o={resolver:wi(i,t),subPrefixes:t.filter(r=>!r.toLowerCase().includes("hover"))},s.set(n,o)),o}const Al=e=>T(e)&&Object.getOwnPropertyNames(e).some(i=>Dt(e[i])),Ll=["top","bottom","left","right","chartArea"];function An(e,i){return"top"===e||"bottom"===e||-1===Ll.indexOf(e)&&"x"===i}function On(e,i){return function(t,s){return t[e]===s[e]?t[i]-s[i]:t[e]-s[e]}}function Tn(e){const i=e.chart,t=i.options.animation;i.notifyPlugins("afterRender"),z(t&&t.onComplete,[e],i)}function Rl(e){const i=e.chart,t=i.options.animation;z(t&&t.onProgress,[e],i)}function Ln(e){return Di()&&"string"==typeof e?e=document.getElementById(e):e&&e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas),e}const Ke={},Rn=e=>{const i=Ln(e);return Object.values(Ke).filter(t=>t.canvas===i).pop()};function El(e,i,t){const s=Object.keys(e);for(const n of s){const o=+n;if(o>=i){const a=e[n];delete e[n],(t>0||o>i)&&(e[o+t]=a)}}}let Ni=(()=>class e{static defaults=N;static instances=Ke;static overrides=It;static registry=mt;static version="4.5.0";static getChart=Rn;static register(...t){mt.add(...t),En()}static unregister(...t){mt.remove(...t),En()}constructor(t,s){const n=this.config=new Dl(s),o=Ln(t),a=Rn(o);if(a)throw new Error("Canvas is already in use. Chart with ID '"+a.id+"' must be destroyed before the canvas with ID '"+a.canvas.id+"' can be reused.");const r=n.createResolver(n.chartOptionScopes(),this.getContext());this.platform=new(n.platform||function Jr(e){return!Di()||typeof OffscreenCanvas<"u"&&e instanceof OffscreenCanvas?Wr:Qr}(o)),this.platform.updateConfig(n);const l=this.platform.acquireContext(o,r.aspectRatio),c=l&&l.canvas,h=c&&c.height,d=c&&c.width;this.id=Ro(),this.ctx=l,this.canvas=c,this.width=d,this.height=h,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new bl,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function qo(e,i){let t;return function(...s){return i?(clearTimeout(t),t=setTimeout(e,i,s)):e.apply(this,s),i}}(u=>this.update(u),r.resizeDelay||0),this._dataChanges=[],Ke[this.id]=this,l&&c?(Mt.listen(this,"complete",Tn),Mt.listen(this,"progress",Rl),this._initialize(),this.attached&&this.update()):console.error("Failed to create chart: can't acquire context from the given item")}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:s},width:n,height:o,_aspectRatio:a}=this;return O(t)?s&&a?a:o?n/o:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return mt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Fs(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return ws(this.canvas,this.ctx),this}stop(){return Mt.stop(this),this}resize(t,s){Mt.running(this)?this._resizeBeforeDraw={width:t,height:s}:this._resize(t,s)}_resize(t,s){const n=this.options,r=this.platform.getMaximumSize(this.canvas,t,s,n.maintainAspectRatio&&this.aspectRatio),l=n.devicePixelRatio||this.platform.getDevicePixelRatio(),c=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Fs(this,l,!0)&&(this.notifyPlugins("resize",{size:r}),z(n.onResize,[this,r],this),this.attached&&this._doResize(c)&&this.render())}ensureScalesHaveIDs(){I(this.options.scales||{},(n,o)=>{n.id=o})}buildOrUpdateScales(){const t=this.options,s=t.scales,n=this.scales,o=Object.keys(n).reduce((r,l)=>(r[l]=!1,r),{});let a=[];s&&(a=a.concat(Object.keys(s).map(r=>{const l=s[r],c=Wi(r,l),h="r"===c,d="x"===c;return{options:l,dposition:h?"chartArea":d?"bottom":"left",dtype:h?"radialLinear":d?"category":"linear"}}))),I(a,r=>{const l=r.options,c=l.id,h=Wi(c,l),d=P(l.type,r.dtype);(void 0===l.position||An(l.position,h)!==An(r.dposition))&&(l.position=r.dposition),o[c]=!0;let u=null;c in n&&n[c].type===d?u=n[c]:(u=new(mt.getScale(d))({id:c,type:d,ctx:this.ctx,chart:this}),n[u.id]=u),u.init(l,t)}),I(o,(r,l)=>{r||delete n[l]}),I(n,r=>{tt.configure(this,r,r.options),tt.addBox(this,r)})}_updateMetasets(){const t=this._metasets,s=this.data.datasets.length,n=t.length;if(t.sort((o,a)=>o.index-a.index),n>s){for(let o=s;o<n;++o)this._destroyDatasetMeta(o);t.splice(s,n-s)}this._sortedMetasets=t.slice(0).sort(On("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:s}}=this;t.length>s.length&&delete this._stacks,t.forEach((n,o)=>{0===s.filter(a=>a===n._dataset).length&&this._destroyDatasetMeta(o)})}buildOrUpdateControllers(){const t=[],s=this.data.datasets;let n,o;for(this._removeUnreferencedMetasets(),n=0,o=s.length;n<o;n++){const a=s[n];let r=this.getDatasetMeta(n);const l=a.type||this.config.type;if(r.type&&r.type!==l&&(this._destroyDatasetMeta(n),r=this.getDatasetMeta(n)),r.type=l,r.indexAxis=a.indexAxis||Vi(l,this.options),r.order=a.order||0,r.index=n,r.label=""+a.label,r.visible=this.isDatasetVisible(n),r.controller)r.controller.updateIndex(n),r.controller.linkScales();else{const c=mt.getController(l),{datasetElementType:h,dataElementType:d}=N.datasets[l];Object.assign(c,{dataElementType:mt.getElement(d),datasetElementType:h&&mt.getElement(h)}),r.controller=new c(this,n),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){I(this.data.datasets,(t,s)=>{this.getDatasetMeta(s).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const s=this.config;s.update();const n=this._options=s.createResolver(s.chartOptionScopes(),this.getContext()),o=this._animationsDisabled=!n.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;const a=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let h=0,d=this.data.datasets.length;h<d;h++){const{controller:u}=this.getDatasetMeta(h),f=!o&&-1===a.indexOf(u);u.buildOrUpdateElements(f),r=Math.max(+u.getMaxOverflow(),r)}r=this._minPadding=n.layout.autoPadding?r:0,this._updateLayout(r),o||I(a,h=>{h.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(On("z","_idx"));const{_active:l,_lastEvent:c}=this;c?this._eventHandler(c,!0):l.length&&this._updateHoverStyles(l,l,!0),this.render()}_updateScales(){I(this.scales,t=>{tt.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,s=new Set(Object.keys(this._listeners)),n=new Set(t.events);(!rs(s,n)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,s=this._getUniformDataChanges()||[];for(const{method:n,start:o,count:a}of s)El(t,o,"_removeElements"===n?-a:a)}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const s=this.data.datasets.length,n=a=>new Set(t.filter(r=>r[0]===a).map((r,l)=>l+","+r.splice(1).join(","))),o=n(0);for(let a=1;a<s;a++)if(!rs(o,n(a)))return;return Array.from(o).map(a=>a.split(",")).map(a=>({method:a[1],start:+a[2],count:+a[3]}))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;tt.update(this,this.width,this.height,t);const s=this.chartArea,n=s.width<=0||s.height<=0;this._layers=[],I(this.boxes,o=>{n&&"chartArea"===o.position||(o.configure&&o.configure(),this._layers.push(...o._layers()))},this),this._layers.forEach((o,a)=>{o._idx=a}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let s=0,n=this.data.datasets.length;s<n;++s)this.getDatasetMeta(s).controller.configure();for(let s=0,n=this.data.datasets.length;s<n;++s)this._updateDataset(s,Dt(t)?t({datasetIndex:s}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,s){const n=this.getDatasetMeta(t),o={meta:n,index:t,mode:s,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",o)&&(n.controller._update(s),o.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",o))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(Mt.has(this)?this.attached&&!Mt.running(this)&&Mt.start(this):(this.draw(),Tn({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:n,height:o}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(n,o)}if(this.clear(),this.width<=0||this.height<=0||!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;const s=this._layers;for(t=0;t<s.length&&s[t].z<=0;++t)s[t].draw(this.chartArea);for(this._drawDatasets();t<s.length;++t)s[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const s=this._sortedMetasets,n=[];let o,a;for(o=0,a=s.length;o<a;++o){const r=s[o];(!t||r.visible)&&n.push(r)}return n}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;const t=this.getSortedVisibleDatasetMetas();for(let s=t.length-1;s>=0;--s)this._drawDataset(t[s]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const s=this.ctx,n={meta:t,index:t.index,cancelable:!0},o=Us(this,t);!1!==this.notifyPlugins("beforeDatasetDraw",n)&&(o&&Fe(s,o),t.controller.draw(),o&&ze(s),n.cancelable=!1,this.notifyPlugins("afterDatasetDraw",n))}isPointInArea(t){return vt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,s,n,o){const a=Lr.modes[s];return"function"==typeof a?a(this,t,n,o):[]}getDatasetMeta(t){const s=this.data.datasets[t],n=this._metasets;let o=n.filter(a=>a&&a._dataset===s).pop();return o||(o={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:s&&s.order||0,index:t,_dataset:s,_parsed:[],_sorted:!1},n.push(o)),o}getContext(){return this.$context||(this.$context=Ot(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const s=this.data.datasets[t];if(!s)return!1;const n=this.getDatasetMeta(t);return"boolean"==typeof n.hidden?!n.hidden:!s.hidden}setDatasetVisibility(t,s){this.getDatasetMeta(t).hidden=!s}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,s,n){const o=n?"show":"hide",a=this.getDatasetMeta(t),r=a.controller._resolveAnimations(void 0,o);re(s)?(a.data[s].hidden=!n,this.update()):(this.setDatasetVisibility(t,n),r.update(a,{visible:n}),this.update(l=>l.datasetIndex===t?o:void 0))}hide(t,s){this._updateVisibility(t,s,!1)}show(t,s){this._updateVisibility(t,s,!0)}_destroyDatasetMeta(t){const s=this._metasets[t];s&&s.controller&&s.controller._destroy(),delete this._metasets[t]}_stop(){let t,s;for(this.stop(),Mt.remove(this),t=0,s=this.data.datasets.length;t<s;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:s}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),ws(t,s),this.platform.releaseContext(s),this.canvas=null,this.ctx=null),delete Ke[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,s=this.platform,n=(a,r)=>{s.addEventListener(this,a,r),t[a]=r},o=(a,r,l)=>{a.offsetX=r,a.offsetY=l,this._eventHandler(a)};I(this.options.events,a=>n(a,o))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,s=this.platform,n=(c,h)=>{s.addEventListener(this,c,h),t[c]=h},o=(c,h)=>{t[c]&&(s.removeEventListener(this,c,h),delete t[c])},a=(c,h)=>{this.canvas&&this.resize(c,h)};let r;const l=()=>{o("attach",l),this.attached=!0,this.resize(),n("resize",a),n("detach",r)};r=()=>{this.attached=!1,o("resize",a),this._stop(),this._resize(0,0),n("attach",l)},s.isAttached(this.canvas)?l():r()}unbindEvents(){I(this._listeners,(t,s)=>{this.platform.removeEventListener(this,s,t)}),this._listeners={},I(this._responsiveListeners,(t,s)=>{this.platform.removeEventListener(this,s,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,s,n){const o=n?"set":"remove";let a,r,l,c;for("dataset"===s&&(a=this.getDatasetMeta(t[0].datasetIndex),a.controller["_"+o+"DatasetHoverStyle"]()),l=0,c=t.length;l<c;++l){r=t[l];const h=r&&this.getDatasetMeta(r.datasetIndex).controller;h&&h[o+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const s=this._active||[],n=t.map(({datasetIndex:a,index:r})=>{const l=this.getDatasetMeta(a);if(!l)throw new Error("No dataset found at index "+a);return{datasetIndex:a,element:l.data[r],index:r}});!Oe(n,s)&&(this._active=n,this._lastEvent=null,this._updateHoverStyles(n,s))}notifyPlugins(t,s,n){return this._plugins.notify(this,t,s,n)}isPluginEnabled(t){return 1===this._plugins._cache.filter(s=>s.plugin.id===t).length}_updateHoverStyles(t,s,n){const o=this.options.hover,a=(c,h)=>c.filter(d=>!h.some(u=>d.datasetIndex===u.datasetIndex&&d.index===u.index)),r=a(s,t),l=n?t:a(t,s);r.length&&this.updateHoverStyle(r,o.mode,!1),l.length&&o.mode&&this.updateHoverStyle(l,o.mode,!0)}_eventHandler(t,s){const n={event:t,replay:s,cancelable:!0,inChartArea:this.isPointInArea(t)},o=r=>(r.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",n,o))return;const a=this._handleEvent(t,s,n.inChartArea);return n.cancelable=!1,this.notifyPlugins("afterEvent",n,o),(a||n.changed)&&this.render(),this}_handleEvent(t,s,n){const{_active:o=[],options:a}=this,l=this._getActiveElements(t,o,n,s),c=function Vo(e){return"mouseup"===e.type||"click"===e.type||"contextmenu"===e.type}(t),h=function Il(e,i,t,s){return t&&"mouseout"!==e.type?s?i:e:null}(t,this._lastEvent,n,c);n&&(this._lastEvent=null,z(a.onHover,[t,l,this],this),c&&z(a.onClick,[t,l,this],this));const d=!Oe(l,o);return(d||s)&&(this._active=l,this._updateHoverStyles(l,o,s)),this._lastEvent=h,d}_getActiveElements(t,s,n,o){if("mouseout"===t.type)return[];if(!n)return s;const a=this.options.hover;return this.getElementsAtEventForMode(t,a.mode,a,o)}})();function En(){return I(Ni.instances,e=>e._plugins.invalidate())}function te(e,i,t,s){return{x:t+e*Math.cos(i),y:s+e*Math.sin(i)}}function qe(e,i,t,s,n,o){const{x:a,y:r,startAngle:l,pixelMargin:c,innerRadius:h}=i,d=Math.max(i.outerRadius+s+t-c,0),u=h>0?h+s+t+c:0;let f=0;const p=n-l;if(s){const Y=((h>0?h-s:0)+(d>0?d-s:0))/2;f=(p-(0!==Y?p*Y/(Y+s):p))/2}const m=(p-Math.max(.001,p*d-t/E)/d)/2,b=l+m+f,_=n-m-f,{outerStart:y,outerEnd:M,innerStart:x,innerEnd:v}=function Vl(e,i,t,s){const n=function Bl(e){return Si(e,["outerStart","outerEnd","innerStart","innerEnd"])}(e.options.borderRadius),o=(t-i)/2,a=Math.min(o,s*i/2),r=l=>{const c=(t-Math.min(o,l))*s/2;return G(l,0,Math.min(o,c))};return{outerStart:r(n.outerStart),outerEnd:r(n.outerEnd),innerStart:G(n.innerStart,0,a),innerEnd:G(n.innerEnd,0,a)}}(i,u,d,_-b),w=d-y,k=d-M,C=b+y/w,A=_-M/k,D=u+x,L=u+v,q=b+x/D,at=_-v/L;if(e.beginPath(),o){const F=(C+A)/2;if(e.arc(a,r,d,C,F),e.arc(a,r,d,F,A),M>0){const et=te(k,A,a,r);e.arc(et.x,et.y,M,A,_+$)}const H=te(L,_,a,r);if(e.lineTo(H.x,H.y),v>0){const et=te(L,at,a,r);e.arc(et.x,et.y,v,_+$,at+Math.PI)}const Y=(_-v/u+(b+x/u))/2;if(e.arc(a,r,u,_-v/u,Y,!0),e.arc(a,r,u,Y,b+x/u,!0),x>0){const et=te(D,q,a,r);e.arc(et.x,et.y,x,q+Math.PI,b-$)}const ut=te(w,b,a,r);if(e.lineTo(ut.x,ut.y),y>0){const et=te(w,C,a,r);e.arc(et.x,et.y,y,b-$,C)}}else{e.moveTo(a,r);const F=Math.cos(C)*d+a,H=Math.sin(C)*d+r;e.lineTo(F,H);const Y=Math.cos(A)*d+a,ut=Math.sin(A)*d+r;e.lineTo(Y,ut)}e.closePath()}function In(e,i,t=i){e.lineCap=P(t.borderCapStyle,i.borderCapStyle),e.setLineDash(P(t.borderDash,i.borderDash)),e.lineDashOffset=P(t.borderDashOffset,i.borderDashOffset),e.lineJoin=P(t.borderJoinStyle,i.borderJoinStyle),e.lineWidth=P(t.borderWidth,i.borderWidth),e.strokeStyle=P(t.borderColor,i.borderColor)}function jl(e,i,t){e.lineTo(t.x,t.y)}function Fn(e,i,t={}){const s=e.length,{start:n=0,end:o=s-1}=t,{start:a,end:r}=i,l=Math.max(n,a),c=Math.min(o,r);return{count:s,start:l,loop:i.loop,ilen:c<l&&!(n<a&&o<a||n>r&&o>r)?s+c-l:c-l}}function Yl(e,i,t,s){const{points:n,options:o}=i,{count:a,start:r,loop:l,ilen:c}=Fn(n,t,s),h=function $l(e){return e.stepped?la:e.tension||"monotone"===e.cubicInterpolationMode?ca:jl}(o);let f,p,g,{move:d=!0,reverse:u}=s||{};for(f=0;f<=c;++f)p=n[(r+(u?c-f:f))%a],!p.skip&&(d?(e.moveTo(p.x,p.y),d=!1):h(e,g,p,u,o.stepped),g=p);return l&&(p=n[(r+(u?c:0))%a],h(e,g,p,u,o.stepped)),!!l}function Ul(e,i,t,s){const n=i.points,{count:o,start:a,ilen:r}=Fn(n,t,s),{move:l=!0,reverse:c}=s||{};let u,f,p,g,m,b,h=0,d=0;const _=M=>(a+(c?r-M:M))%o,y=()=>{g!==m&&(e.lineTo(h,m),e.lineTo(h,g),e.lineTo(h,b))};for(l&&(f=n[_(0)],e.moveTo(f.x,f.y)),u=0;u<=r;++u){if(f=n[_(u)],f.skip)continue;const M=f.x,x=f.y,v=0|M;v===p?(x<g?g=x:x>m&&(m=x),h=(d*h+M)/++d):(y(),e.lineTo(M,x),p=v,d=0,g=m=x),b=x}y()}function Hi(e){const i=e.options;return e._decimated||e._loop||i.tension||"monotone"===i.cubicInterpolationMode||i.stepped||i.borderDash&&i.borderDash.length?Yl:Ul}const ql="function"==typeof Path2D;let Ze=(()=>class e extends kt{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,s){const n=this.options;!n.tension&&"monotone"!==n.cubicInterpolationMode||n.stepped||this._pointsUpdated||(Ra(this._points,n,t,n.spanGaps?this._loop:this._fullLoop,s),this._pointsUpdated=!0)}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function Ga(e,i){const t=e.points,s=e.options.spanGaps,n=t.length;if(!n)return[];const o=!!e._loop,{start:a,end:r}=function Ua(e,i,t,s){let n=0,o=i-1;if(t&&!s)for(;n<i&&!e[n].skip;)n++;for(;n<i&&e[n].skip;)n++;for(n%=i,t&&(o+=n);o>n&&e[o%i].skip;)o--;return o%=i,{start:n,end:o}}(t,n,o,s);return function $s(e,i,t,s){return s&&s.setContext&&t?function Ka(e,i,t,s){const n=e._chart.getContext(),o=Ys(e.options),{_datasetIndex:a,options:{spanGaps:r}}=e,l=t.length,c=[];let h=o,d=i[0].start,u=d;function f(p,g,m,b){const _=r?-1:1;if(p!==g){for(p+=l;t[p%l].skip;)p-=_;for(;t[g%l].skip;)g+=_;p%l!=g%l&&(c.push({start:p%l,end:g%l,loop:m,style:b}),h=b,d=g%l)}}for(const p of i){d=r?d:p.start;let m,g=t[d%l];for(u=d+1;u<=p.end;u++){const b=t[u%l];m=Ys(s.setContext(Ot(n,{type:"segment",p0:g,p1:b,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:a}))),qa(m,h)&&f(d,u-1,p.loop,h),g=b,h=m}d<u-1&&f(d,u-1,p.loop,h)}return c}(e,i,t,s):i}(e,!0===s?[{start:a,end:r,loop:o}]:function Xa(e,i,t,s){const n=e.length,o=[];let l,a=i,r=e[i];for(l=i+1;l<=t;++l){const c=e[l%n];c.skip||c.stop?r.skip||(o.push({start:i%n,end:(l-1)%n,loop:s=!1}),i=a=c.stop?l:null):(a=l,r.skip&&(i=l)),r=c}return null!==a&&o.push({start:i%n,end:a%n,loop:s}),o}(t,a,r<a?r+n:r,!!e._fullLoop&&0===a&&r===n-1),t,i)}(this,this.options.segment))}first(){const t=this.segments;return t.length&&this.points[t[0].start]}last(){const t=this.segments,n=t.length;return n&&this.points[t[n-1].end]}interpolate(t,s){const n=this.options,o=t[s],a=this.points,r=js(this,{property:s,start:o,end:o});if(!r.length)return;const l=[],c=function Xl(e){return e.stepped?Na:e.tension||"monotone"===e.cubicInterpolationMode?Ha:Nt}(n);let h,d;for(h=0,d=r.length;h<d;++h){const{start:u,end:f}=r[h],p=a[u],g=a[f];if(p===g){l.push(p);continue}const b=c(p,g,Math.abs((o-p[s])/(g[s]-p[s])),n.stepped);b[s]=t[s],l.push(b)}return 1===l.length?l[0]:l}pathSegment(t,s,n){return Hi(this)(t,this,s,n)}path(t,s,n){const o=this.segments,a=Hi(this);let r=this._loop;s=s||0,n=n||this.points.length-s;for(const l of o)r&=a(t,this,l,{start:s,end:s+n-1});return!!r}draw(t,s,n,o){(this.points||[]).length&&(this.options||{}).borderWidth&&(t.save(),function Zl(e,i,t,s){ql&&!i.options.segment?function Gl(e,i,t,s){let n=i._path;n||(n=i._path=new Path2D,i.path(n,t,s)&&n.closePath()),In(e,i.options),e.stroke(n)}(e,i,t,s):function Kl(e,i,t,s){const{segments:n,options:o}=i,a=Hi(i);for(const r of n)In(e,o,r.style),e.beginPath(),a(e,i,r,{start:t,end:t+s-1})&&e.closePath(),e.stroke()}(e,i,t,s)}(t,this,n,o),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}})();function zn(e,i,t,s){const n=e.options,{[t]:o}=e.getProps([t],s);return Math.abs(i-o)<n.radius+n.hitRadius}let Ql=(()=>class e extends kt{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,s,n){const o=this.options,{x:a,y:r}=this.getProps(["x","y"],n);return Math.pow(t-a,2)+Math.pow(s-r,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(t,s){return zn(this,t,"x",s)}inYRange(t,s){return zn(this,t,"y",s)}getCenterPoint(t){const{x:s,y:n}=this.getProps(["x","y"],t);return{x:s,y:n}}size(t){let s=(t=t||this.options||{}).radius||0;return s=Math.max(s,s&&t.hoverRadius||0),2*(s+(s&&t.borderWidth||0))}draw(t,s){const n=this.options;this.skip||n.radius<.1||!vt(this,s,this.size(n)/2)||(t.strokeStyle=n.borderColor,t.lineWidth=n.borderWidth,t.fillStyle=n.backgroundColor,ki(t,n,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}})();function Bn(e,i){const{x:t,y:s,base:n,width:o,height:a}=e.getProps(["x","y","base","width","height"],i);let r,l,c,h,d;return e.horizontal?(d=a/2,r=Math.min(t,n),l=Math.max(t,n),c=s-d,h=s+d):(d=o/2,r=t-d,l=t+d,c=Math.min(s,n),h=Math.max(s,n)),{left:r,top:c,right:l,bottom:h}}function Lt(e,i,t,s){return e?0:G(i,t,s)}function ji(e,i,t,s){const n=null===i,o=null===t,r=e&&!(n&&o)&&Bn(e,s);return r&&(n||xt(i,r.left,r.right))&&(o||xt(t,r.top,r.bottom))}function sc(e,i){e.rect(i.x,i.y,i.w,i.h)}function $i(e,i,t={}){const s=e.x!==t.x?-i:0,n=e.y!==t.y?-i:0;return{x:e.x+s,y:e.y+n,w:e.w+((e.x+e.w!==t.x+t.w?i:0)-s),h:e.h+((e.y+e.h!==t.y+t.h?i:0)-n),radius:e.radius}}var oc=Object.freeze({__proto__:null,ArcElement:class Hl extends kt{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:i=>"borderDash"!==i};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(i){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,i&&Object.assign(this,i)}inRange(i,t,s){const n=this.getProps(["x","y"],s),{angle:o,distance:a}=us(n,{x:i,y:t}),{startAngle:r,endAngle:l,innerRadius:c,outerRadius:h,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),u=(this.options.spacing+this.options.borderWidth)/2,f=P(d,l-r),p=ce(o,r,l)&&r!==l,g=f>=W||p,m=xt(a,c+u,h+u);return g&&m}getCenterPoint(i){const{x:t,y:s,startAngle:n,endAngle:o,innerRadius:a,outerRadius:r}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],i),{offset:l,spacing:c}=this.options,h=(n+o)/2,d=(a+r+c+l)/2;return{x:t+Math.cos(h)*d,y:s+Math.sin(h)*d}}tooltipPosition(i){return this.getCenterPoint(i)}draw(i){const{options:t,circumference:s}=this,n=(t.offset||0)/4,o=(t.spacing||0)/2,a=t.circular;if(this.pixelMargin="inner"===t.borderAlign?.33:0,this.fullCircles=s>W?Math.floor(s/W):0,0===s||this.innerRadius<0||this.outerRadius<0)return;i.save();const r=(this.startAngle+this.endAngle)/2;i.translate(Math.cos(r)*n,Math.sin(r)*n);const c=n*(1-Math.sin(Math.min(E,s||0)));i.fillStyle=t.backgroundColor,i.strokeStyle=t.borderColor,function Wl(e,i,t,s,n){const{fullCircles:o,startAngle:a,circumference:r}=i;let l=i.endAngle;if(o){qe(e,i,t,s,l,n);for(let c=0;c<o;++c)e.fill();isNaN(r)||(l=a+(r%W||W))}qe(e,i,t,s,l,n),e.fill()}(i,this,c,o,a),function Nl(e,i,t,s,n){const{fullCircles:o,startAngle:a,circumference:r,options:l}=i,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:u,borderRadius:f}=l,p="inner"===l.borderAlign;if(!c)return;e.setLineDash(d||[]),e.lineDashOffset=u,p?(e.lineWidth=2*c,e.lineJoin=h||"round"):(e.lineWidth=c,e.lineJoin=h||"bevel");let g=i.endAngle;if(o){qe(e,i,t,s,g,n);for(let m=0;m<o;++m)e.stroke();isNaN(r)||(g=a+(r%W||W))}p&&function zl(e,i,t){const{startAngle:s,pixelMargin:n,x:o,y:a,outerRadius:r,innerRadius:l}=i;let c=n/r;e.beginPath(),e.arc(o,a,r,s-c,t+c),l>n?(c=n/l,e.arc(o,a,l,t+c,s-c,!0)):e.arc(o,a,n,t+$,s-$),e.closePath(),e.clip()}(e,i,g),l.selfJoin&&g-a>=E&&0===f&&"miter"!==h&&function Fl(e,i,t){const{startAngle:s,x:n,y:o,outerRadius:a,innerRadius:r,options:l}=i,{borderWidth:c,borderJoinStyle:h}=l,d=Math.min(c/a,Z(s-t));if(e.beginPath(),e.arc(n,o,a-c/2,s+d/2,t-d/2),r>0){const u=Math.min(c/r,Z(s-t));e.arc(n,o,r+c/2,t-u/2,s+u/2,!0)}else{const u=Math.min(c/2,a*Z(s-t));if("round"===h)e.arc(n,o,u,t-E/2,s+E/2,!0);else if("bevel"===h){const f=2*u*u,p=-f*Math.cos(t+E/2)+n,g=-f*Math.sin(t+E/2)+o,m=f*Math.cos(s+E/2)+n,b=f*Math.sin(s+E/2)+o;e.lineTo(p,g),e.lineTo(m,b)}}e.closePath(),e.moveTo(0,0),e.rect(0,0,e.canvas.width,e.canvas.height),e.clip("evenodd")}(e,i,g),o||(qe(e,i,t,s,g,n),e.stroke())}(i,this,c,o,a),i.restore()}},BarElement:class nc extends kt{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(i){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,i&&Object.assign(this,i)}draw(i){const{inflateAmount:t,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:a}=function ec(e){const i=Bn(e),t=i.right-i.left,s=i.bottom-i.top,n=function Jl(e,i,t){const n=e.borderSkipped,o=Ps(e.options.borderWidth);return{t:Lt(n.top,o.top,0,t),r:Lt(n.right,o.right,0,i),b:Lt(n.bottom,o.bottom,0,t),l:Lt(n.left,o.left,0,i)}}(e,t/2,s/2),o=function tc(e,i,t){const{enableBorderRadius:s}=e.getProps(["enableBorderRadius"]),n=e.options.borderRadius,o=Bt(n),a=Math.min(i,t),r=e.borderSkipped,l=s||T(n);return{topLeft:Lt(!l||r.top||r.left,o.topLeft,0,a),topRight:Lt(!l||r.top||r.right,o.topRight,0,a),bottomLeft:Lt(!l||r.bottom||r.left,o.bottomLeft,0,a),bottomRight:Lt(!l||r.bottom||r.right,o.bottomRight,0,a)}}(e,t/2,s/2);return{outer:{x:i.left,y:i.top,w:t,h:s,radius:o},inner:{x:i.left+n.l,y:i.top+n.t,w:t-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}(this),r=function ic(e){return e.topLeft||e.topRight||e.bottomLeft||e.bottomRight}(a.radius)?fe:sc;i.save(),(a.w!==o.w||a.h!==o.h)&&(i.beginPath(),r(i,$i(a,t,o)),i.clip(),r(i,$i(o,-t,a)),i.fillStyle=s,i.fill("evenodd")),i.beginPath(),r(i,$i(o,t)),i.fillStyle=n,i.fill(),i.restore()}inRange(i,t,s){return ji(this,i,t,s)}inXRange(i,t){return ji(this,i,null,t)}inYRange(i,t){return ji(this,null,i,t)}getCenterPoint(i){const{x:t,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],i);return{x:o?(t+n)/2:t,y:o?s:(s+n)/2}}getRange(i){return"x"===i?this.width/2:this.height/2}},LineElement:Ze,PointElement:Ql});const Yi=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],Vn=Yi.map(e=>e.replace("rgb(","rgba(").replace(")",", 0.5)"));function Wn(e){return Yi[e%Yi.length]}function Nn(e){return Vn[e%Vn.length]}function Hn(e){let i;for(i in e)if(e[i].borderColor||e[i].backgroundColor)return!0;return!1}var uc={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(e,i,t){if(!t.enabled)return;const{data:{datasets:s},options:n}=e.config,{elements:o}=n,a=Hn(s)||function hc(e){return e&&(e.borderColor||e.backgroundColor)}(n)||o&&Hn(o)||function dc(){return"rgba(0,0,0,0.1)"!==N.borderColor||"rgba(0,0,0,0.1)"!==N.backgroundColor}();if(!t.forceOverride&&a)return;const r=function cc(e){let i=0;return(t,s)=>{const n=e.getDatasetMeta(s).controller;n instanceof Ei?i=function rc(e,i){return e.backgroundColor=e.data.map(()=>Wn(i++)),i}(t,i):n instanceof an?i=function lc(e,i){return e.backgroundColor=e.data.map(()=>Nn(i++)),i}(t,i):n&&(i=function ac(e,i){return e.borderColor=Wn(i),e.backgroundColor=Nn(i),++i}(t,i))}}(e);s.forEach(r)}};function jn(e){if(e._decimated){const i=e._data;delete e._decimated,delete e._data,Object.defineProperty(e,"data",{configurable:!0,enumerable:!0,writable:!0,value:i})}}function $n(e){e.data.datasets.forEach(i=>{jn(i)})}var mc={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(e,i,t)=>{if(!t.enabled)return void $n(e);const s=e.width;e.data.datasets.forEach((n,o)=>{const{_data:a,indexAxis:r}=n,l=e.getDatasetMeta(o),c=a||n.data;if("y"===ge([r,e.options.indexAxis])||!l.controller.supportsDecimation)return;const h=e.scales[l.xAxisID];if("linear"!==h.type&&"time"!==h.type||e.options.parsing)return;let p,{start:d,count:u}=function pc(e,i){const t=i.length;let n,s=0;const{iScale:o}=e,{min:a,max:r,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(s=G(yt(i,o.axis,a).lo,0,t-1)),n=c?G(yt(i,o.axis,r).hi+1,s,t)-s:t-s,{start:s,count:n}}(l,c);if(u<=(t.threshold||4*s))jn(n);else{switch(O(a)&&(n._data=c,delete n.data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(g){this._data=g}})),t.algorithm){case"lttb":p=function fc(e,i,t,s,n){const o=n.samples||s;if(o>=t)return e.slice(i,i+t);const a=[],r=(t-2)/(o-2);let l=0;const c=i+t-1;let d,u,f,p,g,h=i;for(a[l++]=e[h],d=0;d<o-2;d++){let _,m=0,b=0;const y=Math.floor((d+1)*r)+1+i,M=Math.min(Math.floor((d+2)*r)+1,t)+i,x=M-y;for(_=y;_<M;_++)m+=e[_].x,b+=e[_].y;m/=x,b/=x;const v=Math.floor(d*r)+1+i,w=Math.min(Math.floor((d+1)*r)+1,t)+i,{x:k,y:C}=e[h];for(f=p=-1,_=v;_<w;_++)p=.5*Math.abs((k-m)*(e[_].y-C)-(k-e[_].x)*(b-C)),p>f&&(f=p,u=e[_],g=_);a[l++]=u,h=g}return a[l++]=e[c],a}(c,d,u,s,t);break;case"min-max":p=function gc(e,i,t,s){let a,r,l,c,h,d,u,f,p,g,n=0,o=0;const m=[],_=e[i].x,M=e[i+t-1].x-_;for(a=i;a<i+t;++a){r=e[a],l=(r.x-_)/M*s,c=r.y;const x=0|l;if(x===h)c<p?(p=c,d=a):c>g&&(g=c,u=a),n=(o*n+r.x)/++o;else{const v=a-1;if(!O(d)&&!O(u)){const w=Math.min(d,u),k=Math.max(d,u);w!==f&&w!==v&&m.push({...e[w],x:n}),k!==f&&k!==v&&m.push({...e[k],x:n})}a>0&&v!==f&&m.push(e[v]),m.push(r),h=x,o=0,p=g=c,d=u=f=a}}return m}(c,d,u,s);break;default:throw new Error(`Unsupported decimation algorithm '${t.algorithm}'`)}n._decimated=p}})},destroy(e){$n(e)}};function Ui(e,i,t,s){if(s)return;let n=i[e],o=t[e];return"angle"===e&&(n=Z(n),o=Z(o)),{property:e,start:n,end:o}}function Qe(e,i,t){for(;i>e;i--){const s=t[i];if(!isNaN(s.x)&&!isNaN(s.y))break}return i}function Yn(e,i,t,s){return e&&i?s(e[t],i[t]):e?e[t]:i?i[t]:0}function Un(e,i){let t=[],s=!1;return V(e)?(s=!0,t=e):t=function _c(e,i){const{x:t=null,y:s=null}=e||{},n=i.points,o=[];return i.segments.forEach(({start:a,end:r})=>{r=Qe(a,r,n);const l=n[a],c=n[r];null!==s?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):null!==t&&(o.push({x:t,y:l.y}),o.push({x:t,y:c.y}))}),o}(e,i),t.length?new Ze({points:t,options:{tension:0},_loop:s,_fullLoop:s}):null}function Xn(e){return e&&!1!==e.fill}function xc(e,i,t){let n=e[i].fill;const o=[i];let a;if(!t)return n;for(;!1!==n&&-1===o.indexOf(n);){if(!j(n))return n;if(a=e[n],!a)return!1;if(a.visible)return n;o.push(n),n=a.fill}return!1}function yc(e,i,t){const s=function Sc(e){const i=e.options,t=i.fill;let s=P(t&&t.target,t);return void 0===s&&(s=!!i.backgroundColor),!1!==s&&null!==s&&(!0===s?"origin":s)}(e);if(T(s))return!isNaN(s.value)&&s;let n=parseFloat(s);return j(n)&&Math.floor(n)===n?function vc(e,i,t,s){return("-"===e||"+"===e)&&(t=i+t),!(t===i||t<0||t>=s)&&t}(s[0],i,n,t):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function Pc(e,i,t){const s=[];for(let n=0;n<t.length;n++){const o=t[n],{first:a,last:r,point:l}=Dc(o,i,"x");if(!(!l||a&&r))if(a)s.unshift(l);else if(e.push(l),!r)break}e.push(...s)}function Dc(e,i,t){const s=e.interpolate(i,t);if(!s)return{};const n=s[t],o=e.segments,a=e.points;let r=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],d=a[h.start][t],u=a[h.end][t];if(xt(n,d,u)){r=n===d,l=n===u;break}}return{first:r,last:l,point:s}}class Gn{constructor(i){this.x=i.x,this.y=i.y,this.radius=i.radius}pathSegment(i,t,s){const{x:n,y:o,radius:a}=this;return i.arc(n,o,a,(t=t||{start:0,end:W}).end,t.start,!0),!s.bounds}interpolate(i){const{x:t,y:s,radius:n}=this,o=i.angle;return{x:t+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}}function Xi(e,i,t){const s=function Ac(e){const{chart:i,fill:t,line:s}=e;if(j(t))return function Oc(e,i){const t=e.getDatasetMeta(i);return t&&e.isDatasetVisible(i)?t.dataset:null}(i,t);if("stack"===t)return function wc(e){const{scale:i,index:t,line:s}=e,n=[],o=s.segments,a=s.points,r=function Cc(e,i){const t=[],s=e.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){const o=s[n];if(o.index===i)break;o.hidden||t.unshift(o.dataset)}return t}(i,t);r.push(Un({x:null,y:i.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)Pc(n,a[h],r)}return new Ze({points:n,options:{}})}(e);if("shape"===t)return!0;const n=function Tc(e){return(e.scale||{}).getPointPositionForValue?function Rc(e){const{scale:i,fill:t}=e,s=i.options,n=i.getLabels().length,o=s.reverse?i.max:i.min,a=function kc(e,i,t){let s;return s="start"===e?t:"end"===e?i.options.reverse?i.min:i.max:T(e)?e.value:i.getBaseValue(),s}(t,i,o),r=[];if(s.grid.circular){const l=i.getPointPositionForValue(0,o);return new Gn({x:l.x,y:l.y,radius:i.getDistanceFromCenterForValue(a)})}for(let l=0;l<n;++l)r.push(i.getPointPositionForValue(l,a));return r}(e):function Lc(e){const{scale:i={},fill:t}=e,s=function Mc(e,i){let t=null;return"start"===e?t=i.bottom:"end"===e?t=i.top:T(e)?t=i.getPixelForValue(e.value):i.getBasePixel&&(t=i.getBasePixel()),t}(t,i);if(j(s)){const n=i.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}(e)}(e);return n instanceof Gn?n:Un(n,s)}(i),{chart:n,index:o,line:a,scale:r,axis:l}=i,c=a.options,h=c.fill,d=c.backgroundColor,{above:u=d,below:f=d}=h||{},p=n.getDatasetMeta(o),g=Us(n,p);s&&a.points.length&&(Fe(e,t),function Ec(e,i){const{line:t,target:s,above:n,below:o,area:a,scale:r,clip:l}=i,c=t._loop?"angle":i.axis;e.save();let h=o;o!==n&&("x"===c?(Kn(e,s,a.top),Gi(e,{line:t,target:s,color:n,scale:r,property:c,clip:l}),e.restore(),e.save(),Kn(e,s,a.bottom)):"y"===c&&(qn(e,s,a.left),Gi(e,{line:t,target:s,color:o,scale:r,property:c,clip:l}),e.restore(),e.save(),qn(e,s,a.right),h=n)),Gi(e,{line:t,target:s,color:h,scale:r,property:c,clip:l}),e.restore()}(e,{line:a,target:s,above:u,below:f,area:t,scale:r,axis:l,clip:g}),ze(e))}function Kn(e,i,t){const{segments:s,points:n}=i;let o=!0,a=!1;e.beginPath();for(const r of s){const{start:l,end:c}=r,h=n[l],d=n[Qe(l,c,n)];o?(e.moveTo(h.x,h.y),o=!1):(e.lineTo(h.x,t),e.lineTo(h.x,h.y)),a=!!i.pathSegment(e,r,{move:a}),a?e.closePath():e.lineTo(d.x,t)}e.lineTo(i.first().x,t),e.closePath(),e.clip()}function qn(e,i,t){const{segments:s,points:n}=i;let o=!0,a=!1;e.beginPath();for(const r of s){const{start:l,end:c}=r,h=n[l],d=n[Qe(l,c,n)];o?(e.moveTo(h.x,h.y),o=!1):(e.lineTo(t,h.y),e.lineTo(h.x,h.y)),a=!!i.pathSegment(e,r,{move:a}),a?e.closePath():e.lineTo(t,d.y)}e.lineTo(t,i.first().y),e.closePath(),e.clip()}function Gi(e,i){const{line:t,target:s,property:n,color:o,scale:a,clip:r}=i,l=function bc(e,i,t){const s=e.segments,n=e.points,o=i.points,a=[];for(const r of s){let{start:l,end:c}=r;c=Qe(l,c,n);const h=Ui(t,n[l],n[c],r.loop);if(!i.segments){a.push({source:r,target:h,start:n[l],end:n[c]});continue}const d=js(i,h);for(const u of d){const f=Ui(t,o[u.start],o[u.end],u.loop),p=Hs(r,n,f);for(const g of p)a.push({source:g,target:u,start:{[t]:Yn(h,f,"start",Math.max)},end:{[t]:Yn(h,f,"end",Math.min)}})}}return a}(t,s,n);for(const{source:c,target:h,start:d,end:u}of l){const{style:{backgroundColor:f=o}={}}=c,p=!0!==s;e.save(),e.fillStyle=f,Ic(e,a,r,p&&Ui(n,d,u)),e.beginPath();const g=!!t.pathSegment(e,c);let m;if(p){g?e.closePath():Zn(e,s,u,n);const b=!!s.pathSegment(e,h,{move:g,reverse:!0});m=g&&b,m||Zn(e,s,d,n)}e.closePath(),e.fill(m?"evenodd":"nonzero"),e.restore()}}function Ic(e,i,t,s){const n=i.chart.chartArea,{property:o,start:a,end:r}=s||{};if("x"===o||"y"===o){let l,c,h,d;"x"===o?(l=a,c=n.top,h=r,d=n.bottom):(l=n.left,c=a,h=n.right,d=r),e.beginPath(),t&&(l=Math.max(l,t.left),h=Math.min(h,t.right),c=Math.max(c,t.top),d=Math.min(d,t.bottom)),e.rect(l,c,h-l,d-c),e.clip()}}function Zn(e,i,t,s){const n=i.interpolate(t,s);n&&e.lineTo(n.x,n.y)}var Fc={id:"filler",afterDatasetsUpdate(e,i,t){const s=(e.data.datasets||[]).length,n=[];let o,a,r,l;for(a=0;a<s;++a)o=e.getDatasetMeta(a),r=o.dataset,l=null,r&&r.options&&r instanceof Ze&&(l={visible:e.isDatasetVisible(a),index:a,fill:yc(r,a,s),chart:e,axis:o.controller.options.indexAxis,scale:o.vScale,line:r}),o.$filler=l,n.push(l);for(a=0;a<s;++a)l=n[a],l&&!1!==l.fill&&(l.fill=xc(n,a,t.propagate))},beforeDraw(e,i,t){const s="beforeDraw"===t.drawTime,n=e.getSortedVisibleDatasetMetas(),o=e.chartArea;for(let a=n.length-1;a>=0;--a){const r=n[a].$filler;r&&(r.line.updateControlPoints(o,r.axis),s&&r.fill&&Xi(e.ctx,r,o))}},beforeDatasetsDraw(e,i,t){if("beforeDatasetsDraw"!==t.drawTime)return;const s=e.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){const o=s[n].$filler;Xn(o)&&Xi(e.ctx,o,e.chartArea)}},beforeDatasetDraw(e,i,t){const s=i.meta.$filler;!Xn(s)||"beforeDatasetDraw"!==t.drawTime||Xi(e.ctx,s,e.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const Qn=(e,i)=>{let{boxHeight:t=i,boxWidth:s=i}=e;return e.usePointStyle&&(t=Math.min(t,i),s=e.pointStyleWidth||Math.min(s,i)),{boxWidth:s,boxHeight:t,itemHeight:Math.max(i,t)}};class Jn extends kt{constructor(i){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=i.chart,this.options=i.options,this.ctx=i.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(i,t,s){this.maxWidth=i,this.maxHeight=t,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const i=this.options.labels||{};let t=z(i.generateLabels,[this.chart],this)||[];i.filter&&(t=t.filter(s=>i.filter(s,this.chart.data))),i.sort&&(t=t.sort((s,n)=>i.sort(s,n,this.chart.data))),this.options.reverse&&t.reverse(),this.legendItems=t}fit(){const{options:i,ctx:t}=this;if(!i.display)return void(this.width=this.height=0);const s=i.labels,n=X(s.font),o=n.size,a=this._computeTitleHeight(),{boxWidth:r,itemHeight:l}=Qn(s,o);let c,h;t.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(a,o,r,l)+10):(h=this.maxHeight,c=this._fitCols(a,n,r,l)+10),this.width=Math.min(c,i.maxWidth||this.maxWidth),this.height=Math.min(h,i.maxHeight||this.maxHeight)}_fitRows(i,t,s,n){const{ctx:o,maxWidth:a,options:{labels:{padding:r}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+r;let d=i;o.textAlign="left",o.textBaseline="middle";let u=-1,f=-h;return this.legendItems.forEach((p,g)=>{const m=s+t/2+o.measureText(p.text).width;(0===g||c[c.length-1]+m+2*r>a)&&(d+=h,c[c.length-(g>0?0:1)]=0,f+=h,u++),l[g]={left:0,top:f,row:u,width:m,height:n},c[c.length-1]+=m+r}),d}_fitCols(i,t,s,n){const{ctx:o,maxHeight:a,options:{labels:{padding:r}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=a-i;let d=r,u=0,f=0,p=0,g=0;return this.legendItems.forEach((m,b)=>{const{itemWidth:_,itemHeight:y}=function Bc(e,i,t,s,n){const o=function Vc(e,i,t,s){let n=e.text;return n&&"string"!=typeof n&&(n=n.reduce((o,a)=>o.length>a.length?o:a)),i+t.size/2+s.measureText(n).width}(s,e,i,t),a=function Wc(e,i,t){let s=e;return"string"!=typeof i.text&&(s=to(i,t)),s}(n,s,i.lineHeight);return{itemWidth:o,itemHeight:a}}(s,t,o,m,n);b>0&&f+y+2*r>h&&(d+=u+r,c.push({width:u,height:f}),p+=u+r,g++,u=f=0),l[b]={left:p,top:f,col:g,width:_,height:y},u=Math.max(u,_),f+=y+r}),d+=u,c.push({width:u,height:f}),d}adjustHitBoxes(){if(!this.options.display)return;const i=this._computeTitleHeight(),{legendHitBoxes:t,options:{align:s,labels:{padding:n},rtl:o}}=this,a=Jt(o,this.left,this.width);if(this.isHorizontal()){let r=0,l=Q(s,this.left+n,this.right-this.lineWidths[r]);for(const c of t)r!==c.row&&(r=c.row,l=Q(s,this.left+n,this.right-this.lineWidths[r])),c.top+=this.top+i+n,c.left=a.leftForLtr(a.x(l),c.width),l+=c.width+n}else{let r=0,l=Q(s,this.top+i+n,this.bottom-this.columnSizes[r].height);for(const c of t)c.col!==r&&(r=c.col,l=Q(s,this.top+i+n,this.bottom-this.columnSizes[r].height)),c.top=l,c.left+=this.left+n,c.left=a.leftForLtr(a.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){const i=this.ctx;Fe(i,this),this._draw(),ze(i)}}_draw(){const{options:i,columnSizes:t,lineWidths:s,ctx:n}=this,{align:o,labels:a}=i,r=N.color,l=Jt(i.rtl,this.left,this.width),c=X(a.font),{padding:h}=a,d=c.size,u=d/2;let f;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:p,boxHeight:g,itemHeight:m}=Qn(a,d),y=this.isHorizontal(),M=this._computeTitleHeight();f=y?{x:Q(o,this.left+h,this.right-s[0]),y:this.top+h+M,line:0}:{x:this.left+h,y:Q(o,this.top+M+h,this.bottom-t[0].height),line:0},Bs(this.ctx,i.textDirection);const x=m+h;this.legendItems.forEach((v,w)=>{n.strokeStyle=v.fontColor,n.fillStyle=v.fontColor;const k=n.measureText(v.text).width,C=l.textAlign(v.textAlign||(v.textAlign=a.textAlign)),A=p+u+k;let D=f.x,L=f.y;l.setWidth(this.width),y?w>0&&D+A+h>this.right&&(L=f.y+=x,f.line++,D=f.x=Q(o,this.left+h,this.right-s[f.line])):w>0&&L+x>this.bottom&&(D=f.x=D+t[f.line].width+h,f.line++,L=f.y=Q(o,this.top+M+h,this.bottom-t[f.line].height)),function(v,w,k){if(isNaN(p)||p<=0||isNaN(g)||g<0)return;n.save();const C=P(k.lineWidth,1);if(n.fillStyle=P(k.fillStyle,r),n.lineCap=P(k.lineCap,"butt"),n.lineDashOffset=P(k.lineDashOffset,0),n.lineJoin=P(k.lineJoin,"miter"),n.lineWidth=C,n.strokeStyle=P(k.strokeStyle,r),n.setLineDash(P(k.lineDash,[])),a.usePointStyle){const A={radius:g*Math.SQRT2/2,pointStyle:k.pointStyle,rotation:k.rotation,borderWidth:C},D=l.xPlus(v,p/2);Cs(n,A,D,w+u,a.pointStyleWidth&&p)}else{const A=w+Math.max((d-g)/2,0),D=l.leftForLtr(v,p),L=Bt(k.borderRadius);n.beginPath(),Object.values(L).some(q=>0!==q)?fe(n,{x:D,y:A,w:p,h:g,radius:L}):n.rect(D,A,p,g),n.fill(),0!==C&&n.stroke()}n.restore()}(l.x(D),L,v),D=((e,i,t,s)=>e===(s?"left":"right")?t:"center"===e?(i+t)/2:i)(C,D+p+u,y?D+A:this.right,i.rtl),function(v,w,k){zt(n,k.text,v,w+m/2,c,{strikethrough:k.hidden,textAlign:l.textAlign(k.textAlign)})}(l.x(D),L,v),y?f.x+=A+h:f.y+="string"!=typeof v.text?to(v,c.lineHeight)+h:x}),Vs(this.ctx,i.textDirection)}drawTitle(){const i=this.options,t=i.title,s=X(t.font),n=J(t.padding);if(!t.display)return;const o=Jt(i.rtl,this.left,this.width),a=this.ctx,r=t.position,c=n.top+s.size/2;let h,d=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),h=this.top+c,d=Q(i.align,d,this.right-u);else{const p=this.columnSizes.reduce((g,m)=>Math.max(g,m.height),0);h=c+Q(i.align,this.top,this.bottom-p-i.labels.padding-this._computeTitleHeight())}const f=Q(r,d,d+u);a.textAlign=o.textAlign(_i(r)),a.textBaseline="middle",a.strokeStyle=t.color,a.fillStyle=t.color,a.font=s.string,zt(a,t.text,f,h,s)}_computeTitleHeight(){const i=this.options.title,t=X(i.font),s=J(i.padding);return i.display?t.lineHeight+s.height:0}_getLegendItemAt(i,t){let s,n,o;if(xt(i,this.left,this.right)&&xt(t,this.top,this.bottom))for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],xt(i,n.left,n.left+n.width)&&xt(t,n.top,n.top+n.height))return this.legendItems[s];return null}handleEvent(i){const t=this.options;if(!function Nc(e,i){return!(("mousemove"!==e&&"mouseout"!==e||!i.onHover&&!i.onLeave)&&(!i.onClick||"click"!==e&&"mouseup"!==e))}(i.type,t))return;const s=this._getLegendItemAt(i.x,i.y);if("mousemove"===i.type||"mouseout"===i.type){const n=this._hoveredItem,o=((e,i)=>null!==e&&null!==i&&e.datasetIndex===i.datasetIndex&&e.index===i.index)(n,s);n&&!o&&z(t.onLeave,[i,n,this],this),this._hoveredItem=s,s&&!o&&z(t.onHover,[i,s,this],this)}else s&&z(t.onClick,[i,s,this],this)}}function to(e,i){return i*(e.text?e.text.length:0)}var Hc={id:"legend",_element:Jn,start(e,i,t){const s=e.legend=new Jn({ctx:e.ctx,options:t,chart:e});tt.configure(e,s,t),tt.addBox(e,s)},stop(e){tt.removeBox(e,e.legend),delete e.legend},beforeUpdate(e,i,t){const s=e.legend;tt.configure(e,s,t),s.options=t},afterUpdate(e){const i=e.legend;i.buildLabels(),i.adjustHitBoxes()},afterEvent(e,i){i.replay||e.legend.handleEvent(i.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(e,i,t){const s=i.datasetIndex,n=t.chart;n.isDatasetVisible(s)?(n.hide(s),i.hidden=!0):(n.show(s),i.hidden=!1)},onHover:null,onLeave:null,labels:{color:e=>e.chart.options.color,boxWidth:40,padding:10,generateLabels(e){const i=e.data.datasets,{labels:{usePointStyle:t,pointStyle:s,textAlign:n,color:o,useBorderRadius:a,borderRadius:r}}=e.legend.options;return e._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(t?0:void 0),h=J(c.borderWidth);return{text:i[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:a&&(r||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:e=>e.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:e=>!e.startsWith("on"),labels:{_scriptable:e=>!["generateLabels","filter","sort"].includes(e)}}};class Ki extends kt{constructor(i){super(),this.chart=i.chart,this.options=i.options,this.ctx=i.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(i,t){const s=this.options;if(this.left=0,this.top=0,!s.display)return void(this.width=this.height=this.right=this.bottom=0);this.width=this.right=i,this.height=this.bottom=t;const n=V(s.text)?s.text.length:1;this._padding=J(s.padding);const o=n*X(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const i=this.options.position;return"top"===i||"bottom"===i}_drawArgs(i){const{top:t,left:s,bottom:n,right:o,options:a}=this,r=a.align;let c,h,d,l=0;return this.isHorizontal()?(h=Q(r,s,o),d=t+i,c=o-s):("left"===a.position?(h=s+i,d=Q(r,n,t),l=-.5*E):(h=o-i,d=Q(r,t,n),l=.5*E),c=n-t),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const i=this.ctx,t=this.options;if(!t.display)return;const s=X(t.font),o=s.lineHeight/2+this._padding.top,{titleX:a,titleY:r,maxWidth:l,rotation:c}=this._drawArgs(o);zt(i,t.text,0,0,s,{color:t.color,maxWidth:l,rotation:c,textAlign:_i(t.align),textBaseline:"middle",translation:[a,r]})}}var $c={id:"title",_element:Ki,start(e,i,t){!function jc(e,i){const t=new Ki({ctx:e.ctx,options:i,chart:e});tt.configure(e,t,i),tt.addBox(e,t),e.titleBlock=t}(e,t)},stop(e){tt.removeBox(e,e.titleBlock),delete e.titleBlock},beforeUpdate(e,i,t){const s=e.titleBlock;tt.configure(e,s,t),s.options=t},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Je=new WeakMap;var Yc={id:"subtitle",start(e,i,t){const s=new Ki({ctx:e.ctx,options:t,chart:e});tt.configure(e,s,t),tt.addBox(e,s),Je.set(e,s)},stop(e){tt.removeBox(e,Je.get(e)),Je.delete(e)},beforeUpdate(e,i,t){const s=Je.get(e);tt.configure(e,s,t),s.options=t},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const ke={average(e){if(!e.length)return!1;let i,t,s=new Set,n=0,o=0;for(i=0,t=e.length;i<t;++i){const r=e[i].element;if(r&&r.hasValue()){const l=r.tooltipPosition();s.add(l.x),n+=l.y,++o}}return 0!==o&&0!==s.size&&{x:[...s].reduce((r,l)=>r+l)/s.size,y:n/o}},nearest(e,i){if(!e.length)return!1;let o,a,r,t=i.x,s=i.y,n=Number.POSITIVE_INFINITY;for(o=0,a=e.length;o<a;++o){const l=e[o].element;if(l&&l.hasValue()){const h=mi(i,l.getCenterPoint());h<n&&(n=h,r=l)}}if(r){const l=r.tooltipPosition();t=l.x,s=l.y}return{x:t,y:s}}};function bt(e,i){return i&&(V(i)?Array.prototype.push.apply(e,i):e.push(i)),e}function St(e){return("string"==typeof e||e instanceof String)&&e.indexOf("\n")>-1?e.split("\n"):e}function Uc(e,i){const{element:t,datasetIndex:s,index:n}=i,o=e.getDatasetMeta(s).controller,{label:a,value:r}=o.getLabelAndValue(n);return{chart:e,label:a,parsed:o.getParsed(n),raw:e.data.datasets[s].data[n],formattedValue:r,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:t}}function eo(e,i){const t=e.chart.ctx,{body:s,footer:n,title:o}=e,{boxWidth:a,boxHeight:r}=i,l=X(i.bodyFont),c=X(i.titleFont),h=X(i.footerFont),d=o.length,u=n.length,f=s.length,p=J(i.padding);let g=p.height,m=0,b=s.reduce((M,x)=>M+x.before.length+x.lines.length+x.after.length,0);b+=e.beforeBody.length+e.afterBody.length,d&&(g+=d*c.lineHeight+(d-1)*i.titleSpacing+i.titleMarginBottom),b&&(g+=f*(i.displayColors?Math.max(r,l.lineHeight):l.lineHeight)+(b-f)*l.lineHeight+(b-1)*i.bodySpacing),u&&(g+=i.footerMarginTop+u*h.lineHeight+(u-1)*i.footerSpacing);let _=0;const y=function(M){m=Math.max(m,t.measureText(M).width+_)};return t.save(),t.font=c.string,I(e.title,y),t.font=l.string,I(e.beforeBody.concat(e.afterBody),y),_=i.displayColors?a+2+i.boxPadding:0,I(s,M=>{I(M.before,y),I(M.lines,y),I(M.after,y)}),_=0,t.font=h.string,I(e.footer,y),t.restore(),m+=p.width,{width:m,height:g}}function Kc(e,i,t,s){const{x:n,width:o}=t,{width:a,chartArea:{left:r,right:l}}=e;let c="center";return"center"===s?c=n<=(r+l)/2?"left":"right":n<=o/2?c="left":n>=a-o/2&&(c="right"),function Gc(e,i,t,s){const{x:n,width:o}=s,a=t.caretSize+t.caretPadding;if("left"===e&&n+o+a>i.width||"right"===e&&n-o-a<0)return!0}(c,e,i,t)&&(c="center"),c}function io(e,i,t){const s=t.yAlign||i.yAlign||function Xc(e,i){const{y:t,height:s}=i;return t<s/2?"top":t>e.height-s/2?"bottom":"center"}(e,t);return{xAlign:t.xAlign||i.xAlign||Kc(e,i,t,s),yAlign:s}}function so(e,i,t,s){const{caretSize:n,caretPadding:o,cornerRadius:a}=e,{xAlign:r,yAlign:l}=t,c=n+o,{topLeft:h,topRight:d,bottomLeft:u,bottomRight:f}=Bt(a);let p=function qc(e,i){let{x:t,width:s}=e;return"right"===i?t-=s:"center"===i&&(t-=s/2),t}(i,r);const g=function Zc(e,i,t){let{y:s,height:n}=e;return"top"===i?s+=t:s-="bottom"===i?n+t:n/2,s}(i,l,c);return"center"===l?"left"===r?p+=c:"right"===r&&(p-=c):"left"===r?p-=Math.max(h,u)+n:"right"===r&&(p+=Math.max(d,f)+n),{x:G(p,0,s.width-i.width),y:G(g,0,s.height-i.height)}}function ti(e,i,t){const s=J(t.padding);return"center"===i?e.x+e.width/2:"right"===i?e.x+e.width-s.right:e.x+s.left}function no(e){return bt([],St(e))}function oo(e,i){const t=i&&i.dataset&&i.dataset.tooltip&&i.dataset.tooltip.callbacks;return t?e.override(t):e}const ao={beforeTitle:_t,title(e){if(e.length>0){const i=e[0],t=i.chart.data.labels,s=t?t.length:0;if(this&&this.options&&"dataset"===this.options.mode)return i.dataset.label||"";if(i.label)return i.label;if(s>0&&i.dataIndex<s)return t[i.dataIndex]}return""},afterTitle:_t,beforeBody:_t,beforeLabel:_t,label(e){if(this&&this.options&&"dataset"===this.options.mode)return e.label+": "+e.formattedValue||e.formattedValue;let i=e.dataset.label||"";i&&(i+=": ");const t=e.formattedValue;return O(t)||(i+=t),i},labelColor(e){const t=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{borderColor:t.borderColor,backgroundColor:t.backgroundColor,borderWidth:t.borderWidth,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(e){const t=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{pointStyle:t.pointStyle,rotation:t.rotation}},afterLabel:_t,afterBody:_t,beforeFooter:_t,footer:_t,afterFooter:_t};function st(e,i,t,s){const n=e[i].call(t,s);return typeof n>"u"?ao[i].call(t,s):n}let ro=(()=>class e extends kt{static positioners=ke;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const s=this.chart,n=this.options.setContext(this.getContext()),o=n.enabled&&s.options.animation&&n.animations,a=new Gs(this.chart,o);return o._cacheable&&(this._cachedAnimations=Object.freeze(a)),a}getContext(){return this.$context||(this.$context=function Qc(e,i,t){return Ot(e,{tooltip:i,tooltipItems:t,type:"tooltip"})}(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,s){const{callbacks:n}=s,o=st(n,"beforeTitle",this,t),a=st(n,"title",this,t),r=st(n,"afterTitle",this,t);let l=[];return l=bt(l,St(o)),l=bt(l,St(a)),l=bt(l,St(r)),l}getBeforeBody(t,s){return no(st(s.callbacks,"beforeBody",this,t))}getBody(t,s){const{callbacks:n}=s,o=[];return I(t,a=>{const r={before:[],lines:[],after:[]},l=oo(n,a);bt(r.before,St(st(l,"beforeLabel",this,a))),bt(r.lines,st(l,"label",this,a)),bt(r.after,St(st(l,"afterLabel",this,a))),o.push(r)}),o}getAfterBody(t,s){return no(st(s.callbacks,"afterBody",this,t))}getFooter(t,s){const{callbacks:n}=s,o=st(n,"beforeFooter",this,t),a=st(n,"footer",this,t),r=st(n,"afterFooter",this,t);let l=[];return l=bt(l,St(o)),l=bt(l,St(a)),l=bt(l,St(r)),l}_createItems(t){const s=this._active,n=this.chart.data,o=[],a=[],r=[];let c,h,l=[];for(c=0,h=s.length;c<h;++c)l.push(Uc(this.chart,s[c]));return t.filter&&(l=l.filter((d,u,f)=>t.filter(d,u,f,n))),t.itemSort&&(l=l.sort((d,u)=>t.itemSort(d,u,n))),I(l,d=>{const u=oo(t.callbacks,d);o.push(st(u,"labelColor",this,d)),a.push(st(u,"labelPointStyle",this,d)),r.push(st(u,"labelTextColor",this,d))}),this.labelColors=o,this.labelPointStyles=a,this.labelTextColors=r,this.dataPoints=l,l}update(t,s){const n=this.options.setContext(this.getContext()),o=this._active;let a,r=[];if(o.length){const l=ke[n.position].call(this,o,this._eventPosition);r=this._createItems(n),this.title=this.getTitle(r,n),this.beforeBody=this.getBeforeBody(r,n),this.body=this.getBody(r,n),this.afterBody=this.getAfterBody(r,n),this.footer=this.getFooter(r,n);const c=this._size=eo(this,n),h=Object.assign({},l,c),d=io(this.chart,n,h),u=so(n,h,d,this.chart);this.xAlign=d.xAlign,this.yAlign=d.yAlign,a={opacity:1,x:u.x,y:u.y,width:c.width,height:c.height,caretX:l.x,caretY:l.y}}else 0!==this.opacity&&(a={opacity:0});this._tooltipItems=r,this.$context=void 0,a&&this._resolveAnimations().update(this,a),t&&n.external&&n.external.call(this,{chart:this.chart,tooltip:this,replay:s})}drawCaret(t,s,n,o){const a=this.getCaretPosition(t,n,o);s.lineTo(a.x1,a.y1),s.lineTo(a.x2,a.y2),s.lineTo(a.x3,a.y3)}getCaretPosition(t,s,n){const{xAlign:o,yAlign:a}=this,{caretSize:r,cornerRadius:l}=n,{topLeft:c,topRight:h,bottomLeft:d,bottomRight:u}=Bt(l),{x:f,y:p}=t,{width:g,height:m}=s;let b,_,y,M,x,v;return"center"===a?(x=p+m/2,"left"===o?(b=f,_=b-r,M=x+r,v=x-r):(b=f+g,_=b+r,M=x-r,v=x+r),y=b):(_="left"===o?f+Math.max(c,d)+r:"right"===o?f+g-Math.max(h,u)-r:this.caretX,"top"===a?(M=p,x=M-r,b=_-r,y=_+r):(M=p+m,x=M+r,b=_+r,y=_-r),v=M),{x1:b,x2:_,x3:y,y1:M,y2:x,y3:v}}drawTitle(t,s,n){const o=this.title,a=o.length;let r,l,c;if(a){const h=Jt(n.rtl,this.x,this.width);for(t.x=ti(this,n.titleAlign,n),s.textAlign=h.textAlign(n.titleAlign),s.textBaseline="middle",r=X(n.titleFont),l=n.titleSpacing,s.fillStyle=n.titleColor,s.font=r.string,c=0;c<a;++c)s.fillText(o[c],h.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+l,c+1===a&&(t.y+=n.titleMarginBottom-l)}}_drawColorBox(t,s,n,o,a){const r=this.labelColors[n],l=this.labelPointStyles[n],{boxHeight:c,boxWidth:h}=a,d=X(a.bodyFont),u=ti(this,"left",a),f=o.x(u),g=s.y+(c<d.lineHeight?(d.lineHeight-c)/2:0);if(a.usePointStyle){const m={radius:Math.min(h,c)/2,pointStyle:l.pointStyle,rotation:l.rotation,borderWidth:1},b=o.leftForLtr(f,h)+h/2,_=g+c/2;t.strokeStyle=a.multiKeyBackground,t.fillStyle=a.multiKeyBackground,ki(t,m,b,_),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,ki(t,m,b,_)}else{t.lineWidth=T(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const m=o.leftForLtr(f,h),b=o.leftForLtr(o.xPlus(f,1),h-2),_=Bt(r.borderRadius);Object.values(_).some(y=>0!==y)?(t.beginPath(),t.fillStyle=a.multiKeyBackground,fe(t,{x:m,y:g,w:h,h:c,radius:_}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),fe(t,{x:b,y:g+1,w:h-2,h:c-2,radius:_}),t.fill()):(t.fillStyle=a.multiKeyBackground,t.fillRect(m,g,h,c),t.strokeRect(m,g,h,c),t.fillStyle=r.backgroundColor,t.fillRect(b,g+1,h-2,c-2))}t.fillStyle=this.labelTextColors[n]}drawBody(t,s,n){const{body:o}=this,{bodySpacing:a,bodyAlign:r,displayColors:l,boxHeight:c,boxWidth:h,boxPadding:d}=n,u=X(n.bodyFont);let f=u.lineHeight,p=0;const g=Jt(n.rtl,this.x,this.width),m=function(C){s.fillText(C,g.x(t.x+p),t.y+f/2),t.y+=f+a},b=g.textAlign(r);let _,y,M,x,v,w,k;for(s.textAlign=r,s.textBaseline="middle",s.font=u.string,t.x=ti(this,b,n),s.fillStyle=n.bodyColor,I(this.beforeBody,m),p=l&&"right"!==b?"center"===r?h/2+d:h+2+d:0,x=0,w=o.length;x<w;++x){for(_=o[x],y=this.labelTextColors[x],s.fillStyle=y,I(_.before,m),M=_.lines,l&&M.length&&(this._drawColorBox(s,t,x,g,n),f=Math.max(u.lineHeight,c)),v=0,k=M.length;v<k;++v)m(M[v]),f=u.lineHeight;I(_.after,m)}p=0,f=u.lineHeight,I(this.afterBody,m),t.y-=a}drawFooter(t,s,n){const o=this.footer,a=o.length;let r,l;if(a){const c=Jt(n.rtl,this.x,this.width);for(t.x=ti(this,n.footerAlign,n),t.y+=n.footerMarginTop,s.textAlign=c.textAlign(n.footerAlign),s.textBaseline="middle",r=X(n.footerFont),s.fillStyle=n.footerColor,s.font=r.string,l=0;l<a;++l)s.fillText(o[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+n.footerSpacing}}drawBackground(t,s,n,o){const{xAlign:a,yAlign:r}=this,{x:l,y:c}=t,{width:h,height:d}=n,{topLeft:u,topRight:f,bottomLeft:p,bottomRight:g}=Bt(o.cornerRadius);s.fillStyle=o.backgroundColor,s.strokeStyle=o.borderColor,s.lineWidth=o.borderWidth,s.beginPath(),s.moveTo(l+u,c),"top"===r&&this.drawCaret(t,s,n,o),s.lineTo(l+h-f,c),s.quadraticCurveTo(l+h,c,l+h,c+f),"center"===r&&"right"===a&&this.drawCaret(t,s,n,o),s.lineTo(l+h,c+d-g),s.quadraticCurveTo(l+h,c+d,l+h-g,c+d),"bottom"===r&&this.drawCaret(t,s,n,o),s.lineTo(l+p,c+d),s.quadraticCurveTo(l,c+d,l,c+d-p),"center"===r&&"left"===a&&this.drawCaret(t,s,n,o),s.lineTo(l,c+u),s.quadraticCurveTo(l,c,l+u,c),s.closePath(),s.fill(),o.borderWidth>0&&s.stroke()}_updateAnimationTarget(t){const s=this.chart,n=this.$animations,o=n&&n.x,a=n&&n.y;if(o||a){const r=ke[t.position].call(this,this._active,this._eventPosition);if(!r)return;const l=this._size=eo(this,t),c=Object.assign({},r,this._size),h=io(s,t,c),d=so(t,c,h,s);(o._to!==d.x||a._to!==d.y)&&(this.xAlign=h.xAlign,this.yAlign=h.yAlign,this.width=l.width,this.height=l.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,d))}}_willRender(){return!!this.opacity}draw(t){const s=this.options.setContext(this.getContext());let n=this.opacity;if(!n)return;this._updateAnimationTarget(s);const o={width:this.width,height:this.height},a={x:this.x,y:this.y};n=Math.abs(n)<.001?0:n;const r=J(s.padding);s.enabled&&(this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length)&&(t.save(),t.globalAlpha=n,this.drawBackground(a,t,o,s),Bs(t,s.textDirection),a.y+=r.top,this.drawTitle(a,t,s),this.drawBody(a,t,s),this.drawFooter(a,t,s),Vs(t,s.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,s){const n=this._active,o=t.map(({datasetIndex:l,index:c})=>{const h=this.chart.getDatasetMeta(l);if(!h)throw new Error("Cannot find a dataset at index "+l);return{datasetIndex:l,element:h.data[c],index:c}}),a=!Oe(n,o),r=this._positionChanged(o,s);(a||r)&&(this._active=o,this._eventPosition=s,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,s,n=!0){if(s&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const o=this.options,a=this._active||[],r=this._getActiveElements(t,a,s,n),l=this._positionChanged(r,t),c=s||!Oe(r,a)||l;return c&&(this._active=r,(o.enabled||o.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,s))),c}_getActiveElements(t,s,n,o){const a=this.options;if("mouseout"===t.type)return[];if(!o)return s.filter(l=>this.chart.data.datasets[l.datasetIndex]&&void 0!==this.chart.getDatasetMeta(l.datasetIndex).controller.getParsed(l.index));const r=this.chart.getElementsAtEventForMode(t,a.mode,a,n);return a.reverse&&r.reverse(),r}_positionChanged(t,s){const{caretX:n,caretY:o,options:a}=this,r=ke[a.position].call(this,t,s);return!1!==r&&(n!==r.x||o!==r.y)}})();var th=Object.freeze({__proto__:null,Colors:uc,Decimation:mc,Filler:Fc,Legend:Hc,SubTitle:Yc,Title:$c,Tooltip:{id:"tooltip",_element:ro,positioners:ke,afterInit(e,i,t){t&&(e.tooltip=new ro({chart:e,options:t}))},beforeUpdate(e,i,t){e.tooltip&&e.tooltip.initialize(t)},reset(e,i,t){e.tooltip&&e.tooltip.initialize(t)},afterDraw(e){const i=e.tooltip;if(i&&i._willRender()){const t={tooltip:i};if(!1===e.notifyPlugins("beforeTooltipDraw",{...t,cancelable:!0}))return;i.draw(e.ctx),e.notifyPlugins("afterTooltipDraw",t)}},afterEvent(e,i){e.tooltip&&e.tooltip.handleEvent(i.event,i.replay,i.inChartArea)&&(i.changed=!0)},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(e,i)=>i.bodyFont.size,boxWidth:(e,i)=>i.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:ao},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:e=>"filter"!==e&&"itemSort"!==e&&"external"!==e,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]}});function lo(e){const i=this.getLabels();return e>=0&&e<i.length?i[e]:e}let nh=(()=>class e extends jt{static id="category";static defaults={ticks:{callback:lo}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const s=this._addedLabels;if(s.length){const n=this.getLabels();for(const{index:o,label:a}of s)n[o]===a&&n.splice(o,1);this._addedLabels=[]}super.init(t)}parse(t,s){if(O(t))return null;const n=this.getLabels();return((e,i)=>null===e?null:G(Math.round(e),0,i))(s=isFinite(s)&&n[s]===t?s:function ih(e,i,t,s){const n=e.indexOf(i);return-1===n?((e,i,t,s)=>("string"==typeof i?(t=e.push(i)-1,s.unshift({index:t,label:i})):isNaN(i)&&(t=null),t))(e,i,t,s):n!==e.lastIndexOf(i)?t:n}(n,t,P(s,t),this._addedLabels),n.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(n=0),s||(o=this.getLabels().length-1)),this.min=n,this.max=o}buildTicks(){const t=this.min,s=this.max,n=this.options.offset,o=[];let a=this.getLabels();a=0===t&&s===a.length-1?a:a.slice(t,s+1),this._valueRange=Math.max(a.length-(n?0:1),1),this._startValue=this.min-(n?.5:0);for(let r=t;r<=s;r++)o.push({value:r});return o}getLabelForValue(t){return lo.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const s=this.ticks;return t<0||t>s.length-1?null:this.getPixelForValue(s[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}})();function co(e,i,{horizontal:t,minRotation:s}){const n=dt(s),o=(t?Math.sin(n):Math.cos(n))||.001;return Math.min(i/o,.75*i*(""+e).length)}class ei extends jt{constructor(i){super(i),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(i,t){return O(i)||("number"==typeof i||i instanceof Number)&&!isFinite(+i)?null:+i}handleTickRangeOptions(){const{beginAtZero:i}=this.options,{minDefined:t,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const a=l=>n=t?n:l,r=l=>o=s?o:l;if(i){const l=pt(n),c=pt(o);l<0&&c<0?r(0):l>0&&c>0&&a(0)}if(n===o){let l=0===o?1:Math.abs(.05*o);r(o+l),i||a(n-l)}this.min=n,this.max=o}getTickLimit(){const i=this.options.ticks;let n,{maxTicksLimit:t,stepSize:s}=i;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),t=t||11),t&&(n=Math.min(t,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const i=this.options,t=i.ticks;let s=this.getTickLimit();s=Math.max(2,s);const a=function oh(e,i){const t=[],{bounds:n,step:o,min:a,max:r,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=e,f=o||1,p=h-1,{min:g,max:m}=i,b=!O(a),_=!O(r),y=!O(c),M=(m-g)/(d+1);let v,w,k,C,x=cs((m-g)/p/f)*f;if(x<1e-14&&!b&&!_)return[{value:g},{value:m}];C=Math.ceil(m/x)-Math.floor(g/x),C>p&&(x=cs(C*x/p/f)*f),O(l)||(v=Math.pow(10,l),x=Math.ceil(x*v)/v),"ticks"===n?(w=Math.floor(g/x)*x,k=Math.ceil(m/x)*x):(w=g,k=m),b&&_&&o&&function $o(e,i){const t=Math.round(e);return t-i<=e&&t+i>=e}((r-a)/o,x/1e3)?(C=Math.round(Math.min((r-a)/x,h)),x=(r-a)/C,w=a,k=r):y?(w=b?a:w,k=_?r:k,C=c-1,x=(k-w)/C):(C=(k-w)/x,C=le(C,Math.round(C),x/1e3)?Math.round(C):Math.ceil(C));const A=Math.max(ds(x),ds(w));v=Math.pow(10,O(l)?A:l),w=Math.round(w*v)/v,k=Math.round(k*v)/v;let D=0;for(b&&(u&&w!==a?(t.push({value:a}),w<a&&D++,le(Math.round((w+D*x)*v)/v,a,co(a,M,e))&&D++):w<a&&D++);D<C;++D){const L=Math.round((w+D*x)*v)/v;if(_&&L>r)break;t.push({value:L})}return _&&u&&k!==r?t.length&&le(t[t.length-1].value,r,co(r,M,e))?t[t.length-1].value=r:t.push({value:r}):(!_||k===r)&&t.push({value:k}),t}({maxTicks:s,bounds:i.bounds,min:i.min,max:i.max,precision:t.precision,step:t.stepSize,count:t.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:t.minRotation||0,includeBounds:!1!==t.includeBounds},this._range||this);return"ticks"===i.bounds&&hs(a,this,"value"),i.reverse?(a.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),a}configure(){const i=this.ticks;let t=this.min,s=this.max;if(super.configure(),this.options.offset&&i.length){const n=(s-t)/Math.max(i.length-1,1)/2;t-=n,s+=n}this._startValue=t,this._endValue=s,this._valueRange=s-t}getLabelForValue(i){return de(i,this.chart.options.locale,this.options.ticks.format)}}const Se=e=>Math.floor(At(e)),$t=(e,i)=>Math.pow(10,Se(e)+i);function ho(e){return e/Math.pow(10,Se(e))==1}function uo(e,i,t){const s=Math.pow(10,t),n=Math.floor(e/s);return Math.ceil(i/s)-n}function qi(e){const i=e.ticks;if(i.display&&e.display){const t=J(i.backdropPadding);return P(i.font&&i.font.size,N.font.size)+t.height}return 0}function hh(e,i,t){return t=V(t)?t:[t],{w:ra(e,i.string,t),h:t.length*i.lineHeight}}function fo(e,i,t,s,n){return e===s||e===n?{start:i-t/2,end:i+t/2}:e<s||e>n?{start:i-t,end:i}:{start:i,end:i+t}}function uh(e,i,t,s,n){const o=Math.abs(Math.sin(t)),a=Math.abs(Math.cos(t));let r=0,l=0;s.start<i.l?(r=(i.l-s.start)/o,e.l=Math.min(e.l,i.l-r)):s.end>i.r&&(r=(s.end-i.r)/o,e.r=Math.max(e.r,i.r+r)),n.start<i.t?(l=(i.t-n.start)/a,e.t=Math.min(e.t,i.t-l)):n.end>i.b&&(l=(n.end-i.b)/a,e.b=Math.max(e.b,i.b+l))}function fh(e,i,t){const s=e.drawingArea,{extra:n,additionalAngle:o,padding:a,size:r}=t,l=e.getPointPosition(i,s+n+a,o),c=Math.round(pi(Z(l.angle+$))),h=function _h(e,i,t){return 90===t||270===t?e-=i/2:(t>270||t<90)&&(e-=i),e}(l.y,r.h,c),d=function mh(e){return 0===e||180===e?"center":e<180?"left":"right"}(c),u=function bh(e,i,t){return"right"===t?e-=i:"center"===t&&(e-=i/2),e}(l.x,r.w,d);return{visible:!0,x:l.x,y:h,textAlign:d,left:u,top:h,right:u+r.w,bottom:h+r.h}}function gh(e,i){if(!i)return!0;const{left:t,top:s,right:n,bottom:o}=e;return!(vt({x:t,y:s},i)||vt({x:t,y:o},i)||vt({x:n,y:s},i)||vt({x:n,y:o},i))}function xh(e,i,t){const{left:s,top:n,right:o,bottom:a}=t,{backdropColor:r}=i;if(!O(r)){const l=Bt(i.borderRadius),c=J(i.backdropPadding);e.fillStyle=r;const h=s-c.left,d=n-c.top,u=o-s+c.width,f=a-n+c.height;Object.values(l).some(p=>0!==p)?(e.beginPath(),fe(e,{x:h,y:d,w:u,h:f,radius:l}),e.fill()):e.fillRect(h,d,u,f)}}function go(e,i,t,s){const{ctx:n}=e;if(t)n.arc(e.xCenter,e.yCenter,i,0,W);else{let o=e.getPointPosition(0,i);n.moveTo(o.x,o.y);for(let a=1;a<s;a++)o=e.getPointPosition(a,i),n.lineTo(o.x,o.y)}}const ii={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},nt=Object.keys(ii);function po(e,i){return e-i}function mo(e,i){if(O(i))return null;const t=e._adapter,{parser:s,round:n,isoWeekday:o}=e._parseOpts;let a=i;return"function"==typeof s&&(a=s(a)),j(a)||(a="string"==typeof s?t.parse(a,s):t.parse(a)),null===a?null:(n&&(a="week"!==n||!qt(o)&&!0!==o?t.startOf(a,n):t.startOf(a,"isoWeek",o)),+a)}function bo(e,i,t,s){const n=nt.length;for(let o=nt.indexOf(e);o<n-1;++o){const a=ii[nt[o]];if(a.common&&Math.ceil((t-i)/((a.steps?a.steps:Number.MAX_SAFE_INTEGER)*a.size))<=s)return nt[o]}return nt[n-1]}function _o(e,i,t){if(t){if(t.length){const{lo:s,hi:n}=bi(t,i);e[t[s]>=i?t[s]:t[n]]=!0}}else e[i]=!0}function xo(e,i,t){const s=[],n={},o=i.length;let a,r;for(a=0;a<o;++a)r=i[a],n[r]=a,s.push({value:r,major:!1});return 0!==o&&t?function Ch(e,i,t,s){const n=e._adapter,o=+n.startOf(i[0].value,s),a=i[i.length-1].value;let r,l;for(r=o;r<=a;r=+n.add(r,1,s))l=t[r],l>=0&&(i[l].major=!0);return i}(e,s,n,t):s}let Zi=(()=>class e extends jt{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,s={}){const n=t.time||(t.time={}),o=this._adapter=new Pr__date(t.adapters.date);o.init(s),ae(n.displayFormats,o.formats()),this._parseOpts={parser:n.parser,round:n.round,isoWeekday:n.isoWeekday},super.init(t),this._normalized=s.normalized}parse(t,s){return void 0===t?null:mo(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,s=this._adapter,n=t.time.unit||"day";let{min:o,max:a,minDefined:r,maxDefined:l}=this.getUserBounds();function c(h){!r&&!isNaN(h.min)&&(o=Math.min(o,h.min)),!l&&!isNaN(h.max)&&(a=Math.max(a,h.max))}(!r||!l)&&(c(this._getLabelBounds()),("ticks"!==t.bounds||"labels"!==t.ticks.source)&&c(this.getMinMax(!1))),o=j(o)&&!isNaN(o)?o:+s.startOf(Date.now(),n),a=j(a)&&!isNaN(a)?a:+s.endOf(Date.now(),n)+1,this.min=Math.min(o,a-1),this.max=Math.max(o+1,a)}_getLabelBounds(){const t=this.getLabelTimestamps();let s=Number.POSITIVE_INFINITY,n=Number.NEGATIVE_INFINITY;return t.length&&(s=t[0],n=t[t.length-1]),{min:s,max:n}}buildTicks(){const t=this.options,s=t.time,n=t.ticks,o="labels"===n.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&o.length&&(this.min=this._userMin||o[0],this.max=this._userMax||o[o.length-1]);const a=this.min,l=function Go(e,i,t){let s=0,n=e.length;for(;s<n&&e[s]<i;)s++;for(;n>s&&e[n-1]>t;)n--;return s>0||n<e.length?e.slice(s,n):e}(o,a,this.max);return this._unit=s.unit||(n.autoSkip?bo(s.minUnit,this.min,this.max,this._getLabelCapacity(a)):function Sh(e,i,t,s,n){for(let o=nt.length-1;o>=nt.indexOf(t);o--){const a=nt[o];if(ii[a].common&&e._adapter.diff(n,s,a)>=i-1)return a}return nt[t?nt.indexOf(t):0]}(this,l.length,s.minUnit,this.min,this.max)),this._majorUnit=n.major.enabled&&"year"!==this._unit?function wh(e){for(let i=nt.indexOf(e)+1,t=nt.length;i<t;++i)if(ii[nt[i]].common)return nt[i]}(this._unit):void 0,this.initOffsets(o),t.reverse&&l.reverse(),xo(this,l,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let o,a,s=0,n=0;this.options.offset&&t.length&&(o=this.getDecimalForValue(t[0]),s=1===t.length?1-o:(this.getDecimalForValue(t[1])-o)/2,a=this.getDecimalForValue(t[t.length-1]),n=1===t.length?a:(a-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;s=G(s,0,r),n=G(n,0,r),this._offsets={start:s,end:n,factor:1/(s+1+n)}}_generate(){const t=this._adapter,s=this.min,n=this.max,o=this.options,a=o.time,r=a.unit||bo(a.minUnit,s,n,this._getLabelCapacity(s)),l=P(o.ticks.stepSize,1),c="week"===r&&a.isoWeekday,h=qt(c)||!0===c,d={};let f,p,u=s;if(h&&(u=+t.startOf(u,"isoWeek",c)),u=+t.startOf(u,h?"day":r),t.diff(n,s,r)>1e5*l)throw new Error(s+" and "+n+" are too far apart with stepSize of "+l+" "+r);const g="data"===o.ticks.source&&this.getDataTimestamps();for(f=u,p=0;f<n;f=+t.add(f,l,r),p++)_o(d,f,g);return(f===n||"ticks"===o.bounds||1===p)&&_o(d,f,g),Object.keys(d).sort(po).map(m=>+m)}getLabelForValue(t){const n=this.options.time;return this._adapter.format(t,n.tooltipFormat?n.tooltipFormat:n.displayFormats.datetime)}format(t,s){return this._adapter.format(t,s||this.options.time.displayFormats[this._unit])}_tickFormatFunction(t,s,n,o){const a=this.options,r=a.ticks.callback;if(r)return z(r,[t,s,n],this);const l=a.time.displayFormats,c=this._unit,h=this._majorUnit,u=h&&l[h],f=n[s];return this._adapter.format(t,o||(h&&u&&f&&f.major?u:c&&l[c]))}generateTickLabels(t){let s,n,o;for(s=0,n=t.length;s<n;++s)o=t[s],o.label=this._tickFormatFunction(o.value,s,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const s=this._offsets,n=this.getDecimalForValue(t);return this.getPixelForDecimal((s.start+n)*s.factor)}getValueForPixel(t){const s=this._offsets,n=this.getDecimalForPixel(t)/s.factor-s.end;return this.min+n*(this.max-this.min)}_getLabelSize(t){const s=this.options.ticks,n=this.ctx.measureText(t).width,o=dt(this.isHorizontal()?s.maxRotation:s.minRotation),a=Math.cos(o),r=Math.sin(o),l=this._resolveTickFontOptions(0).size;return{w:n*a+l*r,h:n*r+l*a}}_getLabelCapacity(t){const s=this.options.time,n=s.displayFormats,o=n[s.unit]||n.millisecond,a=this._tickFormatFunction(t,0,xo(this,[t],this._majorUnit),o),r=this._getLabelSize(a),l=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return l>0?l:1}getDataTimestamps(){let s,n,t=this._cache.data||[];if(t.length)return t;const o=this.getMatchingVisibleMetas();if(this._normalized&&o.length)return this._cache.data=o[0].controller.getAllParsedValues(this);for(s=0,n=o.length;s<n;++s)t=t.concat(o[s].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let s,n;if(t.length)return t;const o=this.getLabels();for(s=0,n=o.length;s<n;++s)t.push(mo(this,o[s]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return ps(t.sort(po))}})();function si(e,i,t){let o,a,r,l,s=0,n=e.length-1;t?(i>=e[s].pos&&i<=e[n].pos&&({lo:s,hi:n}=yt(e,"pos",i)),({pos:o,time:r}=e[s]),({pos:a,time:l}=e[n])):(i>=e[s].time&&i<=e[n].time&&({lo:s,hi:n}=yt(e,"time",i)),({time:o,pos:r}=e[s]),({time:a,pos:l}=e[n]));const c=a-o;return c?r+(l-r)*(i-o)/c:r}Ni.register(Cr,oc,th,Object.freeze({__proto__:null,CategoryScale:nh,LinearScale:class ah extends ei{static id="linear";static defaults={ticks:{callback:Ee.formatters.numeric}};determineDataLimits(){const{min:i,max:t}=this.getMinMax(!0);this.min=j(i)?i:0,this.max=j(t)?t:1,this.handleTickRangeOptions()}computeTickLimit(){const i=this.isHorizontal(),t=i?this.width:this.height,s=dt(this.options.ticks.minRotation),n=(i?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(t/Math.min(40,o.lineHeight/n))}getPixelForValue(i){return null===i?NaN:this.getPixelForDecimal((i-this._startValue)/this._valueRange)}getValueForPixel(i){return this._startValue+this.getDecimalForPixel(i)*this._valueRange}},LogarithmicScale:class ch extends jt{static id="logarithmic";static defaults={ticks:{callback:Ee.formatters.logarithmic,major:{enabled:!0}}};constructor(i){super(i),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(i,t){const s=ei.prototype.parse.apply(this,[i,t]);if(0!==s)return j(s)&&s>0?s:null;this._zero=!0}determineDataLimits(){const{min:i,max:t}=this.getMinMax(!0);this.min=j(i)?Math.max(0,i):null,this.max=j(t)?Math.max(0,t):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!j(this._userMin)&&(this.min=i===$t(this.min,0)?$t(this.min,-1):$t(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:i,maxDefined:t}=this.getUserBounds();let s=this.min,n=this.max;const o=r=>s=i?s:r,a=r=>n=t?n:r;s===n&&(s<=0?(o(1),a(10)):(o($t(s,-1)),a($t(n,1)))),s<=0&&o($t(n,-1)),n<=0&&a($t(s,1)),this.min=s,this.max=n}buildTicks(){const i=this.options,s=function lh(e,{min:i,max:t}){i=ot(e.min,i);const s=[],n=Se(i);let o=function rh(e,i){let s=Se(i-e);for(;uo(e,i,s)>10;)s++;for(;uo(e,i,s)<10;)s--;return Math.min(s,Se(e))}(i,t),a=o<0?Math.pow(10,Math.abs(o)):1;const r=Math.pow(10,o),l=n>o?Math.pow(10,n):0,c=Math.round((i-l)*a)/a,h=Math.floor((i-l)/r/10)*r*10;let d=Math.floor((c-h)/Math.pow(10,o)),u=ot(e.min,Math.round((l+h+d*Math.pow(10,o))*a)/a);for(;u<t;)s.push({value:u,major:ho(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,a=o>=0?1:a),u=Math.round((l+h+d*Math.pow(10,o))*a)/a;const f=ot(e.max,u);return s.push({value:f,major:ho(f),significand:d}),s}({min:this._userMin,max:this._userMax},this);return"ticks"===i.bounds&&hs(s,this,"value"),i.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(i){return void 0===i?"0":de(i,this.chart.options.locale,this.options.ticks.format)}configure(){const i=this.min;super.configure(),this._startValue=At(i),this._valueRange=At(this.max)-At(i)}getPixelForValue(i){return(void 0===i||0===i)&&(i=this.min),null===i||isNaN(i)?NaN:this.getPixelForDecimal(i===this.min?0:(At(i)-this._startValue)/this._valueRange)}getValueForPixel(i){const t=this.getDecimalForPixel(i);return Math.pow(10,this._startValue+t*this._valueRange)}},RadialLinearScale:class kh extends ei{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Ee.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:i=>i,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(i){super(i),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const i=this._padding=J(qi(this.options)/2),t=this.width=this.maxWidth-i.width,s=this.height=this.maxHeight-i.height;this.xCenter=Math.floor(this.left+t/2+i.left),this.yCenter=Math.floor(this.top+s/2+i.top),this.drawingArea=Math.floor(Math.min(t,s)/2)}determineDataLimits(){const{min:i,max:t}=this.getMinMax(!1);this.min=j(i)&&!isNaN(i)?i:0,this.max=j(t)&&!isNaN(t)?t:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/qi(this.options))}generateTickLabels(i){ei.prototype.generateTickLabels.call(this,i),this._pointLabels=this.getLabels().map((t,s)=>{const n=z(this.options.pointLabels.callback,[t,s],this);return n||0===n?n:""}).filter((t,s)=>this.chart.getDataVisibility(s))}fit(){const i=this.options;i.display&&i.pointLabels.display?function dh(e){const i={l:e.left+e._padding.left,r:e.right-e._padding.right,t:e.top+e._padding.top,b:e.bottom-e._padding.bottom},t=Object.assign({},i),s=[],n=[],o=e._pointLabels.length,a=e.options.pointLabels,r=a.centerPointLabels?E/o:0;for(let l=0;l<o;l++){const c=a.setContext(e.getPointLabelContext(l));n[l]=c.padding;const h=e.getPointPosition(l,e.drawingArea+n[l],r),d=X(c.font),u=hh(e.ctx,d,e._pointLabels[l]);s[l]=u;const f=Z(e.getIndexAngle(l)+r),p=Math.round(pi(f));uh(t,i,f,fo(p,h.x,u.w,0,180),fo(p,h.y,u.h,90,270))}e.setCenterPoint(i.l-t.l,t.r-i.r,i.t-t.t,t.b-i.b),e._pointLabelItems=function ph(e,i,t){const s=[],n=e._pointLabels.length,o=e.options,{centerPointLabels:a,display:r}=o.pointLabels,l={extra:qi(o)/2,additionalAngle:a?E/n:0};let c;for(let h=0;h<n;h++){l.padding=t[h],l.size=i[h];const d=fh(e,h,l);s.push(d),"auto"===r&&(d.visible=gh(d,c),d.visible&&(c=d))}return s}(e,s,n)}(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(i,t,s,n){this.xCenter+=Math.floor((i-t)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(i,t,s,n))}getIndexAngle(i){return Z(i*(W/(this._pointLabels.length||1))+dt(this.options.startAngle||0))}getDistanceFromCenterForValue(i){if(O(i))return NaN;const t=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-i)*t:(i-this.min)*t}getValueForDistanceFromCenter(i){if(O(i))return NaN;const t=i/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-t:this.min+t}getPointLabelContext(i){const t=this._pointLabels||[];if(i>=0&&i<t.length){const s=t[i];return function Mh(e,i,t){return Ot(e,{label:t,index:i,type:"pointLabel"})}(this.getContext(),i,s)}}getPointPosition(i,t,s=0){const n=this.getIndexAngle(i)-$+s;return{x:Math.cos(n)*t+this.xCenter,y:Math.sin(n)*t+this.yCenter,angle:n}}getPointPositionForValue(i,t){return this.getPointPosition(i,this.getDistanceFromCenterForValue(t))}getBasePosition(i){return this.getPointPositionForValue(i||0,this.getBaseValue())}getPointLabelPosition(i){const{left:t,top:s,right:n,bottom:o}=this._pointLabelItems[i];return{left:t,top:s,right:n,bottom:o}}drawBackground(){const{backgroundColor:i,grid:{circular:t}}=this.options;if(i){const s=this.ctx;s.save(),s.beginPath(),go(this,this.getDistanceFromCenterForValue(this._endValue),t,this._pointLabels.length),s.closePath(),s.fillStyle=i,s.fill(),s.restore()}}drawGrid(){const i=this.ctx,t=this.options,{angleLines:s,grid:n,border:o}=t,a=this._pointLabels.length;let r,l,c;if(t.pointLabels.display&&function yh(e,i){const{ctx:t,options:{pointLabels:s}}=e;for(let n=i-1;n>=0;n--){const o=e._pointLabelItems[n];if(!o.visible)continue;const a=s.setContext(e.getPointLabelContext(n));xh(t,a,o);const r=X(a.font),{x:l,y:c,textAlign:h}=o;zt(t,e._pointLabels[n],l,c+r.lineHeight/2,r,{color:a.color,textAlign:h,textBaseline:"middle"})}}(this,a),n.display&&this.ticks.forEach((h,d)=>{if(0!==d||0===d&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);const u=this.getContext(d),f=n.setContext(u),p=o.setContext(u);!function vh(e,i,t,s,n){const o=e.ctx,a=i.circular,{color:r,lineWidth:l}=i;!a&&!s||!r||!l||t<0||(o.save(),o.strokeStyle=r,o.lineWidth=l,o.setLineDash(n.dash||[]),o.lineDashOffset=n.dashOffset,o.beginPath(),go(e,t,a,s),o.closePath(),o.stroke(),o.restore())}(this,f,l,a,p)}}),s.display){for(i.save(),r=a-1;r>=0;r--){const h=s.setContext(this.getPointLabelContext(r)),{color:d,lineWidth:u}=h;!u||!d||(i.lineWidth=u,i.strokeStyle=d,i.setLineDash(h.borderDash),i.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(t.reverse?this.min:this.max),c=this.getPointPosition(r,l),i.beginPath(),i.moveTo(this.xCenter,this.yCenter),i.lineTo(c.x,c.y),i.stroke())}i.restore()}}drawBorder(){}drawLabels(){const i=this.ctx,t=this.options,s=t.ticks;if(!s.display)return;const n=this.getIndexAngle(0);let o,a;i.save(),i.translate(this.xCenter,this.yCenter),i.rotate(n),i.textAlign="center",i.textBaseline="middle",this.ticks.forEach((r,l)=>{if(0===l&&this.min>=0&&!t.reverse)return;const c=s.setContext(this.getContext(l)),h=X(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){i.font=h.string,a=i.measureText(r.label).width,i.fillStyle=c.backdropColor;const d=J(c.backdropPadding);i.fillRect(-a/2-d.left,-o-h.size/2-d.top,a+d.width,h.size+d.height)}zt(i,r.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),i.restore()}drawTitle(){}},TimeScale:Zi,TimeSeriesScale:class Ph extends Zi{static id="timeseries";static defaults=Zi.defaults;constructor(i){super(i),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const i=this._getTimestampsForTable(),t=this._table=this.buildLookupTable(i);this._minPos=si(t,this.min),this._tableRange=si(t,this.max)-this._minPos,super.initOffsets(i)}buildLookupTable(i){const{min:t,max:s}=this,n=[],o=[];let a,r,l,c,h;for(a=0,r=i.length;a<r;++a)c=i[a],c>=t&&c<=s&&n.push(c);if(n.length<2)return[{time:t,pos:0},{time:s,pos:1}];for(a=0,r=n.length;a<r;++a)h=n[a+1],l=n[a-1],c=n[a],Math.round((h+l)/2)!==c&&o.push({time:c,pos:a/(r-1)});return o}_generate(){const i=this.min,t=this.max;let s=super.getDataTimestamps();return(!s.includes(i)||!s.length)&&s.splice(0,0,i),(!s.includes(t)||1===s.length)&&s.push(t),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let i=this._cache.all||[];if(i.length)return i;const t=this.getDataTimestamps(),s=this.getLabelTimestamps();return i=t.length&&s.length?this.normalize(t.concat(s)):t.length?t:s,i=this._cache.all=i,i}getDecimalForValue(i){return(si(this._table,i)-this._minPos)/this._tableRange}getValueForPixel(i){const t=this._offsets,s=this.getDecimalForPixel(i)/t.factor-t.end;return si(this._table,s*this._tableRange+this._minPos,!0)}}}));const Oh=Ni;let Th=(()=>{class e{platformId;el;type;plugins=[];width;height;responsive=!0;ariaLabel;ariaLabelledBy;get data(){return this._data}set data(t){this._data=t,this.reinit()}get options(){return this._options}set options(t){this._options=t,this.reinit()}onDataSelect=new U.bkB;isBrowser=!1;initialized;_data;_options={};chart;constructor(t,s){this.platformId=t,this.el=s}ngAfterViewInit(){this.initChart(),this.initialized=!0}onCanvasClick(t){if(this.chart){const s=this.chart.getElementsAtEventForMode(t,"nearest",{intersect:!0},!1),n=this.chart.getElementsAtEventForMode(t,"dataset",{intersect:!0},!1);s&&s[0]&&n&&this.onDataSelect.emit({originalEvent:t,element:s[0],dataset:n})}}initChart(){if((0,S.UE)(this.platformId)){let t=this.options||{};t.responsive=this.responsive,t.responsive&&(this.height||this.width)&&(t.maintainAspectRatio=!1),this.chart=new Oh(this.el.nativeElement.children[0].children[0],{type:this.type,data:this.data,options:this.options,plugins:this.plugins})}}getCanvas(){return this.el.nativeElement.children[0].children[0]}getBase64Image(){return this.chart.toBase64Image()}generateLegend(){if(this.chart)return this.chart.generateLegend()}refresh(){this.chart&&this.chart.update()}reinit(){this.chart&&(this.chart.destroy(),this.initChart())}ngOnDestroy(){this.chart&&(this.chart.destroy(),this.initialized=!1,this.chart=null)}static \u0275fac=function(s){return new(s||e)(U.rXU(U.Agw),U.rXU(U.aKT))};static \u0275cmp=U.VBU({type:e,selectors:[["p-chart"]],hostAttrs:[1,"p-element"],inputs:{type:"type",plugins:"plugins",width:"width",height:"height",responsive:"responsive",ariaLabel:"ariaLabel",ariaLabelledBy:"ariaLabelledBy",data:"data",options:"options"},outputs:{onDataSelect:"onDataSelect"},decls:2,vars:8,consts:[[2,"position","relative"],["role","img",3,"click"]],template:function(s,n){1&s&&(U.j41(0,"div",0)(1,"canvas",1),U.bIt("click",function(a){return n.onCanvasClick(a)}),U.k0s()()),2&s&&(U.xc7("width",n.responsive&&!n.width?null:n.width)("height",n.responsive&&!n.height?null:n.height),U.R7$(1),U.BMQ("aria-label",n.ariaLabel)("aria-labelledby",n.ariaLabelledBy)("width",n.responsive&&!n.width?null:n.width)("height",n.responsive&&!n.height?null:n.height))},encapsulation:2,changeDetection:0})}return e})(),Lh=(()=>{class e{static \u0275fac=function(s){return new(s||e)};static \u0275mod=U.$C({type:e});static \u0275inj=U.G2t({imports:[S.MD]})}return e})()}}]);