import { Injectable } from '@angular/core';
import { CommonService } from './common.service';
import { BaseUrlService } from './base-url.service';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { WholeSale } from '../models/whole-sale';
import { ToastModel } from '../models/toast.model';
import { DatePipe } from '@angular/common';
import { commercialRegions } from '../mocks/mocks';

@Injectable({
  providedIn: 'root'
})
export class WholeSaleService {

  url: string = '';
  base_url: string;
  getWholeSaleBoolean: boolean = false;
  wholeSaleDetail: WholeSale;

  constructor(
    private commonSrv: CommonService,
    private baseUrlService: BaseUrlService,
    private http: HttpClient,
  ) {
    this.url = this.baseUrlService.getOrigin() + environment.basePath;
  }

  async createWholeSale(wholeSale: Partial<WholeSale>): Promise<any> {
    try {
      const response = await this.http.post<WholeSale>(`${this.url}whole-sale`, wholeSale).toPromise();
      this.commonSrv.showToast(
        {
          color: 'success',
          message: 'Demi gros creé avec succès',
        }
      );
      return response;
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error);
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      };
      await this.commonSrv.showToast(toastMessage);
      return error;
    }
  }

  async getWholeSale(param: any): Promise<{ data: WholeSale[], count: number }> {

    try {
      let params = new HttpParams();

      const { offset, limit, startDate, endDate, tel, name, commercialRegion, animateDonutId } = param;

      if (startDate && endDate) {
        params = params.append('startDate', new DatePipe('fr').transform(startDate, 'YYYY-MM-dd'));
        params = params.append('endDate', new DatePipe('fr').transform(endDate, 'YYYY-MM-dd'));
      }
      if (offset) params = params.append('offset', offset);
      if (limit) params = params.append('limit', limit);
      if (name) params = params.append('firstName', name);
      if (commercialRegion) params = params.append('address.commercialRegion', commercialRegion);
      if (animateDonutId) params = params.append('associatedDonutAnimator._id', animateDonutId);
      if (tel) params = params.append('tel', tel);
      params = params.append('enable', true);

      const response = await this.http.get<{ data: WholeSale[], count: number }>(`${this.url}whole-sale`, { params }).toPromise();

      // Normaliser les données pour gérer les deux formats d'ID (string et ObjectId)
      if (response?.data) {
        response.data = response.data.map(wholeSale => this.normalizeWholeSale(wholeSale));
      }

      return response;
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error);
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      };
      await this.commonSrv.showToast(toastMessage);
      return error;
    }
  }

  /**
   * Normalise les données WholeSale pour gérer les deux formats d'ID
   * (string et ObjectId) dans associatedDonutAnimator
   */
  private normalizeWholeSale(wholeSale: WholeSale): WholeSale {
    if (wholeSale.associatedDonutAnimator) {
      // Si l'ID est un ObjectId avec $oid, on le garde tel quel
      // Si c'est un string, on le convertit en format ObjectId pour la cohérence
      const currentId = wholeSale.associatedDonutAnimator._id;

      if (typeof currentId === 'string') {
        // Convertir le string en format ObjectId
        wholeSale.associatedDonutAnimator._id = { $oid: currentId };
      }

      console.log('🔄 Normalisation WholeSale:', {
        originalId: currentId,
        normalizedId: wholeSale.associatedDonutAnimator._id,
        wholeSaleName: wholeSale.name
      });
    }

    return wholeSale;
  }

  async find(id: string): Promise<WholeSale> {
    try {
      const response = await this.http.get<WholeSale>(`${this.url}whole-sale/${id}`).toPromise();
      return this.normalizeWholeSale(response);
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error);
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      };
      await this.commonSrv.showToast(toastMessage);
      return null;
    }
  }

  async updateWholeSale(wholeSale: WholeSale): Promise<any> {
    try {
      const response = await this.http.patch<WholeSale>(`${this.url}whole-sale/${wholeSale._id}`, wholeSale).toPromise();
      this.commonSrv.showToast(
        {
          color: 'success',
          message: 'Demi gros modifié avec succès',
        }
      );
      return response;
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error);
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      };
      await this.commonSrv.showToast(toastMessage);
      return error;
    }
  }
}


