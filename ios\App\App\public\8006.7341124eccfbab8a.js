"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8006],{40129:(S,C,r)=>{r.d(C,{vi:()=>P});const P=(0,r(22126).F3)("BarcodeScanner",{web:()=>r.e(5499).then(r.bind(r,85499)).then(e=>new e.BarcodeScannerWeb)})},6239:(S,C,r)=>{r.r(C),r.d(C,{RecapScanPageModule:()=>F});var d=r(56610),p=r(37222),i=r(77897),_=r(77575),s=r(73308),P=r(39316),e=r(14599),b=r(81559),h=r(82571),M=r(99987),n=r(2978),m=r(26409),u=r(68896),a=r(62049),c=r(13217),o=r(44444),E=r(17709),f=r(58133),R=r(54648),T=r(71333),y=r(74657);function A(l,j){1&l&&n.nrm(0,"app-progress-spinner")}function x(l,j){if(1&l&&(n.j41(0,"div",14)(1,"p"),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"div",15)(5,"label",16),n.EFF(6),n.nI1(7,"translate"),n.k0s(),n.j41(8,"div",17),n.EFF(9),n.k0s()(),n.j41(10,"div",15)(11,"label",16),n.EFF(12,"Type de client"),n.k0s(),n.j41(13,"div",17),n.EFF(14),n.k0s()(),n.j41(15,"div",15)(16,"label",16),n.EFF(17),n.nI1(18,"translate"),n.k0s(),n.j41(19,"div",17),n.EFF(20),n.k0s()(),n.j41(21,"div",15)(22,"label",16),n.EFF(23),n.nI1(24,"translate"),n.k0s(),n.j41(25,"div",17),n.EFF(26),n.k0s()(),n.j41(27,"div",15)(28,"label",16),n.EFF(29),n.nI1(30,"translate"),n.k0s(),n.j41(31,"div",17),n.EFF(32),n.k0s()(),n.j41(33,"div",15)(34,"label",16),n.EFF(35,"Quartier"),n.k0s(),n.j41(36,"div",17),n.EFF(37),n.k0s()()()),2&l){const t=n.XpG();n.R7$(2),n.SpI(" ",n.bMT(3,11,"qr-orders.user-info")," "),n.R7$(4),n.JRh(n.bMT(7,13,"user-info.full-name")),n.R7$(3),n.SpI(" ",null==t.user?null:t.user.firstName,""),n.R7$(5),n.JRh(t.getCategory(null==t.user?null:t.user.categoryType)),n.R7$(3),n.JRh(n.bMT(18,15,"user-info.phone")),n.R7$(3),n.JRh(null==t.user?null:t.user.tel),n.R7$(3),n.JRh(n.bMT(24,17,"user-info.region")),n.R7$(3),n.JRh(null==t.user?null:t.user.address.region),n.R7$(3),n.JRh(n.bMT(30,19,"indirect-clients.ville")),n.R7$(3),n.JRh(null==t.user?null:t.user.address.city),n.R7$(5),n.JRh(null==t.user||null==t.user.address?null:t.user.address.neighborhood)}}const D=[{path:"",component:(()=>{class l{constructor(){this.orderSrv=(0,n.WQX)(b.Q),this.scannerSrv=(0,n.WQX)(u.I),this.qrcodeSrv=(0,n.WQX)(E.Q),this.storageService=(0,n.WQX)(e.n),this.productSrv=(0,n.WQX)(P.b),this.commonSrv=(0,n.WQX)(h.h),this.translateService=(0,n.WQX)(a.E),this.fidelitySrv=(0,n.WQX)(c._),this.location=(0,n.WQX)(d.aZ),this.route=(0,n.WQX)(_.Ix),this.isFrench=this.translateService.currentLang===M.T.French,this.storageService.getUserConnected(),this.user=this.qrcodeSrv.currenUser}ngOnInit(){var t=this;return(0,s.A)(function*(){t.isLoading=!0,t.cart={...JSON.parse(t.storageService.load("cart")),items:JSON.parse(t.storageService.load("items"))};const g=yield t.fidelitySrv.getPoints({});t.totalPoints=t.fidelitySrv.calculateTotalPointsOrder(t.cart?.items,g),t.supplier=JSON.parse(t.storageService.load("supplier")),t.isLoading=!1})()}ionViewWillEnter(){return(0,s.A)(function*(){})()}validatePoint(){var t=this;return(0,s.A)(function*(){try{t.isLoading=!0;let g=null;const O=t.storageService.load("USER_INFO");if(O)try{g=JSON.parse(O)}catch(v){console.error("Error parsing USER_INFO:",v)}if(!g)return t.isLoading=!1,void t.commonSrv?.showToast?.({color:"danger",message:"User information not found"});const N=g?.category===f.s.Commercial?t.user:g;if(!t.supplier)return t.isLoading=!1,void t.commonSrv?.showToast?.({color:"danger",message:"Supplier information not found"});if(!t.cart)return t.isLoading=!1,void t.commonSrv?.showToast?.({color:"danger",message:"Cart information not found"});const k={user:N,supplier:t.supplier,cart:{...t.cart},qrCodeData:JSON.parse(t.storageService.load("qrCodeData"))};let U;try{U=yield t.scannerSrv.validateScanData(k)}catch(v){return console.error("Error validating scan data:",v),t.isLoading=!1,void t.commonSrv?.showToast?.({color:"danger",message:"Error validating scan data"})}if(t.isLoading=!1,U instanceof m.yz){const v=U?.error?.message||"Operation failed";t.commonSrv?.showToast?.({color:"danger",message:v})}else{try{t.storageService.remove("items"),t.storageService.remove("cart"),t.storageService.remove("suppliers"),t.storageService.remove("qrCodeData"),t.productSrv.dataQrCode=[],t.productSrv.currentDataProductScan=[]}catch(v){console.error("Error removing storage items:",v)}[f.s.Commercial,f.s.DonutAnimator].includes(g?.category)?(t.qrcodeSrv.currenUser=null,t.user=null,t.route.navigate(["order/history/list-order"]).catch(v=>console.error("Navigation error:",v))):t.route.navigate(["navigation/fidelity-program"]).catch(v=>console.error("Navigation error:",v))}}catch(g){console.error("Unexpected error in validatePoint:",g),t.isLoading=!1,t.commonSrv?.showToast?.({color:"danger",message:"An unexpected error occurred"})}finally{t.isLoading=!1}})()}back(){this.location.back()}getCategory(t){switch(t){case o.iL.BHB:return"BHB";case o.iL.BS:return"BS";case o.iL.BPI:return"BPI";default:return"Unknown Category"}}static{this.\u0275fac=function(g){return new(g||l)}}static{this.\u0275cmp=n.VBU({type:l,selectors:[["app-recap-scan"]],decls:24,vars:15,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[4,"ngIf"],[1,"scroll-container"],[3,"cart","isPoint","points"],[1,"profile-container"],[1,"avatar"],["name","person-outline"],[1,"info"],[1,"title"],[1,"name"],["class","user-info-container",4,"ngIf"],[1,"btn-validate"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"click"],[1,"user-info-container"],[1,"user-info-item"],[1,"user-info-label"],[1,"user-info-value"]],template:function(g,O){1&g&&(n.j41(0,"ion-header")(1,"div",0)(2,"ion-img",1),n.bIt("click",function(){return O.back()}),n.k0s(),n.j41(3,"ion-title"),n.EFF(4),n.nI1(5,"translate"),n.k0s()()(),n.DNE(6,A,1,0,"app-progress-spinner",2),n.j41(7,"section",3),n.nrm(8,"app-purchase-summary",4),n.j41(9,"div",5)(10,"div",6),n.nrm(11,"ion-icon",7),n.k0s(),n.j41(12,"div",8)(13,"div",9),n.EFF(14),n.nI1(15,"translate"),n.k0s(),n.j41(16,"div",10),n.EFF(17),n.k0s()()(),n.DNE(18,x,38,21,"div",11),n.k0s(),n.j41(19,"div",12)(20,"ion-button",13),n.bIt("click",function(){return O.validatePoint()}),n.j41(21,"ion-label"),n.EFF(22),n.nI1(23,"translate"),n.k0s()()()),2&g&&(n.R7$(4),n.SpI(" ",n.bMT(5,9,"recap-scan.title")," "),n.R7$(2),n.Y8G("ngIf",O.isLoading),n.R7$(2),n.Y8G("cart",O.cart)("isPoint",!0)("points",O.totalPoints),n.R7$(6),n.SpI(" ",n.bMT(15,11,"recap-scan.supplier")," "),n.R7$(3),n.JRh(null==O.supplier?null:O.supplier.name),n.R7$(1),n.Y8G("ngIf",O.user),n.R7$(4),n.SpI(" ",n.bMT(23,13,"recap-scan.validate")," "))},dependencies:[d.bT,i.Jm,i.eU,i.iq,i.KW,i.he,i.BC,R.N,T._,y.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.scroll-container[_ngcontent-%COMP%]{height:100%;overflow-y:auto;scroll-behavior:smooth}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;height:80%;margin:0 calc(41 * var(--res));background-color:transparent;overflow-x:hidden;overflow-y:auto}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .fextrabold[_ngcontent-%COMP%]{font-family:Mont Bold;font-weight:700;font-size:calc(40.7 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .fMeduim[_ngcontent-%COMP%]{font-family:Mont Light}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .primary[_ngcontent-%COMP%]{color:#143c5d!important}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .recap-title[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .totalTtc[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .totalTtc[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{padding:0 calc(37.5 * var(--res));height:calc(120 * var(--res));display:flex;background-color:#c62f45e0;color:#fff;align-items:center;justify-content:space-between;border-radius:calc(20 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .type-truck[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin:1em 0;padding:0 calc(41 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .type-truck[_ngcontent-%COMP%]   ion-toggle[_ngcontent-%COMP%]{color:#0d7d3d;--background-checked: #0d7d3d}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:calc(40 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .discount-details[_ngcontent-%COMP%]{padding-top:.5em;padding-bottom:1em;border-top:1px solid rgba(128,128,128,.448)}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-family:Mont Bold}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;display:block;font-size:calc(45 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{height:calc(12.25 * var(--resH));box-shadow:0 3.08499px 10.7975px #00000016;border:.771248px solid rgba(218,218,218,.47);border-radius:3.85624px;--background: #ffffff}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{--background: var(--ion-color-primary)}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .cancel-discount[_ngcontent-%COMP%]{display:inline-block;padding:.6em;color:#fff;background-color:var(--ion-color-secondary);border-radius:5px;margin:0 0 1em}.scroll-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;align-items:center;gap:5px;margin-bottom:20%}.scroll-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{text-align:center;color:#419cfb;font-family:Mont Bold;font-size:16px;line-height:1.4;max-width:280px;margin:0 auto}.scroll-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;border-bottom:.6px solid #419cfb;border-radius:3px;width:83%;font-weight:400;height:2em;padding:16px}.scroll-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%], .scroll-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]   .user-info-value[_ngcontent-%COMP%]{font-weight:600;color:#0b305c;font-size:15px}.profile-container[_ngcontent-%COMP%]{display:flex;align-items:center;background:#E4EBF3;padding:12px;border-radius:12px;margin:24px calc(41 * var(--res))}.avatar[_ngcontent-%COMP%]{width:40px;height:40px;background:#e1e1e1;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:12px}.avatar[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#666}.info[_ngcontent-%COMP%]{display:flex;flex-direction:column}.title[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-bottom:2px}.name[_ngcontent-%COMP%]{font-size:16px;color:#333;font-family:Mont SemiBold}.btn-validate[_ngcontent-%COMP%]{padding:0 calc(25 * var(--res));position:fixed;bottom:0;width:calc(100% - 10 * var(--res))}ion-button[_ngcontent-%COMP%]{margin:1em 0}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont SemiBold;margin:1em 0}.header[_ngcontent-%COMP%]{--background: #F1F2F4;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}"]})}}return l})()}];let W=(()=>{class l{static{this.\u0275fac=function(g){return new(g||l)}}static{this.\u0275mod=n.$C({type:l})}static{this.\u0275inj=n.G2t({imports:[_.iI.forChild(D),_.iI]})}}return l})();var I=r(93887);let F=(()=>{class l{static{this.\u0275fac=function(g){return new(g||l)}}static{this.\u0275mod=n.$C({type:l})}static{this.\u0275inj=n.G2t({imports:[d.MD,p.YN,i.bv,I.G,y.h,W]})}}return l})()},39316:(S,C,r)=>{r.d(C,{b:()=>h});var d=r(73308),p=r(26409),i=r(94934),_=r(45312),s=r(2978),P=r(82571),e=r(33607),b=r(77897);let h=(()=>{class M{constructor(m,u,a,c){this.http=m,this.commonSrv=u,this.baseUrlService=a,this.toastController=c,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+_.c.basePath+"products"}getProducts(m){var u=this;return(0,d.A)(function*(){try{let a=new p.Nl;return m?.limit&&(a=a.append("limit",m?.limit)),yield(0,i.s)(u.http.get(u.url,{params:a}))}catch(a){const o={message:u.commonSrv.getError("",a).message,color:"danger"};return yield u.commonSrv.showToast(o),a}})()}getProduct(m){var u=this;return(0,d.A)(function*(){try{return yield(0,i.s)(u.http.get(`${u.url}/${m}`))}catch(a){const o={message:u.commonSrv.getError("",a).message,color:"danger"};return yield u.commonSrv.showToast(o),a}})()}static{this.\u0275fac=function(u){return new(u||M)(s.KVO(p.Qq),s.KVO(P.h),s.KVO(e.K),s.KVO(b.K_))}}static{this.\u0275prov=s.jDH({token:M,factory:M.\u0275fac,providedIn:"root"})}}return M})()},94440:(S,C,r)=>{r.d(C,{c:()=>p});var d=r(2978);let p=(()=>{class i{transform(s,...P){return s?s.length>P[0]?`${s.substring(0,P[0]-3)}...`:s:""}static{this.\u0275fac=function(P){return new(P||i)}}static{this.\u0275pipe=d.EJ8({name:"truncateString",type:i,pure:!0})}}return i})()},68896:(S,C,r)=>{r.d(C,{I:()=>n});var d=r(73308),p=r(2978),i=r(40129),_=r(82571),s=r(45312),P=r(26409),e=r(33607),b=r(14599),h=r(94934),M=r(56610);let n=(()=>{class m{constructor(){this.commonSrv=(0,p.WQX)(_.h),this.http=(0,p.WQX)(P.Qq),this.baseUrl=(0,p.WQX)(e.K),this.storageSrv=(0,p.WQX)(b.n),this.base_url=`${this.baseUrl.getOrigin()}${s.c.basePath}`}validateScanData(a){var c=this;return(0,d.A)(function*(){try{return yield(0,h.s)(c.http.post(`${c.base_url}scanner-data`,a))}catch(o){const f={message:c.commonSrv.getError("",o).message,color:"danger"};return yield c.commonSrv.showToast(f),o}})()}checkPermission(){return(0,d.A)(function*(){try{const{camera:a}=yield i.vi.requestPermissions();return"granted"===a}catch(a){return console.log(a),!1}})()}stopScan(){var a=this;return(0,d.A)(function*(){a.currDisplay=!1,document.querySelector("body").classList.remove("scanner-active")})()}showContent(){document.querySelectorAll(".hide-on-scan").forEach(c=>{c.style.display=""}),document.querySelector("body").classList.remove("scanner-active")}prepareScanner(){return(0,d.A)(function*(){document.body.classList.add("scanner-active")})()}startScan(){var a=this;return(0,d.A)(function*(){try{if(!(yield a.checkPermission()))return void(yield a.commonSrv.showToast({color:"danger",message:"Permission refus\xe9e pour utiliser la cam\xe9ra"}));yield a.prepareScanner(),yield i.vi.installGoogleBarcodeScannerModule(),console.log("\u2705 Module MLKit install\xe9 avec succ\xe8s");const{barcodes:o}=yield i.vi.scan();if(a.restoreUI(),o&&o.length>0)return console.log("\u{1f3af} Scan r\xe9ussi:",o[0]),o[0].rawValue||o[0].displayValue;yield a.commonSrv.showToast({color:"warning",message:"Aucun code-barres d\xe9tect\xe9"})}catch(c){console.error("\u274c Erreur MLKit scan:",c),yield a.commonSrv.showToast({color:"danger",message:"Erreur lors du scan"})}finally{a.stopScan()}})()}restoreUI(){document.body.classList.remove("scanner-active")}getVolumeOrderByParticularClient(a){var c=this;return(0,d.A)(function*(){let o=new P.Nl;const{status:E=300,offset:f,limit:R,enable:T=!0,associatedCommercialId:y,startDate:A,endDate:x,customerName:L}=a;void 0!==f&&(o=o.append("offset",f)),R&&(o=o.append("limit",R)),E&&(o=o.append("status",E)),y&&(o=o.append("user.associatedCommercial._id",y)),o=o.append("enable",T),A&&x&&(o=o.append("startDate",new M.vh("fr").transform(A,"YYYY-MM-dd")),o=o.append("endDate",new M.vh("fr").transform(x,"YYYY-MM-dd"))),L&&(o=o.append("user.firstName",L));try{return yield(0,h.s)(c.http.get(`${c.base_url}scanner-data/volume-order-by-particular-client`,{params:o}))}catch(D){const I={message:c.commonSrv.getError("",D).message,color:"danger"};return yield c.commonSrv.showToast(I),D}})()}static{this.\u0275fac=function(c){return new(c||m)}}static{this.\u0275prov=p.jDH({token:m,factory:m.\u0275fac,providedIn:"root"})}}return m})()}}]);