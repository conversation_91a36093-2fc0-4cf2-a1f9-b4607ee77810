<app-progress-spinner *ngIf="isLoading"></app-progress-spinner>
<ion-header>
  <ion-toolbar class="header">
    <ion-img (click)="back()" slot="start" src="/assets/icons/arrow-blue.svg"></ion-img>
    <ion-title class="title">{{ "order-new-page.first-step.choose-store" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<div id="container">
  <ion-card class="custom-card" [formGroup]="filterForm">
    <div class="container">
      <div class="image">
        <ion-img src="../../../../assets/icons/man.png"></ion-img>
      </div>
      <div class="company relative">
        <ion-input [clearInput]="true" placeholder="{{ 'order-new-page.first-step.supplier' | translate }}"
          (input)="filterSuppliers($event)" (focus)="showSuggestions = true" formControlName="name" type="text">
          <div *ngIf="filteredSuppliers.length > 0 && showSuggestions" class="autocomplete-list">
            <div *ngFor="let supplier of filteredSuppliers" class="autocomplete-item"
              (click)="selectSupplier(supplier); $event.stopPropagation()">
              {{ supplier.name }}
            </div>
          </div>
        </ion-input>
        <ion-button expand="block" (click)="nextStep(selectedSupplier || otherSupplier)">
          {{ "order-new-page.first-step.choose" | translate }}
        </ion-button>
      </div>
    </div>
  </ion-card>
  <ion-card *ngFor="let company of suppliers" class="custom-card" (click)="nextStep(company)">
    <div class="container">
      <div class="image">
        <ion-img src="../../../../assets/icons/man.png"></ion-img>
      </div>
      <div class="company">
        <label for="">{{ company?.name | truncateString:20 }}</label>
        <p>{{ commonService?.user?.address?.region | truncateString:28 }}</p>
        <ion-button expand="block">
          {{ "order-new-page.first-step.choose" | translate }}
        </ion-button>
      </div>
      <div class="chevron">
        <ion-icon slot="end" name="chevron-forward-outline" color="primary"></ion-icon>
      </div>
    </div>
  </ion-card>

</div>