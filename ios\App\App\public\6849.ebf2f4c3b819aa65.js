"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6849],{46849:(m,s,t)=>{t.r(s),t.d(s,{ReportingModule:()=>g});var h=t(56610),d=t(77575),o=t(2978);const i=[{path:"",loadChildren:()=>t.e(4103).then(t.bind(t,64103)).then(n=>n.ReportingListPageModule)},{path:"reporting-stats",loadChildren:()=>Promise.all([t.e(9996),t.e(3385),t.e(2076),t.e(9284)]).then(t.bind(t,69284)).then(n=>n.ReportingStatsPageModule)},{path:"reporting-stats-retailler",loadChildren:()=>Promise.all([t.e(3385),t.e(120)]).then(t.bind(t,83385)).then(n=>n.RepportingStatsRetaillerPageModule)},{path:"reporting-purchases-evolution",loadChildren:()=>Promise.all([t.e(7438),t.e(7186),t.e(2462)]).then(t.bind(t,52462)).then(n=>n.ReportingPurchasesEvolutionPageModule)},{path:"reporting-purchases-quantity",loadChildren:()=>Promise.all([t.e(7438),t.e(7186),t.e(4047)]).then(t.bind(t,44047)).then(n=>n.ReportingPurchasesQuantityPageModule)},{path:"reporting-top-products",loadChildren:()=>Promise.all([t.e(7438),t.e(7186),t.e(9996),t.e(2076),t.e(1907)]).then(t.bind(t,71907)).then(n=>n.ReportingTopProductsPageModule)},{path:"repporting-stats-retailler",loadChildren:()=>Promise.all([t.e(3385),t.e(120)]).then(t.bind(t,83385)).then(n=>n.RepportingStatsRetaillerPageModule)},{path:"reporting-sales-evolutions",loadChildren:()=>Promise.all([t.e(7438),t.e(7186),t.e(9996),t.e(6470)]).then(t.bind(t,66470)).then(n=>n.ReportingSalesEvolutionsPageModule)},{path:"reporting-order-particular-associated",loadChildren:()=>Promise.all([t.e(7438),t.e(7186),t.e(9069)]).then(t.bind(t,99069)).then(n=>n.ReportingOrderParticularAssociatedPageModule)}];let a=(()=>{class n{static{this.\u0275fac=function(l){return new(l||n)}}static{this.\u0275mod=o.$C({type:n})}static{this.\u0275inj=o.G2t({imports:[d.iI.forChild(i),d.iI]})}}return n})(),g=(()=>{class n{static{this.\u0275fac=function(l){return new(l||n)}}static{this.\u0275mod=o.$C({type:n})}static{this.\u0275inj=o.G2t({imports:[h.MD,a]})}}return n})()}}]);