import { Activated<PERSON>oute, Router } from '@angular/router';
import { LoadingController, Platform } from '@ionic/angular';
import { Location } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { ProductService } from '../../services/product.service';
import { StorageService } from 'src/app/shared/services/storage.service';
import { ScannerService } from 'src/app/shared/services/scanner.service';
import { MethodServiceService } from 'src/app/shared/services/method-service.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { Language } from 'src/app/shared/enum/language.enum';
import { environment } from 'src/environments/environment';
import { TranslateConfigService } from 'src/app/shared/services/translate-config.service';
import { QrCodeService } from 'src/app/shared/services/qr-code.service';
import { QrCodeData, QrCodeStatus } from 'src/app/shared/models/qr-code';
import { UserService } from 'src/app/shared/services/user.service';
import { Particular, ParticularCategory } from 'src/app/shared/models/user.models';
import { BarcodeScanner } from '@capacitor-mlkit/barcode-scanning';
import { UserCategory } from 'src/app/shared/enum/user-category.enum';

@Component({
  selector: 'app-product-scan',
  templateUrl: './product-scan.page.html',
  styleUrls: ['./product-scan.page.scss'],
})
export class ProductScanPage implements OnInit {
  isLoading: boolean;
  manualOrderEnabled: boolean;
  scannerSrv = inject(ScannerService);
  platform = inject(Platform);
  protected userSrv = inject(UserService);
  user: Particular | null = null;
  private route = inject(ActivatedRoute)


  private manualOrderService = inject(MethodServiceService);

  constructor(
    private router: Router,
    private location: Location,
    private qrCodeSrv: QrCodeService,
    private commonSrv: CommonService,
    public productSrv: ProductService,
    private storageService: StorageService,
    private loadingCtrl: LoadingController,
    private translateService: TranslateConfigService
  ) { }

  async ngOnInit(): Promise<void> {
    if (this.route.snapshot.params['id']) {
      await this.getUser(this.route.snapshot.params['id']);
    }
    await this.fetchManualOrderSetting();
  }

  ionViewWillEnter() {
    this.commonSrv.showNav = false;
  }

  back() {
    this.location.back();
  }

  trackByFn(index: any, item: any): any {
    return index;
  }

  showDetail(): void {
    this.router.navigate(['/item-detail']);
  }

  async showScan(): Promise<string | void> {
    // Le module Google Barcode Scanner est initialisé au démarrage de l'application
    await this.platform.ready();

    const { supported } = await BarcodeScanner.isSupported();
    if (!supported) return this.commonSrv.showToast({
      message: this.translateService.currentLang === Language.French
        ? 'Scanner non pris en charge sur cet appareil'
        : 'Scanner not supported on this device',
      color: 'warning',
    });
    this.scannerSrv.currDisplay = true;

    const result = await this.scannerSrv.startScan();
    console.log('result first level::::', result);
    this.scannerSrv.currDisplay = false;
    return result;
  }

  async openScan() {
    let loading: HTMLIonLoadingElement | undefined;
    try {
      // 1. Scan ou valeur de dev
      const scanResult = environment?.dev
        ? '{"code":"a123456789"}'
        : await this.showScan();

      if (!scanResult) {
        await this.commonSrv.showToast({
          color: 'danger',
          message:
            this.translateService.currentLang === Language.French
              ? 'Aucun résultat de scan'
              : 'No scan result',
        });
        return;
      }

      // 2. Parsing sécurisé
      let dataQrCode: Partial<QrCodeData>
      try {
        dataQrCode = JSON.parse(scanResult);
      } catch {
        await this.commonSrv.showToast({
          color: 'danger',
          message:
            this.translateService.currentLang === Language.French
              ? 'QR code invalide'
              : 'Invalid QR code',
        });
        return;
      }

      if (!dataQrCode?.code) {
        await this.commonSrv.showToast({
          color: 'danger',
          message:
            this.translateService.currentLang === Language.French
              ? 'Code absent du QR code'
              : 'Code is missing from the QR code',
        });
        return;
      }

      // 3. Loading
      loading = await this.loadingCtrl.create({
        message:
          this.translateService.currentLang === Language.French
            ? `Vérification du scan...\nCode: ${dataQrCode?.code}`
            : `Checking scan...\nCode: ${dataQrCode?.code}`,
      });
      await loading.present();

      // 4. Déjà scanné ?
      if (await this.verifyScan(dataQrCode)) return;

      // 5. Vérification côté serveur
      const qrCode = await this.qrCodeSrv.getQrCodeDataAnUpDateStateToScanned({
        code: dataQrCode?.code, particularUser: this.user?.category === UserCategory.Particular ? this.user?._id : null
      });

      console.log(`==========QR Code data: ${JSON.stringify(qrCode)}=================`);


      if (qrCode instanceof Error || !qrCode?.code) {
        console.log(`==========QR Code not found or error: ${JSON.stringify(qrCode)}=================`);

        await this.commonSrv.showToast({
          message: this.translateService.currentLang === Language.French
            ? 'QR Code non actif ou invalide'
            : 'QR Code is inactive or invalid',
          color: 'danger',
        });
        return;
      }

      if (qrCode?.status === QrCodeStatus.USED) {
        console.log(`==========QR Code already used: ${JSON.stringify(qrCode)}=================`);

        await this.commonSrv.showToast({
          message: this.translateService.currentLang === Language.French
            ? "QR Code déjà utilisé"
            : 'QR Code is already used',
          color: 'danger',
        });
        return;
      }

      // 6. Ajout du produit
      await this.addProduct(qrCode, dataQrCode);

    } catch (error) {
      console.error('Error in openScan:', error);
      await this.commonSrv.showToast({
        message:
          this.translateService.currentLang === Language.French
            ? "Une erreur est survenue lors du scan"
            : "An error occurred during scan",
        color: 'danger',
      });
      return error;
    } finally {
      if (loading) await loading.dismiss();
    }
  }


  async verifyScan(scanResult: Partial<QrCodeData>): Promise<boolean> {
    // Vérification si le code existe déjà dans les données scannées
    const existingIndex = this.productSrv.dataQrCode.findIndex(
      (item) => item.code === scanResult?.code
    );
    if (existingIndex !== -1) {
      // Si le code existe déjà, on ne l'ajoute pas
      console.log(`==========QR Code already scanned: ${scanResult?.code}=================`);

      await this.commonSrv.showToast({
        message: this.translateService.currentLang === Language.French
          ? 'QR Code déjà scanné'
          : 'QR Code is already scanned',
        color: 'danger',
      });
      return true;
    }

    return false;
  }

  async addProduct(scanResult: QrCodeData, data: Partial<QrCodeData>): Promise<void> {

    // Recherche d'un produit identique (même produit et même packaging)
    const existingIndex = this.productSrv.currentDataProductScan.findIndex(
      (item) =>
        item?.product?._id === scanResult?.product?._id &&
        item?.packaging?._id === scanResult?.packaging?._id
    );

    if (existingIndex !== -1) {
      // Met à jour la quantité de l'article existant
      const currentQuantity = this.productSrv.currentDataProductScan[existingIndex].quantity || 1;
      this.productSrv.currentDataProductScan[existingIndex].quantity = currentQuantity + 1;
    } else {
      // Ajoute le nouvel article avec une quantité de 1 et le code scanné
      this.productSrv.currentDataProductScan.unshift({
        ...scanResult,
        code: data?.code,
        quantity: 1,
      });
    }

    this.productSrv.dataQrCode.push(scanResult);
  }

  nextStep(): void {
    const products = this.productSrv.currentDataProductScan.filter(
      (x) => x.quantity && x.quantity > 0
    );
    this.storageService.store('items', JSON.stringify(products));
    this.storageService.store('qrCodeData', JSON.stringify(this.productSrv.dataQrCode));
    this.router.navigate(['/order/choice-suppliers']);
  }

  openManuelOrder() {
    this.router.navigate(['/order/particular-order/first-step']);
  }

  async fetchManualOrderSetting() {
    try {
      const response = await this.manualOrderService.getManualOrderSetting();
      this.manualOrderEnabled = response;
    } catch (error) {
      console.error(
        'Erreur lors de la récupération de la configuration :',
        error
      );
    }
  }

  async getUser(idUser: string) {
    this.user = await this.userSrv.find(idUser);
    this.qrCodeSrv.currenUser = this.user

  }

  getCategory(code: number): string {
    switch (code) {
      case ParticularCategory.BHB:
        return 'BHB';
      case ParticularCategory.BS:
        return 'BS';
      case ParticularCategory.BPI:
        return 'BPI';
      default:
        return 'Unknown Category';
    }
  }



}
