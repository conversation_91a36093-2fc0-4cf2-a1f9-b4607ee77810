<ion-header [translucent]="true" [ngStyle]="{opacity: scannerSrv?.currDisplay ? 0 : 1 }">
  <div class="header">
    <div class="header_nav">
      <div class="header_nav--profil">
        <div class="img_profil">
          <img [src]="attachment?.file || user?.profilePicture || 'assets/icons/Profil2.png'" alt="profil image"
            class="header-img">
        </div>
        <div class="profil_info">
          <div class="profil_info_bigText">{{'home-page.title' | translate }}</div>
          <div>{{user.firstName | titlecase }} {{user.lastName | titlecase }}</div>
        </div>
      </div>
      <div class="header_nav--icon">
        <div class="icons_profil">
          <div class="notification">
            <img src="assets/icons/bell.svg" alt="" routerLink="/navigation/notifications" class="header-img-end">
            <div class="badge-alert" *ngIf="unreadNotifications">{{unreadNotifications}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ion-header>

<ion-content [fullscreen]="true" class="home-container">

  <div id="container" [ngStyle]="{opacity: scannerSrv?.currDisplay ? 0 : 1}">
    <div class="tab-buttons">
      <button (click)="setActiveTab('home')" [class.active]="activeTab === 'home'">{{"home-page-particular.welcome" |
        translate}} </button>
      <button (click)="setActiveTab('formats-amigo')"
        [class.active]="activeTab === 'formats-amigo'">{{"home-page-particular.formats" | translate}} </button>
    </div>

    <div *ngIf="activeTab === 'home'">
      <div class="banner">
        <img [src]="banner1 || '/assets/images/firstslide.png'" alt="/assets/images/firstslide.png">
      </div>
      <div class="section section-1 video-container">
        <!-- Vidéo en miniature -->
        <img class="video-preview" src="assets/images/amigopreview1.jpg" (click)="openVideoModal()" />
      </div>

      <!-- Modal personnalisé -->
      <div *ngIf="isVideoOpen" class="custom-modal">
        <div class="modal-overlay" (click)="closeVideoModal()"></div>
        <div class="modal-content">
          <video class="modal-video" controls autoplay>
            <source src="https://firebasestorage.googleapis.com/v0/b/la-pasta-308a8.appspot.com/o/pate_farine_amigo_1.mp4?alt=media&token=4b5b5a12-8024-4e71-bb10-d62ba0db0a19" type="video/mp4">
            {{"home-page-particular.video-unvailable" | translate}}
          </video>
          <ion-icon name="close-circle-outline" (click)="closeVideoModal()"></ion-icon>

        </div>
      </div>
      <div class="section">
        <img class="rounded" [src]="banner2 || 'assets/images/bottomimagehom.png'"
          alt="assets/images/bottomimagehom.png" />
      </div>
    </div>

    <div *ngIf="activeTab === 'formats-amigo'">
      <div class="banner">
        <ion-card class="card slide-container tendency">
          <ion-card-content class="card-content">
            <ion-slides #mySlider class="slide-content" [options]="slideOpts">
              <ion-slide *ngFor="let slide of tendancySlides" [ngStyle]="{'background-image': 'url(' + slide.img +')'}"
                (click)="handleBannerClick(slide)">
              </ion-slide>
            </ion-slides>
          </ion-card-content>
        </ion-card>
      </div>
      <div class="grid">
        <div class="item">
          <img class="amigo25" src="assets/images/amigo25.png" />
          <div class="description">
            <p>Amigo {{"home-page-particular.bag" | translate}}</p>
            <span>25 KG</span>
          </div>
        </div>
        <div class="item">
          <img class="amigo50" src="assets/images/amigo50.png" />
          <div class="description">
            <p>Amigo {{"home-page-particular.bag" | translate}}</p>
            <span>50 KG</span>
          </div>
        </div>
        <div class="item">
          <img class="amigo5" src="assets/images/amigo5.png" />
          <div class="description">
            <p>Amigo {{"home-page-particular.bag" | translate}}</p>
            <span>5 KG</span>
          </div>
        </div>
        <div class="item-oil">
          <img src="assets/images/oil.png" />
        </div>
      </div>
      <div class="endText">
        <p>{{"home-page-particular.product-description" | translate}}</p>
      </div>
    </div>
  </div>

  <!-- ion-fab vertical="bottom" horizontal="end" slot="fixed"> -->
  <!-- <ion-fab>
    <ion-fab-button class="cimencamBtn">
      <ion-img src="/assets/logos/cadyst.png" class="floatingBloc"> </ion-img>
    </ion-fab-button>

    <ion-fab-list side="top">
      <ion-fab-button class="LiveChatBtn animate__animated animate__bounceInUp" routerLink="/navigation/contact">
        <ion-img src="/assets/icons/message-square-green.svg"> </ion-img>
      </ion-fab-button>
      <ion-fab-button class="LiveChatBtn animate__animated animate__bounceInUp" (click)="startScan()">
        <ion-img src="/assets/logos/qr-code.png"> </ion-img>
      </ion-fab-button>
    </ion-fab-list>
  </ion-fab> -->

  <!-- fab placed to the bottom end -->


</ion-content>

<app-qr-code-scanner *ngIf="scannerSrv?.currDisplay"></app-qr-code-scanner>