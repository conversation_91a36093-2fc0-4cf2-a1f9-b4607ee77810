import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { UserCategory } from 'src/app/shared/enum/user-category.enum';
import { MarketPlaceCart, OrderMarket } from 'src/app/shared/models/order-market-place';
import { MarketPlaceService } from 'src/app/menu-order/services/market-place.service';
import { OrderStatus } from 'src/app/shared/models/order';
import { CommonService } from 'src/app/shared/services/common.service';
import { InfiniteScrollCustomEvent, ModalController } from '@ionic/angular';
import { ToastModel } from 'src/app/shared/models/toast.model';
import { FilterOrderHistoryComponent } from 'src/app/menu-order/history/filter-order-history/filter-order-history.component';
import { Language } from 'src/app/shared/enum/language.enum';
import { TranslateConfigService } from 'src/app/shared/services/translate-config.service';

@Component({
  selector: 'app-historic-market-place',
  templateUrl: './historic-market-place.page.html',
  styleUrls: ['./historic-market-place.page.scss'],
})
export class HistoricMarketPlacePage {

  isLoading = false;
  skeletons = [1, 2, 3, 4, 5, 6];
  offset = 0;
  limit = 20;
  category = {
    status: UserCategory.Particular,
  };
  orderStatus = {
    status: OrderStatus.CREATED,
  }
  userCategory = UserCategory;
  status = OrderStatus;
  orders: OrderMarket[] = [];
  filterData: any;


  constructor(
    private location: Location,
    private marketService: MarketPlaceService,
    public commonService: CommonService,
    private modalCtrl: ModalController,
    private translateService: TranslateConfigService,

  ) { }

  back() {
    this.location.back();
  }

  async ionViewWillEnter() {
    this.isLoading = true;
    this.orders = [];
    await this.initRefresh();
  }

  ionViewDidEnter() {
    this.isLoading = false;
  }
  async doRefresh(event: any) {
    this.offset = 0;
    this.orders = [];
    await this.getOrderItemByUser();
    event.target.complete();
  }

  async initRefresh(): Promise<void> {
    this.offset = 0;
    this.orders = [];
    await this.getOrderItemByUser();
  }

  async getFlowOrder(event: any): Promise<void> {
    this.offset += this.limit;
    await this.getOrderItemByUser();
    (event as InfiniteScrollCustomEvent).target.complete();
  }

  async showFilter(): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: FilterOrderHistoryComponent,
      initialBreakpoint: 0.6,
      cssClass: 'modal',
      breakpoints: [0, 0.6, 0.7],
      mode: 'ios',
      componentProps: {
        filterData: this.filterData,
      },
    });
    modal.present();
    const { data } = await modal.onWillDismiss();
    console.log(data);

    if (data && Object.keys(data).length > 0) {
      this.filterData = data;
      this.offset = 0;
      this.orders = [];
      await this.getOrderItemByUser();

      if (this.orders.length === 0) {
        await this.commonService.showToast({
          message: this.translateService.currentLang === Language.French
            ? 'Aucune commande trouvée pour cette période'
            : 'No orders found for this period',
          color: 'warning',
        });
      }
    }
  }

  async getOrderItemByUser() {
    try {
      this.skeletons = [1, 2, 3, 4, 5, 6];
      this.isLoading = true;

      const query: any = {
        ...this.filterData,
        limit: this.limit,
        offset: this.offset,
        status: this.orderStatus.status,
      };

      if (!this.commonService?.user?._id) {
        this.handleUserOrderError('User ID is missing.');
        return;
      }

      const response = await this.marketService.getAllOrderItemsByUser(query);

      if (!response?.data?.length) {
        return;
      }

      this.orders = this.offset === 0 ?
        response.data :
        [...this.orders, ...response.data];

    } catch (error) {
      this.handleUserOrderError('Failed to fetch orders.');
    } finally {
      this.isLoading = false;
      this.skeletons = [];
    }
  }

  private handleUserOrderError(message: string) {
    this.isLoading = false;
    this.skeletons = [];
    const toastMessage: ToastModel = {
      message: message,
      color: 'danger',
    };
    this.commonService.showToast(toastMessage);
  }

}
