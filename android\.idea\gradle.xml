<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="testRunner" value="CHOOSE_PER_TEST" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="jbr-21" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$PROJECT_DIR$/capacitor-cordova-android-plugins" />
            <option value="$PROJECT_DIR$/../node_modules/@capacitor-mlkit/barcode-scanning/android" />
            <option value="$PROJECT_DIR$/../node_modules/@capacitor/android/capacitor" />
            <option value="$PROJECT_DIR$/../node_modules/@capacitor/app/android" />
            <option value="$PROJECT_DIR$/../node_modules/@capacitor/camera/android" />
            <option value="$PROJECT_DIR$/../node_modules/@capacitor/geolocation/android" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>