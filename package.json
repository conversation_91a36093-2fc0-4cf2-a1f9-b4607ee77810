{"name": "<PERSON><PERSON>", "version": "1.1.2", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration staging", "build:staging": "ng build --configuration staging && ts-node brotli_compress.ts", "build:staging:apk": "ionic capacitor build android --configuration staging", "build:production:apk": "ionic capacitor build android --configuration production", "build:prod": "ng build --configuration production && ts-node brotli_compress.ts", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/common": "^16.2.12", "@angular/core": "^16.2.12", "@angular/forms": "^16.2.12", "@angular/google-maps": "^16.2.12", "@angular/platform-browser": "^16.2.12", "@angular/platform-browser-dynamic": "^16.2.12", "@angular/router": "^16.2.12", "@angular/service-worker": "^16.2.12", "@awesome-cordova-plugins/app-version": "^6.15.0", "@awesome-cordova-plugins/core": "^6.16.0", "@capacitor-mlkit/barcode-scanning": "^7.2.1", "@capacitor/android": "^7.0.0", "@capacitor/app": "^7.0.1", "@capacitor/camera": "^7.0.0", "@capacitor/core": "^7.0.0", "@capacitor/geolocation": "^7.0.0", "@capacitor/ios": "^7.0.0", "@ionic/angular": "^6.1.9", "@ionic/angular-toolkit": "^12.2.0", "@ionic/pwa-elements": "^3.1.1", "@ionic/storage": "^4.0.0", "@ionic/utils-process": "^2.1.12", "@ionic/utils-stream": "^3.1.7", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "ajv": "^8.17.1", "animate.css": "^4.1.1", "chart.js": "^4.1.2", "deps": "^1.0.0", "lodash-es": "^4.17.21", "lz-string": "^1.5.0", "moment": "^2.29.4", "ngx-image-compress": "^15.1.6", "peer": "^1.0.2", "primeng": "^16.9.1", "rxjs": "~7.8.1", "sharp": "^0.34.1", "swiper": "^8.3.2", "tslib": "^2.2.0", "zone.js": "~0.13.3"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.14", "@angular/cli": "^16.2.16", "@angular/compiler": "^16.2.12", "@angular/compiler-cli": "^16.2.12", "@angular/localize": "^16.2.12", "@capacitor/assets": "^3.0.5", "@capacitor/cli": "^7.0.0", "@tsconfig/recommended": "^1.0.1", "@types/google.maps": "^3.58.1", "@types/jasmine": "~3.6.0", "@types/lodash": "^4.14.199", "@types/lz-string": "^1.3.34", "@types/node": "^16.18.105", "@typescript-eslint/eslint-plugin": "5.3.0", "@typescript-eslint/parser": "5.3.0", "brotli": "^1.3.3", "eslint": "^7.6.0", "jasmine-core": "~3.8.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.2", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "node-gzip": "^1.1.2", "ts-node": "~8.3.0", "typescript": "~4.9.5"}, "description": "An Ionic project"}