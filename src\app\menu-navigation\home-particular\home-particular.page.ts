import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Platform, ModalController, PopoverController } from '@ionic/angular';
import moment from 'moment';
import { Subscription, interval } from 'rxjs';
import { ProductService } from 'src/app/menu-order/services/product.service';
import { BottomSheetValidationActionComponent } from 'src/app/shared/components/bottom-sheet-validation-action/bottom-sheet-validation-action.component';
import { ImageBanner, LevelBanner } from 'src/app/shared/models/image-banner.entity';
import { NotificationMessage, messageType } from 'src/app/shared/models/notification-type.model';
import { Product } from 'src/app/shared/models/product.model';
import { BaseUser } from 'src/app/shared/models/user.models';
import { CommonService } from 'src/app/shared/services/common.service';
import { NotificationsService } from 'src/app/shared/services/notifications.service';
import { StorageService } from 'src/app/shared/services/storage.service';
import { UserService } from 'src/app/shared/services/user.service';
import { VersionService } from 'src/app/shared/services/version.service';
import { RedirectToSabitouComponent } from '../home/<USER>/redirect-to-sabitou.component';
import { UpdateAppModalComponent } from '../home/<USER>/update-app-modal.component';
import { ReferralConfirmationModalComponent } from 'src/app/shared/components/referral-confirmation-modal/referral-confirmation-modal.component';
import { Invitation, InvitationStatus } from 'src/app/shared/models/invitation.model';
import { FidelityProgramService } from 'src/app/shared/services/fidelity-program.service';
import { ScannerService } from 'src/app/shared/services/scanner.service';

@Component({
  selector: 'app-home-particular',
  templateUrl: './home-particular.page.html',
  styleUrls: ['./home-particular.page.scss'],
})
export class HomeParticularPage implements OnInit {
  skeletons = [1, 2, 3, 4,];

  backgroundColors = ['#CDF5D0', '#FFBF74', '#CEE6FE']; // Define your background colors here
  activeTab: string = 'home';
  slideOfferOpts = {
    initialSlide: 0,
    speed: 400,
    spaceBetween: 16,
    autoplay: true,
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
    pagination: {
      el: '.swiper-pagination',
      clickable: true,
    }
  };

  slideOpts = {
    initialSlide: 0,
    speed: 400,
    spaceBetween: 10,
    centeredSlides: true,
    loop: true,
    autoplay: {
      delay: 4000,
      disableOnInteraction: false
    },
    pagination: {
      el: '.swiper-pagination',
      clickable: true
    }
  };


  attachment = {
    file: '',
    name: '',
    contentType: '',
  };
  slides = [];
  selectedItem: any;
  isVideoOpen = false;
  listTab = [
    {
      id: 0,
      title: 'Suggestions'
    },
    // {
    //   id: 1,
    //   title: "Meilleures ventes"
    // },
    // {
    //   id: 2,
    //   title: 'Tendances'
    // }
  ];

  tendancySlides = [];
  isScanning: boolean;
  products: Product[] = [];
  isAppMaintained: Boolean;
  unreadNotifications: number;
  backButtonSub: Subscription;
  lastDeniedUpdateApp: any;
  showUpdateAppRequest: boolean;
  dataNotification: NotificationMessage[];
  user: BaseUser;
  subscription: Subscription;
  listCard!: any[];
  images: ImageBanner[];
  filterForm = {
    level: '', enable: true, created_at: "",
    date: { start: moment().startOf('year'), end: moment().endOf('year') },
  }
  offset = 0;
  limit = 50;
  total = 0;
  invitation: Invitation

  banner1: string;
  banner2: string;

  constructor(
    private router: Router,
    private platform: Platform,
    private userSrv: UserService,
    private modalCtrl: ModalController,
    private productSrv: ProductService,
    private popOver: PopoverController,
    public scannerSrv: ScannerService,
    public commonService: CommonService,
    public versionService: VersionService,
    private storageService: StorageService,
    private notificationService: NotificationsService,
    private fidelityProgramService: FidelityProgramService,

  ) {
    this.verifyAppUpToDate().then((isUpToDate) => {
      if (!isUpToDate) {
        const source = interval(50000);
        this.subscription = source.subscribe(async () => {
          await this.verifyAppUpToDate();
        });
      }
    });
  }

  async ngOnInit(): Promise<void> {
    this.user = this.storageService.getUserConnected();
    this.commonService.tab = 'navigation/home-alt';
    this.commonService.showNav = true;
    // await this.getNotificationsMessages();
    await this.getFirstProduct();
    await this.verifyAppUpToDate();
    this.selectedItem = this.listTab[0].title;
    this.listCard = this.products;
  }

  async ionViewWillEnter(): Promise<void> {
    this.commonService.tab = 'navigation/home-alt';
    this.commonService.showNav = true;
    await this.verifyAppUpToDate();
    await this.getNotificationsMessages();

    await this.getImage();

    const invitation = await this.fidelityProgramService.getPendingInvitationForUser(+this.user?.tel);

    if (invitation && invitation?.status === InvitationStatus.PENDING) {
      await this.showModalReferral(invitation);
    }
  }


  async showModalReferral(invitation: Invitation) {
    const modal = await this.modalCtrl.create({
      component: ReferralConfirmationModalComponent,
      cssClass: 'modalClass',
      componentProps: {
        invitation: invitation
      },
    });

    await modal.present()
  }

  async ionWillLeave() {
    await this.scannerSrv.stopScan();
  }

  async startScan() {
    await this.platform.ready();
    this.scannerSrv.currDisplay = true;

    const result = await this.scannerSrv.startScan();
    this.scannerSrv.currDisplay = false;

    if (result) {
      this.storageService.store('qrCodeData', result)
      this.router.navigate(['/navigation/qr-code']);
    }
  }

  setActiveTab(tabName: string) {
    this.activeTab = tabName;
  }

  getBackgroundColor(index: number): string {
    return this.backgroundColors[index % this.backgroundColors.length];
  }

  async getImage(): Promise<any> {
    const query = {
      offset: this.offset,
      limit: this.limit,
      ...this.filterForm,
    };
    this.images = (await this.commonService.getAllImageBanner(query))?.data;
    this.tendancySlides = this.images?.filter((image: ImageBanner) => { return image?.level === LevelBanner.HOME_2 }).map(result => { return { img: result?.image } });
    if (!this.tendancySlides?.length) this.tendancySlides = [
      {
        img: '/assets/images/secondslide.png',
      },
      {
        img: '/assets/images/thirdslide.png',
      }]

    const imageBanner1 = this.images?.filter((image: ImageBanner) => { return image?.level === LevelBanner.BANNER_HOME_2_LEVEL_1 });
    const imageBanner2 = this.images?.filter((image: ImageBanner) => { return image?.level === LevelBanner.BANNER_HOME_2_LEVEL_2 });
    this.banner1 = imageBanner1?.length
      ? imageBanner1[0]?.image : '/assets/images/firstslide.png'
    this.banner2 = imageBanner2?.length
      ? imageBanner2[0]?.image : '/assets/images/bottomimagehom.png';

  }

  openVideoModal() {
    this.isVideoOpen = true;
  }
  closeVideoModal() {
    this.isVideoOpen = false;
  }
  async ionViewDidEnter() {
    // await this.verifyAppUpToDate();
  }

  async getFirstProduct(): Promise<Product[]> {

    const query = { limit: 20 }
    return this.products = (await this.productSrv.getProducts(query))?.data;
  }

  async ionViewDidLeave(): Promise<void> {
    this.platform.backButton.subscribeWithPriority(10000, () => this.onBack());

  }

  onBack(): void {
    this.exitAppOnBackButtonPress();
  }

  exitAppOnBackButtonPress(): void {
    this.platform.backButton.subscribe(async () => {
      if (this.router.isActive('/home', true) || this.router.url === 'navigation/home-alt') {
        this.showBottomSheet();
      }
    });
  }

  setBackgroundColor(itemTab: any) {
    const el = document.querySelector(`#${itemTab.title}`) as HTMLElement;
    const otherButton = document.querySelectorAll('.buttons-tabset ion-button') as any;
    otherButton.map((elt: any) => {
      elt.style.color = '#143c5d';
      elt.style.background = '#E7EAEF';
      elt.style.borderRadius = '50px';

      return elt;
    })
    el.style.color = '#fff';
    el.style.background = '#143c5d';
    el.style.borderRadius = '50px';
  }

  handleClick(item: any) {
    this.selectedItem = item?.title;
  }

  async verifyAppUpToDate(): Promise<boolean> {
    if (this.isAppMaintained) return;

    const isAppUpToDate = await this.versionService.isUpToDate();
    if (!isAppUpToDate) {
      this.isAppMaintained = true;
      const popover = await this.popOver.create({
        component: UpdateAppModalComponent,
        cssClass: 'updateModal',
      });
      popover.present();
      const { data, role } = await popover.onWillDismiss();
      this.isAppMaintained = false;
    }
    return isAppUpToDate;
  }

  async showBottomSheet() {
    const modal = await this.modalCtrl.create({
      component: BottomSheetValidationActionComponent,
      initialBreakpoint: 0.5,
      breakpoints: [0, 0.5],
      cssClass: ['modal', 'bottom-sheet-container'],
      mode: 'ios',
      componentProps: {
        label: `Quitter l'application ?`,
        description: `Vous êtes sur le point de quitter l'application`,
      },
    });
    modal.present();

    const { data, role } = await modal.onWillDismiss();

    if (role === 'validate') {
      // navigator["app"].exitApp();
    }
  }

  async redirectToSabitou() {
    const modal = await this.modalCtrl.create({
      component: RedirectToSabitouComponent,
      mode: 'ios',
    });
    modal.present();
  }
  async getNotificationsMessages() {

    this.user = this.storageService.getUserConnected();
    const userNotif = await this.userSrv?.find(this.user?._id);
    const query = {
      email: this.user?.email,
      userId: userNotif?._id
    };
    query['notifications'] = JSON.stringify({ $in: userNotif?.notifications?.map(notification => notification?.id) });
    this.dataNotification = (await this.notificationService.getMessages(query))?.data;


    this.dataNotification?.forEach(notification => {
      const userNotification = userNotif?.notifications?.find(notif => notif?.id === notification?._id?.toString());
      if (userNotification) notification.status = userNotification?.status;
    });

    this.unreadNotifications = (this.dataNotification?.filter(dataNotif => dataNotif?.status === messageType.CREATE)?.length) || 0;

  }

  async showDetailNotification() {
    this.router.navigate(['/navigation/notifications']);
  }

  async navigateToParticularOrder() {
    this.router.navigate(['/order/particular-order']);
  }

  trackByFn(index: any, item: any): any {
    return index;
  }

  showDetail(itemId: any): void {
    this.router.navigate(['/order/product-detail/' + itemId]);
  }

  handleBannerClick(slide: any) {
    if (slide.redirectUrl) {
      if (slide.redirectUrl.startsWith('http')) {
        window.open(slide.redirectUrl, '_blank');
      } else {
        this.router.navigate([slide.redirectUrl]);
      }
    }
  }

}
