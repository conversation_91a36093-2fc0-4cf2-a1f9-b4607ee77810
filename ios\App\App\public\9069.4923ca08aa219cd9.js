"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9069],{40129:(T,y,o)=>{o.d(y,{vi:()=>f});const f=(0,o(22126).F3)("BarcodeScanner",{web:()=>o.e(5499).then(o.bind(o,85499)).then(n=>new n.BarcodeScannerWeb)})},99069:(T,y,o)=>{o.r(y),o.d(y,{ReportingOrderParticularAssociatedPageModule:()=>w});var _=o(56610),l=o(37222),i=o(77897),C=o(77575),u=o(73308),f=o(99987),n=o(2978),E=o(82571),A=o(62049),h=o(74657);function N(M,x){if(1&M){const c=n.RV6();n.j41(0,"ion-datetime",19,20),n.bIt("ionChange",function(){n.eBV(c);const O=n.sdS(1);return n.Njj(O.confirm(!0))}),n.k0s()}}function s(M,x){if(1&M){const c=n.RV6();n.j41(0,"ion-datetime",21,20),n.bIt("ionChange",function(){n.eBV(c);const O=n.sdS(1);return n.Njj(O.confirm(!0))}),n.k0s()}}let e=(()=>{class M{constructor(c,p,O){this.commonSrv=c,this.modalCtrl=p,this.translateService=O,this.filterForm=new l.gE({startDate:new l.MJ(""),endDate:new l.MJ(""),customerName:new l.MJ("")})}ngOnInit(){this.filterForm.reset(),this.filterForm.patchValue({startDate:this.filterData?.startDate,endDate:this.filterData?.endDate,customerName:this.filterData?.customerName}),this.filterForm?.updateValueAndValidity()}closeModal(){var c=this;return(0,u.A)(function*(){const p=c.filterForm.value;if(p.startDate>p.endDate)return yield c.commonSrv.showToast({message:c.translateService.currentLang===f.T.French?"Veuillez renseigner une date de d\xe9but inf\xe9rieure \xe0 celle de la date de fin":"Please enter a start date less than the end date",color:"warning"});c.modalCtrl.dismiss({...c.filterForm.value})})()}static{this.\u0275fac=function(p){return new(p||M)(n.rXU(E.h),n.rXU(i.W3),n.rXU(A.E))}}static{this.\u0275cmp=n.VBU({type:M,selectors:[["app-filter-particular-associated"]],inputs:{filterData:"filterData"},decls:43,vars:25,consts:[[1,"bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end"],["src","assets/icons/close.svg",3,"click"],["id","content",3,"formGroup"],[1,"form-group","padding-horizontal"],[1,"title"],[1,"mbottom"],[1,"date-time"],["slot","start","src","assets/icons/calendar.svg",1,"ion-text-center"],["id","date","placeholder","JJ/MM/AAAA",1,"ion-text-start",3,"value"],["trigger","date","size","cover","side","top","alignment","center"],["id","enddate","placeholder","JJ/MM/AAAA",1,"ion-text-start",3,"value"],["trigger","enddate","size","cover","side","top","alignment","center"],["position","floating",1,"title"],["formControlName","customerName","clearInput",""],[1,"add-qty-btn","mbottom","padding-horizontal",3,"click"],["color","primary",1,"btn","add-line",3,"disabled"],["name","search-sharp"],["formControlName","startDate","presentation","date","locale","fr-FR",3,"ionChange"],["popoverDatetime",""],["formControlName","endDate","presentation","date","locale","fr-FR",3,"ionChange"]],template:function(p,O){1&p&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2)(4,"ion-img",3),n.bIt("click",function(){return O.closeModal()}),n.k0s()(),n.j41(5,"ion-label"),n.EFF(6),n.nI1(7,"translate"),n.k0s()()(),n.j41(8,"ion-content")(9,"form",4)(10,"div",5)(11,"ion-label",6),n.EFF(12),n.nI1(13,"translate"),n.k0s(),n.j41(14,"div",7)(15,"ion-item",8),n.nrm(16,"ion-icon",9)(17,"ion-input",10),n.nI1(18,"date"),n.j41(19,"ion-popover",11),n.DNE(20,N,2,0,"ng-template"),n.k0s()()()(),n.j41(21,"div",5)(22,"ion-label",6),n.EFF(23),n.nI1(24,"translate"),n.k0s(),n.j41(25,"div",7)(26,"ion-item",8),n.nrm(27,"ion-icon",9)(28,"ion-input",12),n.nI1(29,"date"),n.j41(30,"ion-popover",13),n.DNE(31,s,2,0,"ng-template"),n.k0s()()()(),n.j41(32,"div",5)(33,"ion-item",7)(34,"ion-label",14),n.EFF(35),n.nI1(36,"translate"),n.k0s(),n.nrm(37,"ion-input",15),n.k0s()(),n.j41(38,"div",16),n.bIt("click",function(){return O.closeModal()}),n.j41(39,"ion-button",17),n.nrm(40,"ion-icon",18),n.EFF(41),n.nI1(42,"translate"),n.k0s()()()()()),2&p&&(n.R7$(6),n.JRh(n.bMT(7,9,"history-page.title-filter")),n.R7$(3),n.Y8G("formGroup",O.filterForm),n.R7$(3),n.SpI("",n.bMT(13,11,"history-page.startDate")," "),n.R7$(5),n.FS9("value",n.i5U(18,13,O.filterForm.get("startDate").value,"dd/MM/yyyy")),n.R7$(6),n.SpI("",n.bMT(24,16,"history-page.endDate")," "),n.R7$(5),n.FS9("value",n.i5U(29,18,O.filterForm.get("endDate").value,"dd/MM/yyyy")),n.R7$(7),n.JRh(n.bMT(36,21,"reporting.clientName")),n.R7$(4),n.Y8G("disabled",O.filterForm.invalid),n.R7$(2),n.SpI(" ",n.bMT(42,23,"history-page.btn-filter")," "))},dependencies:[l.qT,l.BC,l.cb,i.Jm,i.W9,i.A9,i.eU,i.iq,i.KW,i.$w,i.uz,i.he,i.Zx,i.ai,i.CF,i.Je,i.Gw,l.j4,l.JD,_.vh,h.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;color:#1e1e1e}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;color:#000;display:flex;flex-direction:column;height:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(40 * var(--res));margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{font-family:Mont Regular;--padding-start: 0;font-size:calc(36 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .date-time[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{--padding-start: var(--space-4) !important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{border-bottom:2px solid #dedede}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   .unit[_ngcontent-%COMP%]{margin-right:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   .tonne[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{border-bottom:2px solid #dedede}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{margin-bottom:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   .full-input[_ngcontent-%COMP%]{margin:.5em 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   .btn-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-schedule[_ngcontent-%COMP%]{margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   .add-line[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   .add-line[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .mbottom[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res));width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]   .ion-label[_ngcontent-%COMP%]{font-family:Mont Regular!important;font-weight:400!important;font-size:calc(42 * var(--res))!important;color:#1e1e1e!important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fMedium[_ngcontent-%COMP%]{font-family:Mont Light}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .no-mbottom[_ngcontent-%COMP%]{margin-bottom:0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{padding:calc(25 * var(--res)) 0 calc(75 * var(--res)) 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(30 * var(--res));line-height:initial}ion-content[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));width:100%}"]})}}return M})();var t=o(68896),a=o(32327),r=o(57870),d=o(71604),g=o(11244);function m(M,x){if(1&M&&(n.qex(0),n.j41(1,"ion-row")(2,"ion-col",16),n.EFF(3),n.k0s(),n.j41(4,"ion-col",16),n.EFF(5),n.k0s()(),n.bVm()),2&M){const c=x.$implicit;n.R7$(3),n.JRh((null==c?null:c.firstName)||"N/A"),n.R7$(2),n.JRh((null==c?null:c.totalQuantity)||"N/A")}}const v=[{path:"",component:(()=>{class M{constructor(c,p,O,R,Y){this.scannerService=c,this.commonSrv=p,this.modalCtrl=O,this.reportingSrv=R,this.retailService=Y,this.evolutionOrderParticularClients=[],this.totalProducts=0,this.filterData={startDate:null,endDate:null,customerName:""}}ngOnInit(){var c=this;return(0,u.A)(function*(){yield c.getVolumeOrderByParticularClient(),c.geTotalProduct()})()}loadData(){var c=this;return(0,u.A)(function*(){c.getVolumeOrderByParticularClient()})()}doRefresh(c){var p=this;return(0,u.A)(function*(){p.filterData={customerName:"",startDate:null,endDate:null},yield p.loadData(),c.target.complete()})()}showFilter(){var c=this;return(0,u.A)(function*(){const p=yield c.modalCtrl.create({component:e,initialBreakpoint:.6,cssClass:"modal",breakpoints:[0,.5],mode:"ios",componentProps:{filterData:{...c.filterData},isShow:!0}});p.present();const O=(yield p.onWillDismiss()).data;c.filterData={...c.filterData,...O},c.filterData&&(yield c.getVolumeOrderByParticularClient())})()}getVolumeOrderByParticularClient(){var c=this;return(0,u.A)(function*(){const p={associatedCommercialId:c.commonSrv?.user?._id,...c.filterData},O=yield c.scannerService.getVolumeOrderByParticularClient(p),R=yield c.retailService.getVolumeOrderByParticularClient(p);c.evolutionOrderParticularClients.push(...O,...R)})()}geTotalProduct(){this.totalProducts=this.evolutionOrderParticularClients?.reduce((c,p)=>c+(p.totalQuantity||0),0)}static{this.\u0275fac=function(p){return new(p||M)(n.rXU(t.I),n.rXU(E.h),n.rXU(i.W3),n.rXU(a.k),n.rXU(r.l))}}static{this.\u0275cmp=n.VBU({type:M,selectors:[["app-reporting-order-particular-associated"]],decls:38,vars:31,consts:[[1,"header"],["mode","md","slot","start","text",""],["defaultHref","./"],[1,"title-and-filter-container"],[1,"title"],["slot","fixed"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText","ionRefresh"],[1,"ion-content"],[1,"p-card"],[1,"p-card-header"],[1,"filter"],["src","/assets/icons/refresh-green.png",1,"img-refresh",3,"click"],["expand","block","fill","outline","color","primary","size","small",3,"click"],["slot","start","src","/assets/icons/filtre-icon.png",1,"img-filter"],["size","5",1,"row-title"],[4,"ngFor","ngForOf"],["size","5"]],template:function(p,O){1&p&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-buttons",1),n.nrm(3,"ion-back-button",2),n.k0s(),n.j41(4,"div",3)(5,"h1",4),n.EFF(6),n.nI1(7,"capitalize"),n.nI1(8,"translate"),n.k0s()()()(),n.j41(9,"ion-content")(10,"ion-refresher",5)(11,"ion-refresher-content",6),n.bIt("ionRefresh",function(Y){return O.doRefresh(Y)}),n.nI1(12,"translate"),n.nI1(13,"translate"),n.k0s()(),n.j41(14,"div",7)(15,"p-card",8)(16,"div",9)(17,"div"),n.EFF(18),n.k0s(),n.j41(19,"div",10)(20,"ion-img",11),n.bIt("click",function(Y){return O.doRefresh(Y)}),n.k0s(),n.j41(21,"ion-button",12),n.bIt("click",function(){return O.showFilter()}),n.nrm(22,"ion-img",13),n.EFF(23),n.nI1(24,"capitalize"),n.nI1(25,"translate"),n.k0s()()()(),n.j41(26,"ion-grid")(27,"ion-row")(28,"ion-col",14),n.EFF(29),n.nI1(30,"titlecase"),n.nI1(31,"translate"),n.k0s(),n.j41(32,"ion-col",14),n.EFF(33),n.nI1(34,"titlecase"),n.nI1(35,"translate"),n.nI1(36,"translate"),n.k0s()(),n.DNE(37,m,6,2,"ng-container",15),n.k0s()()()),2&p&&(n.R7$(6),n.JRh(n.bMT(7,9,n.bMT(8,11,"reporting.top-products"))),n.R7$(5),n.FS9("pullingText",n.bMT(12,13,"refresher.pull")),n.Mz_("refreshingText","",n.bMT(13,15,"refresher.refreshing"),"..."),n.R7$(7),n.SpI("Total : ",O.totalProducts," "),n.R7$(5),n.SpI(" ",n.bMT(24,17,n.bMT(25,19,"reporting.btn-filter"))," "),n.R7$(6),n.JRh(n.bMT(30,21,n.bMT(31,23,"reporting.clientName"))),n.R7$(4),n.Lme("",n.bMT(34,25,n.bMT(35,27,"reporting.quantity")),"(",n.bMT(36,29,"reporting.bags"),")50KG"),n.R7$(4),n.Y8G("ngForOf",O.evolutionOrderParticularClients))},dependencies:[_.Sq,i.el,i.Jm,i.QW,i.hU,i.W9,i.lO,i.eU,i.KW,i.To,i.Ki,i.ln,i.ai,i.tY,d.Z,_.PV,g.F,h.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--border-color: transparent;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-back-button[_ngcontent-%COMP%]{width:4rem}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title-and-filter-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:100%}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title-and-filter-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--mont-semibold);font-size:calc(55 * var(--res));text-align:start;flex-grow:1}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title-and-filter-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .color-primary[_ngcontent-%COMP%]{color:#143c5d}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]{display:flex;align-items:flex-start;flex-direction:column;height:4em;padding:1em var(--container-padding);background-position:center;background-size:cover;border-radius:16px;width:100%;background-image:url(/assets/images/Total-card.png)}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]{display:flex;flex-direction:column;font-size:1em;font-family:Mont Regular}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .balance[_ngcontent-%COMP%]{font-size:var(--fs-32-px);color:var(--clr-tertiary-600);font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-size:var(--fs-15-px);color:var(--clr-tertiary-600)}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{font-size:var(--fs-10-px)}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .icon-home[_ngcontent-%COMP%]{height:48px;width:48px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]{padding:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]{width:100%;margin:20px .5em;background-color:#d9d9d9}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:0 0 calc(25 * var(--res)) 0;font-family:var(--mont-semibold);font-size:calc(42 * var(--res));color:#143c5d}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]{display:flex;align-items:center;gap:calc(50 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   .img-refresh[_ngcontent-%COMP%]{width:calc(50 * var(--res));cursor:pointer}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{text-transform:capitalize;--padding-top: calc(35 * var(--res));--padding-bottom: calc(35 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   .img-filter[_ngcontent-%COMP%]{margin-right:calc(15 * var(--res));width:calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-chart[_ngcontent-%COMP%]{height:12rem}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]{background:#CEE5FE;padding:1em;border-radius:10px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{padding-bottom:10px;margin-bottom:10px;border-bottom:1px dashed var(--ion-color-dark);display:flex;flex-direction:row;justify-content:space-between;font-size:calc(30 * var(--res));text-align:center}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .row-title[_ngcontent-%COMP%]{font-weight:500;background:hwb(211 53% 4%);border-radius:10px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .semi[_ngcontent-%COMP%]{font-family:Mont SemiBold!important}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .row-data[_ngcontent-%COMP%]{color:#143c5d;font-size:12px;font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .row-header[_ngcontent-%COMP%]{border-style:none!important}ion-content[_ngcontent-%COMP%]   .product-contain[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between}"]})}}return M})()}];let b=(()=>{class M{static{this.\u0275fac=function(p){return new(p||M)}}static{this.\u0275mod=n.$C({type:M})}static{this.\u0275inj=n.G2t({imports:[C.iI.forChild(v),C.iI]})}}return M})();var D=o(93887),I=o(60787);let w=(()=>{class M{static{this.\u0275fac=function(p){return new(p||M)}}static{this.\u0275mod=n.$C({type:M})}static{this.\u0275inj=n.G2t({imports:[_.MD,l.YN,i.bv,I.F,d.D,b,D.G,h.h,l.X1]})}}return M})()},32327:(T,y,o)=>{o.d(y,{k:()=>A});var _=o(73308),l=o(56610),i=o(26409),C=o(94934),u=o(45312),f=o(2978),n=o(33607),E=o(82571);let A=(()=>{class h{constructor(s,e,t){this.http=s,this.baseUrl=e,this.commonSrv=t,this.base_url=`${this.baseUrl.getOrigin()}${u.c.basePath}`}geTotalQuantityOrder(s){var e=this;return(0,_.A)(function*(){try{let t=new i.Nl;const{status:a,startDate:r,endDate:d,enable:g=!0,userId:m}=s;return a&&(t=t.append("status",a)),r&&d&&(t=t.append("startDate",new l.vh("fr").transform(r,"YYYY-MM-dd")),t=t.append("endDate",new l.vh("fr").transform(d,"YYYY-MM-dd"))),t=t.append("enable",g),m&&(t=t.append("user",m)),yield(0,C.s)(e.http.get(`${e.base_url}reporting/total-quantity-evolution`,{params:t}))}catch(t){return e.commonSrv.showToast({message:t.error.message,color:"danger"}),t}})()}geTotalProduct(s){var e=this;return(0,_.A)(function*(){try{let t=new i.Nl;const{status:a,startDate:r,endDate:d,product:g,enable:m=!0,userId:P}=s;return r&&d&&(t=t.append("startDate",new l.vh("fr").transform(r,"YYYY-MM-dd")),t=t.append("endDate",new l.vh("fr").transform(d,"YYYY-MM-dd"))),a&&(t=t.append("status",a)),g&&(t=t.append("items.product._id",g)),P&&(t=t.append("user",P)),t=t.append("enable",m),yield(0,C.s)(e.http.get(`${e.base_url}reporting/product-quantity`,{params:t}))}catch(t){return yield e.commonSrv.showToast({message:t.error.message,color:"danger"}),t}})()}getTotalEarnPoint(s){var e=this;return(0,_.A)(function*(){try{let t=new i.Nl;const{startDate:a,endDate:r,region:d,enable:g=!0}=s;return d&&(t=t.append("region",d)),a&&r&&(t=t.append("startDate",new l.vh("fr").transform(a,"YYYY-MM-dd")),t=t.append("endDate",new l.vh("fr").transform(r,"YYYY-MM-dd"))),t=t.append("enable",g),yield(0,C.s)(e.http.get(`${e.base_url}reporting/points-evolution`,{params:t}))}catch(t){return yield e.commonSrv.showToast({message:t.error.message,color:"danger"}),t}})()}getDistributorVolume(s){var e=this;return(0,_.A)(function*(){try{let t=new i.Nl;const{status:a,startDate:r,endDate:d,product:g,enable:m=!0,userId:P}=s;return a&&(t=t.append("status",a)),g&&(t=t.append("items.product._id",g)),P&&(t=t.append("user",P)),r&&d&&(t=t.append("startDate",new l.vh("fr").transform(r,"YYYY-MM-dd")),t=t.append("endDate",new l.vh("fr").transform(d,"YYYY-MM-dd"))),t=t.append("enable",m),yield(0,C.s)(e.http.get(`${e.base_url}reporting/distributor-volume`,{params:t}))}catch(t){return yield e.commonSrv.showToast({message:t.error.message,color:"danger"}),t}})()}getTotalPoint(s){var e=this;return(0,_.A)(function*(){try{let t=new i.Nl;const{status:a,product:g,enable:m=!0,region:P}=s;return a&&(t=t.append("status",a)),g&&(t=t.append("items.product._id",g)),P&&(t=t.append("payment.mode.id",P)),t=t.append("enable",m),yield(0,C.s)(e.http.get(`${e.base_url}reporting/retail-points`,{params:t}))}catch(t){return yield e.commonSrv.showToast({message:t.error.message,color:"danger"}),t}})()}static{this.\u0275fac=function(e){return new(e||h)(f.KVO(i.Qq),f.KVO(n.K),f.KVO(E.h))}}static{this.\u0275prov=f.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})()},11244:(T,y,o)=>{o.d(y,{F:()=>l});var _=o(2978);let l=(()=>{class i{transform(u){return console.log(),`${u?.slice(0,1)?.toLocaleUpperCase()+u?.slice(1)?.toLocaleLowerCase()}`}static{this.\u0275fac=function(f){return new(f||i)}}static{this.\u0275pipe=_.EJ8({name:"capitalize",type:i,pure:!0})}}return i})()},57870:(T,y,o)=>{o.d(y,{l:()=>A});var _=o(73308),l=o(26409),i=o(45312),C=o(56610),u=o(94934),f=o(2978),n=o(82571),E=o(33607);let A=(()=>{class h{constructor(s,e,t){this.http=s,this.commonSrv=e,this.baseUrlService=t,this.url=this.baseUrlService.getOrigin()+i.c.basePath}getOrdersRetaill(s){var e=this;return(0,_.A)(function*(){try{let t=new l.Nl;const{num:a,status:r,offset:d,limit:g,startDate:m,endDate:P,customerReference:v,particularId:b}=s;return m&&P&&(t=t.append("startDate",new C.vh("fr").transform(m,"YYYY-MM-dd")),t=t.append("endDate",new C.vh("fr").transform(P,"YYYY-MM-dd"))),v&&(t=t.append("customerReference",v)),void 0!==d&&(t=t.append("offset",d)),g&&(t=t.append("limit",g)),r&&(t=t.append("status",r)),a&&(t=t.append("appReference",a)),b&&(t=t.append("particular",b)),yield(0,u.s)(e.http.get(`${e.url}order-supplier/particulars`,{params:t}))}catch(t){return t}})()}getAllOrderRetaillForCommercial(s){var e=this;return(0,_.A)(function*(){try{let t=new l.Nl;const{num:a,status:r,offset:d,limit:g,startDate:m,endDate:P,customerReference:v,region:b,userId:D}=s;return m&&P&&(t=t.append("startDate",new C.vh("fr").transform(m,"YYYY-MM-dd")),t=t.append("endDate",new C.vh("fr").transform(P,"YYYY-MM-dd"))),v&&(t=t.append("customerReference",v)),void 0!==d&&(t=t.append("offset",d)),g&&(t=t.append("limit",g)),r&&(t=t.append("status",r)),a&&(t=t.append("appReference",a)),b&&(t=t.append("distributors.address.region",b)),D&&(t=t.append("supplier.associatedCommercial._id",D)),yield(0,u.s)(e.http.get(`${e.url}order-supplier`,{params:t}))}catch(t){return t}})()}getOrderRetaillParticular(s){var e=this;return(0,_.A)(function*(){try{let t=new l.Nl;const{status:a,offset:r,limit:d,startDate:g,endDate:m,particularId:P}=s;return g&&m&&(t=t.append("startDate",new C.vh("fr").transform(g,"YYYY-MM-dd")).append("endDate",new C.vh("fr").transform(m,"YYYY-MM-dd"))),void 0!==r&&(t=t.append("offset",r)),d&&(t=t.append("limit",d)),a&&(t=t.append("status",a)),P&&(t=t.append("particularId",P)),yield(0,u.s)(e.http.get(`${e.url}order-supplier/particulars`,{params:t}))}catch(t){return console.error("Erreur lors de la r\xe9cup\xe9ration des commandes:",t),t}})()}getScannerOrderParticular(s){var e=this;return(0,_.A)(function*(){try{let t=new l.Nl;const{status:a,offset:r,limit:d,startDate:g,endDate:m,particularId:P}=s;return g&&m&&(t=t.append("startDate",new C.vh("fr").transform(g,"YYYY-MM-dd")).append("endDate",new C.vh("fr").transform(m,"YYYY-MM-dd"))),void 0!==r&&(t=t.append("offset",r)),d&&(t=t.append("limit",d)),a&&(t=t.append("status",a)),yield(0,u.s)(e.http.get(`${e.url}scanner-data/particular/${P}`,{params:t}))}catch(t){return console.error("Erreur lors de la r\xe9cup\xe9ration des commandes:",t),t}})()}validateRetailOrder(s){var e=this;return(0,_.A)(function*(){try{return yield(0,u.s)(e.http.patch(`${e.url}order-supplier/${s._id}`,{orders:s},{headers:{Authorization:`Bearer ${e.commonSrv?.user?.accessToken}`}}))}catch(t){return e.commonSrv.getError(t?.message,t)}})()}rejectRetailOrder(s,e){var t=this;return(0,_.A)(function*(){try{return yield(0,u.s)(t.http.patch(`${t.url}order-supplier/${s._id}/rejectOder`,{rejectMessage:e,user:t.commonSrv.user},{headers:{Authorization:`Bearer ${t.commonSrv?.user?.accessToken}`}}))}catch(a){return t.commonSrv.getError(a?.message,a)}})()}imageOrderValidated(s){var e=this;return(0,_.A)(function*(){try{return yield(0,u.s)(e.http.post(`${e.url}images`,s))}catch(t){return t}})()}findOrderRetail(s){var e=this;return(0,_.A)(function*(){try{return yield(0,u.s)(e.http.get(e.url+"scanner-data/"+s))}catch{return null}})()}getImageRetail(s){var e=this;return(0,_.A)(function*(){let t=new l.Nl;const{appRef:a}=s;return a&&(t=t.append("appRef",a)),yield(0,u.s)(e.http.get(`${e.url}images`,{params:t}))})()}getVolumeOrderByParticularClient(s){var e=this;return(0,_.A)(function*(){let t=new l.Nl;const{status:a=300,offset:r,limit:d,enable:g=!0,associatedCommercialId:m,startDate:P,endDate:v,customerName:b}=s;void 0!==r&&(t=t.append("offset",r)),d&&(t=t.append("limit",d)),a&&(t=t.append("status",a)),m&&(t=t.append("user.associatedCommercial._id",m)),t=t.append("enable",g),P&&v&&(t=t.append("startDate",new C.vh("fr").transform(P,"YYYY-MM-dd")),t=t.append("endDate",new C.vh("fr").transform(v,"YYYY-MM-dd"))),b&&(t=t.append("user.firstName",b));try{return yield(0,u.s)(e.http.get(`${e.url}scanner-data/volume-order-by-particular-client`,{params:t}))}catch(D){const w={message:e.commonSrv.getError("",D).message,color:"danger"};return yield e.commonSrv.showToast(w),D}})()}static{this.\u0275fac=function(e){return new(e||h)(f.KVO(l.Qq),f.KVO(n.h),f.KVO(E.K))}}static{this.\u0275prov=f.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})()},68896:(T,y,o)=>{o.d(y,{I:()=>N});var _=o(73308),l=o(2978),i=o(40129),C=o(82571),u=o(45312),f=o(26409),n=o(33607),E=o(14599),A=o(94934),h=o(56610);let N=(()=>{class s{constructor(){this.commonSrv=(0,l.WQX)(C.h),this.http=(0,l.WQX)(f.Qq),this.baseUrl=(0,l.WQX)(n.K),this.storageSrv=(0,l.WQX)(E.n),this.base_url=`${this.baseUrl.getOrigin()}${u.c.basePath}`}validateScanData(t){var a=this;return(0,_.A)(function*(){try{return yield(0,A.s)(a.http.post(`${a.base_url}scanner-data`,t))}catch(r){const g={message:a.commonSrv.getError("",r).message,color:"danger"};return yield a.commonSrv.showToast(g),r}})()}checkPermission(){return(0,_.A)(function*(){try{const{camera:t}=yield i.vi.requestPermissions();return"granted"===t}catch(t){return console.log(t),!1}})()}stopScan(){var t=this;return(0,_.A)(function*(){t.currDisplay=!1,document.querySelector("body").classList.remove("scanner-active")})()}showContent(){document.querySelectorAll(".hide-on-scan").forEach(a=>{a.style.display=""}),document.querySelector("body").classList.remove("scanner-active")}prepareScanner(){return(0,_.A)(function*(){document.body.classList.add("scanner-active")})()}startScan(){var t=this;return(0,_.A)(function*(){try{if(!(yield t.checkPermission()))return void(yield t.commonSrv.showToast({color:"danger",message:"Permission refus\xe9e pour utiliser la cam\xe9ra"}));yield t.prepareScanner(),yield i.vi.installGoogleBarcodeScannerModule(),console.log("\u2705 Module MLKit install\xe9 avec succ\xe8s");const{barcodes:r}=yield i.vi.scan();if(t.restoreUI(),r&&r.length>0)return console.log("\u{1f3af} Scan r\xe9ussi:",r[0]),r[0].rawValue||r[0].displayValue;yield t.commonSrv.showToast({color:"warning",message:"Aucun code-barres d\xe9tect\xe9"})}catch(a){console.error("\u274c Erreur MLKit scan:",a),yield t.commonSrv.showToast({color:"danger",message:"Erreur lors du scan"})}finally{t.stopScan()}})()}restoreUI(){document.body.classList.remove("scanner-active")}getVolumeOrderByParticularClient(t){var a=this;return(0,_.A)(function*(){let r=new f.Nl;const{status:d=300,offset:g,limit:m,enable:P=!0,associatedCommercialId:v,startDate:b,endDate:D,customerName:I}=t;void 0!==g&&(r=r.append("offset",g)),m&&(r=r.append("limit",m)),d&&(r=r.append("status",d)),v&&(r=r.append("user.associatedCommercial._id",v)),r=r.append("enable",P),b&&D&&(r=r.append("startDate",new h.vh("fr").transform(b,"YYYY-MM-dd")),r=r.append("endDate",new h.vh("fr").transform(D,"YYYY-MM-dd"))),I&&(r=r.append("user.firstName",I));try{return yield(0,A.s)(a.http.get(`${a.base_url}scanner-data/volume-order-by-particular-client`,{params:r}))}catch(w){const x={message:a.commonSrv.getError("",w).message,color:"danger"};return yield a.commonSrv.showToast(x),w}})()}static{this.\u0275fac=function(a){return new(a||s)}}static{this.\u0275prov=l.jDH({token:s,factory:s.\u0275fac,providedIn:"root"})}}return s})()}}]);