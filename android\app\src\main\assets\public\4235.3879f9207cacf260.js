"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4235],{4235:(R,a,t)=>{t.r(a),t.d(a,{NavigationPageModule:()=>y});var g=t(56610),s=t(37222),i=t(77897),h=t(77575),m=t(58133),l=t(2978),P=t(82571);const C=[{path:"",children:[{path:"home-alt",loadChildren:()=>Promise.all([t.e(9963),t.e(8047),t.e(2076),t.e(964)]).then(t.bind(t,30964)).then(n=>n.HomeParticularPageModule)},{path:"home",loadChildren:()=>Promise.all([t.e(9963),t.e(8047),t.e(2297)]).then(t.bind(t,72297)).then(n=>n.HomePageModule)},{path:"",component:(()=>{class n{constructor(d,o){this.router=d,this.commonService=o}ngOnInit(){const d=this.commonService.user;[m.s.Particular,m.s.DonutAnimator].includes(d?.category)?this.router.navigate(["/navigation/home-alt"]):this.router.navigate(["/navigation/home"])}static{this.\u0275fac=function(o){return new(o||n)(l.rXU(h.Ix),l.rXU(P.h))}}static{this.\u0275cmp=l.VBU({type:n,selectors:[["app-redirect"]],decls:0,vars:0,template:function(o,F){},encapsulation:2})}}return n})()}]},{path:"faq-page",loadChildren:()=>t.e(6839).then(t.bind(t,26839)).then(n=>n.FaqPagePageModule)},{path:"lexique",loadChildren:()=>t.e(4768).then(t.bind(t,24768)).then(n=>n.LexiquePageModule)},{path:"general-condition",loadChildren:()=>t.e(6110).then(t.bind(t,96110)).then(n=>n.GeneralConditionPageModule)},{path:"reporting",loadChildren:()=>t.e(6849).then(t.bind(t,46849)).then(n=>n.ReportingModule)},{path:"claim-form",loadChildren:()=>Promise.all([t.e(3037),t.e(2076),t.e(6033)]).then(t.bind(t,16033)).then(n=>n.ClaimFormPageModule)},{path:"account-balance",loadChildren:()=>Promise.all([t.e(7438),t.e(7186),t.e(2076),t.e(671)]).then(t.bind(t,671)).then(n=>n.AccountBalancePageModule)},{path:"reload-balance",loadChildren:()=>t.e(1482).then(t.bind(t,41482)).then(n=>n.ReloadBalancePageModule)},{path:"jde-report",loadChildren:()=>t.e(7821).then(t.bind(t,17821)).then(n=>n.JdeReportPageModule)},{path:"jde-loads-not-dispatched",loadChildren:()=>t.e(831).then(t.bind(t,60831)).then(n=>n.JdeLoadsNotDispatchedPageModule)},{path:"reprint-invoice",loadChildren:()=>t.e(8553).then(t.bind(t,58553)).then(n=>n.ReprintInvoicePageModule)},{path:"notifications",loadChildren:()=>t.e(2605).then(t.bind(t,72605)).then(n=>n.NotificationsPageModule)},{path:"retrievements",loadChildren:()=>t.e(3400).then(t.bind(t,13400)).then(n=>n.RetrievementsPageModule)},{path:"removals",loadChildren:()=>t.e(4282).then(t.bind(t,64282)).then(n=>n.RemovalsPageModule)},{path:"companies-account",loadChildren:()=>t.e(3674).then(t.bind(t,63674)).then(n=>n.CompanieAccountPageModule)},{path:"lexique",loadChildren:()=>t.e(4768).then(t.bind(t,24768)).then(n=>n.LexiquePageModule)},{path:"account",loadChildren:()=>Promise.all([t.e(3037),t.e(9686)]).then(t.bind(t,19686)).then(n=>n.MyAccountPageModule)},{path:"market-place",loadChildren:()=>Promise.all([t.e(2076),t.e(1864)]).then(t.bind(t,71864)).then(n=>n.MarketPlacePageModule)},{path:"contact",loadChildren:()=>t.e(5068).then(t.bind(t,15068)).then(n=>n.ContactPageModule)},{path:"catalogue",loadChildren:()=>Promise.all([t.e(1468),t.e(7164)]).then(t.bind(t,39441)).then(n=>n.CataloguePageModule)},{path:"feedback",loadChildren:()=>Promise.all([t.e(2076),t.e(5388)]).then(t.bind(t,95388)).then(n=>n.FeedbackPageModule)},{path:"feedback-detail",loadChildren:()=>Promise.all([t.e(2076),t.e(2996)]).then(t.bind(t,2996)).then(n=>n.FeedbackDetailPageModule)},{path:"home-particular",loadChildren:()=>Promise.all([t.e(9963),t.e(8047),t.e(2076),t.e(964)]).then(t.bind(t,30964)).then(n=>n.HomeParticularPageModule)},{path:"fidelity-program",loadChildren:()=>Promise.all([t.e(2076),t.e(6039)]).then(t.bind(t,76039)).then(n=>n.FidelityProgramPageModule)},{path:"account-management",loadChildren:()=>t.e(5882).then(t.bind(t,25882)).then(n=>n.AccountManagementPageModule)},{path:"user-list",loadChildren:()=>Promise.all([t.e(7438),t.e(2076),t.e(6084)]).then(t.bind(t,66084)).then(n=>n.UserListPageModule)},{path:"indirect-user",loadChildren:()=>Promise.all([t.e(973),t.e(2076),t.e(2391)]).then(t.bind(t,22391)).then(n=>n.IndirectUserPageModule)},{path:"create-indirect",loadChildren:()=>Promise.all([t.e(3037),t.e(4608),t.e(356)]).then(t.bind(t,90356)).then(n=>n.CreateIndirectAccountPageModule)},{path:"manage-user",loadChildren:()=>t.e(3973).then(t.bind(t,43973)).then(n=>n.ManageAccountModule)},{path:"qr-code",loadChildren:()=>t.e(225).then(t.bind(t,30225)).then(n=>n.QrCodePageModule)}];let M=(()=>{class n{static{this.\u0275fac=function(o){return new(o||n)}}static{this.\u0275mod=l.$C({type:n})}static{this.\u0275inj=l.G2t({imports:[h.iI.forChild(C),h.iI]})}}return n})();var v=t(74657),f=t(93887);let y=(()=>{class n{static{this.\u0275fac=function(o){return new(o||n)}}static{this.\u0275mod=l.$C({type:n})}static{this.\u0275inj=l.G2t({imports:[s.YN,i.bv,f.G,g.MD,v.h,s.X1,M]})}}return n})()}}]);