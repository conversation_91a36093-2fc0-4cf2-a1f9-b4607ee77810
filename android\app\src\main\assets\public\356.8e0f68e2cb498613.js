"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[356],{90356:(x,O,a)=>{a.r(O),a.d(O,{CreateIndirectAccountPageModule:()=>A});var f=a(56610),l=a(37222),F=a(24608),d=a(77897),P=a(77575),p=a(73308),n=a(2978),E=a(23985),y=a(44444),T=a(43556),I=a(82571),r=a(63037),C=a(71682),u=a(58133),s=a(5141),t=a(74657);function m(c,_){1&c&&n.nrm(0,"app-progress-spinner")}function h(c,_){if(1&c&&(n.j41(0,"ion-select-option",28),n.EFF(1),n.k0s()),2&c){const e=_.$implicit;n.Y8G("value",e),n.R7$(1),n.SpI(" ",e,"")}}const b=function(c,_){return{id:c,name:_}};function M(c,_){if(1&c&&(n.j41(0,"ion-select-option",28),n.EFF(1),n.k0s()),2&c){const e=_.$implicit;n.Y8G("value",n.l_i(2,b,null==e?null:e._id,null==e?null:e.name)),n.R7$(1),n.SpI(" ",null==e?null:e.name," ")}}function k(c,_){1&c&&n.nrm(0,"ion-spinner",29)}const R=function(){return["/navigation/user-list"]},U=[{path:"",component:(()=>{class c{constructor(e,o){this.fb=e,this.imageCompress=o,this.companySrv=(0,n.WQX)(T.B),this.commonService=(0,n.WQX)(I.h),this.location=(0,n.WQX)(f.aZ),this.userServ=(0,n.WQX)(E.D),this.authenticationService=(0,n.WQX)(C.k),this.LIMIT_SIZE=15e5,this.hasDescription=!1,this.companies=[],this.userToUpdate=null,this.attachment={file:"",name:"",contentType:""}}ngOnInit(){var e=this;return(0,p.A)(function*(){e.userToUpdate=e.userServ.currentUserParticular,e.initForm(),e.regions=e.commonService.getRegions(),e.companies=(yield e.companySrv.getCompanies({projection:"name,erpSoldToId"}))?.data,e.userToUpdate&&e.patchForm(e.userToUpdate)})()}getRegion(e){this.cities=this.commonService.getCities(e.detail.value)}initForm(){this.form=this.fb.group({firstName:["",l.k0.required],social:[""],tel:["",[l.k0.required]],region:["",l.k0.required],district:[""],associatedCompanies:[[]]})}patchForm(e){this.form.patchValue({firstName:e.firstName,tel:e.tel,social:e.socialReason,region:e.address?.region||"",district:e.address?.district||"",associatedCompanies:e.associatedCompanies?.map(o=>o._id)||[]})}onSubmit(){var e=this;return(0,p.A)(function*(){if(e.isLoading=!0,e.form.invalid)return e.markFormGroupTouched(e.form),e.commonService.showToast({color:"warning",message:"An error occurred"}),void(e.isLoading=!1);const o=e.form.value,i=new y.cs(0);i.firstName=o.firstName,i.socialReason=o.social,i.category=u.s.Particular,i.tel=o.tel,i.address={region:e.getCommercialRegion(o.region),district:o.district},i.associatedCompanies=o.associatedCompanies,i.profilePicture=e.attachment.file;try{if(e.userToUpdate){const g=e.form.value;e.userToUpdate.firstName=g.firstName||e.userToUpdate.firstName,e.userToUpdate.socialReason=g.social||e.userToUpdate.socialReason,e.userToUpdate.category=u.s.Particular,e.userToUpdate.tel=g.tel||e.userToUpdate.tel,e.userToUpdate.address={region:g.region?e.getCommercialRegion(g.region):e.userToUpdate.address?.region,district:g.district||e.userToUpdate.address?.district},yield e.userServ.updateUserParticular(e.userToUpdate),e.location.back(),e.isLoading=!1,e.userServ.currentUserParticular=null,e.form.reset()}else 201==(yield e.authenticationService.signupParticular(i))?.status&&(e.location.back(),e.isLoading=!1,e.form.reset())}catch{}finally{e.isLoading=!1}})()}getCommercialRegion(e){const o=s.MJ,i=e.trim().toLowerCase();console.log("Normalized region:",i);for(const[g,v]of Object.entries(o))if(console.log("Checking key:",g,"with regions:",v),v.some(w=>w.trim().toLowerCase()===i))return console.log("Match found:",g),g;return console.log("No match found"),null}markFormGroupTouched(e){Object.values(e.controls).forEach(o=>{o.markAsTouched(),o instanceof l.gE&&this.markFormGroupTouched(o)})}setAttachment(e){var o=this;return(0,p.A)(function*(){try{o.file=o.getFileFromDataSet(e),o.attachment.file=yield o.getFileDataUrl(o.file);let i=yield o.getFileSize(o.file,o.attachment.file);o.validateFileSize(i,o.LIMIT_SIZE),[o.attachment.name,o.attachment.contentType]=[o.file.name,o.file.type]}catch(i){o.handleError(i)}})()}getFileFromDataSet(e){const o=e.target.files[0];return this.validate(o),o}getFileDataUrl(e){var o=this;return(0,p.A)(function*(){const i=yield o.convertFileToDataUrl(e);return o.validate(i),i})()}convertFileToDataUrl(e){return new Promise((o,i)=>{const g=new FileReader;g.readAsDataURL(e),g.onload=()=>o(g.result),g.onerror=v=>i(v)})}validate(e){if(!e)throw new Error("Une erreur est survenue, veuillez ressayer SVP !")}getFileSize(e,o){var i=this;return(0,p.A)(function*(){let g=e.size;return"application/pdf"!=e.type&&(o=yield i.imageCompress.compressFile(o,r._k.Up),g=i.imageCompress.byteCount(o)),g})()}validateFileSize(e,o){if(e>o)throw new Error("Veuillez choisir un fichier de moins de 1,5 MB SVP !")}handleError(e){this.file=null,this.commonService.showToast({color:"danger",message:`${e.message}`})}static{this.\u0275fac=function(o){return new(o||c)(n.rXU(l.ok),n.rXU(r.ep))}}static{this.\u0275cmp=n.VBU({type:c,selectors:[["app-create-indirect-account"]],decls:57,vars:23,consts:[[4,"ngIf"],[3,"translucent"],[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",1,"img",3,"routerLink"],[1,"buttons"],[1,"transparent"],["name","search"],["name","funnel-outline"],[3,"fullscreen"],[3,"formGroup","ngSubmit"],["position","floating"],["formControlName","firstName","clearInput",""],["formControlName","social","clearInput",""],["type","number","clearInput","","formControlName","tel"],["type","text","clearInput","","formControlName","district"],["mode","ios","formControlName","region","interface","action-sheet",3,"cancelText","ionChange"],["stores",""],[3,"value",4,"ngFor","ngForOf"],["formControlName","associatedCompanies","multiple","true","mode","ios","interface","action-sheet"],[1,"iconImage"],[1,"image"],[1,"attachment-btn"],["type","file","id","file","accept","image/*,.pdf","multiple","",2,"display","none",3,"change"],["src","/assets/icons/imageIcon.svg","alt","Upload","slot","end","onclick","document.getElementById('file').click()",2,"cursor","pointer","width","40px","height","40px"],[1,"image","localisation"],[1,"btn-validate"],["type","submit","color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled"],["name","bubbles",4,"ngIf"],[3,"value"],["name","bubbles"]],template:function(o,i){1&o&&(n.DNE(0,m,1,0,"app-progress-spinner",0),n.j41(1,"ion-header",1)(2,"div",2),n.nrm(3,"ion-img",3),n.j41(4,"ion-title"),n.EFF(5," Clients indirects "),n.k0s()(),n.j41(6,"div",4)(7,"ion-button",5),n.nrm(8,"ion-icon",6),n.k0s(),n.j41(9,"ion-button",5),n.nrm(10,"ion-icon",7),n.k0s()()(),n.j41(11,"ion-content",8)(12,"form",9),n.bIt("ngSubmit",function(){return i.onSubmit()}),n.j41(13,"ion-item")(14,"ion-label",10),n.EFF(15," Nom + Pr\xe9nom "),n.k0s(),n.nrm(16,"ion-input",11),n.k0s(),n.j41(17,"ion-item")(18,"ion-label",10),n.EFF(19," Appellation commerciale"),n.k0s(),n.nrm(20,"ion-input",12),n.k0s(),n.j41(21,"ion-item")(22,"ion-label",10),n.EFF(23," Num\xe9ro de T\xe9l\xe9phone"),n.k0s(),n.nrm(24,"ion-input",13),n.k0s(),n.j41(25,"ion-item")(26,"ion-label",10),n.EFF(27,"Localisation"),n.k0s(),n.nrm(28,"ion-input",14),n.k0s(),n.j41(29,"ion-item")(30,"ion-label",10),n.EFF(31),n.nI1(32,"translate"),n.k0s(),n.j41(33,"ion-select",15,16),n.bIt("ionChange",function(v){return i.getRegion(v)}),n.nI1(35,"translate"),n.DNE(36,h,2,2,"ion-select-option",17),n.k0s()(),n.j41(37,"ion-item")(38,"ion-label",10),n.EFF(39),n.nI1(40,"translate"),n.k0s(),n.j41(41,"ion-select",18),n.DNE(42,M,2,5,"ion-select-option",17),n.k0s()(),n.j41(43,"div",19)(44,"div",20)(45,"div",21)(46,"input",22),n.bIt("change",function(v){return i.setAttachment(v)}),n.k0s(),n.nrm(47,"img",23),n.k0s()(),n.j41(48,"div",24)(49,"ion-label",10),n.EFF(50),n.k0s()()(),n.j41(51,"div",25)(52,"ion-button",26)(53,"ion-label"),n.EFF(54),n.nI1(55,"translate"),n.k0s(),n.DNE(56,k,1,0,"ion-spinner",27),n.k0s()()()()),2&o&&(n.Y8G("ngIf",i.isLoading),n.R7$(1),n.Y8G("translucent",!0),n.R7$(2),n.Y8G("routerLink",n.lJ4(22,R)),n.R7$(8),n.Y8G("fullscreen",!0),n.R7$(1),n.Y8G("formGroup",i.form),n.R7$(19),n.SpI(" ",n.bMT(32,14,"reseller-new-page.first-step.select-region-label")," "),n.R7$(2),n.FS9("cancelText",n.bMT(35,16,"button.cancel")),n.R7$(3),n.Y8G("ngForOf",i.regions),n.R7$(3),n.SpI(" ",n.bMT(40,18,"indirect-clients.vendors")," "),n.R7$(3),n.Y8G("ngForOf",i.companies),n.R7$(8),n.SpI("Images : ",null==i.file?null:i.file.name," "),n.R7$(2),n.Y8G("disabled",!i.form.valid),n.R7$(2),n.SpI(" ",n.bMT(55,20,"order-new-page.planning-modal.save-button-label")," "),n.R7$(2),n.Y8G("ngIf",i.isLoading))},dependencies:[f.Sq,f.bT,l.qT,l.BC,l.cb,l.j4,l.JD,d.Jm,d.W9,d.eU,d.iq,d.KW,d.$w,d.uz,d.he,d.Nm,d.Ip,d.w2,d.BC,d.su,d.Je,d.Gw,d.N7,P.Wk,t.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{display:flex;padding-top:13px;align-items:center}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700!important;margin:auto}ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res));color:#0b305c}.header[_ngcontent-%COMP%]{--background: #F1F2F4;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.buttons[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent !important;--box-shadow: none;color:#0b305c;border:none;--padding: auto}.buttons[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#419cfb}ion-content[_ngcontent-%COMP%]{--background: #F1F2F4}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{width:calc(100% - 48px);margin:2em auto auto;padding:auto;--background: #F1F2F4}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:.75rem;color:#0b305c}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0px;margin-bottom:calc(41 * var(--res));--background: $color-nineteen;--ripple-color: transparent;--background-activated: transparent;--background-activated-opacity: transparent;--background-focused: transparent;--background-focused-opacity: transparent;--background-hover: transparent;--background-hover-opacity: transparent}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#fff}ion-content[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:.75rem}ion-content[_ngcontent-%COMP%]   .submit[_ngcontent-%COMP%]{width:calc(100% - 48px);margin-left:24px}ion-content[_ngcontent-%COMP%]   .add[_ngcontent-%COMP%]{font-size:.6rem;font-weight:700;--color: #0B305C;--background: #419CFB2B;--padding: 5px 10px;--border-radius: 30px;margin:1rem auto}ion-content[_ngcontent-%COMP%]   .iconImage[_ngcontent-%COMP%]{display:flex;width:calc(100% - 48px);margin-bottom:1rem}ion-content[_ngcontent-%COMP%]   .iconImage[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{border:1.12px solid #D5DFEB;border-radius:6px;padding:.5rem}ion-content[_ngcontent-%COMP%]   .iconImage[_ngcontent-%COMP%]   .localisation[_ngcontent-%COMP%]{margin-left:.2rem}"]})}}return c})()}];let S=(()=>{class c{static{this.\u0275fac=function(o){return new(o||c)}}static{this.\u0275mod=n.$C({type:c})}static{this.\u0275inj=n.G2t({imports:[P.iI.forChild(U),P.iI]})}}return c})(),A=(()=>{class c{static{this.\u0275fac=function(o){return new(o||c)}}static{this.\u0275mod=n.$C({type:c})}static{this.\u0275inj=n.G2t({imports:[f.MD,l.YN,l.X1,d.bv,F.vj,t.h,S]})}}return c})()},43556:(x,O,a)=>{a.d(O,{B:()=>I});var f=a(73308),l=a(94934),F=a(45312),d=a(26409),p=(a(99987),a(2978)),n=a(33607),E=a(82571),y=a(14599),T=a(74657);let I=(()=>{class r{constructor(u,s,t,m,h){this.baseUrl=u,this.http=s,this.commonSrv=t,this.storageSrv=m,this.translateService=h,this.base_url=`${this.baseUrl.getOrigin()}${F.c.basePath}`,this.base_url+="companies"}create(u){var s=this;return(0,f.A)(function*(){try{return delete u._id,yield(0,l.s)(s.http.post(s.base_url,u))}catch(t){return s.commonSrv.getError("Echec de cr\xe9ation de la compagnie",t)}})()}getCompanies(u){var s=this;return(0,f.A)(function*(){try{let t=new d.Nl;const{category:m,city:h,limit:b,name:M,regionCom:k,solToId:R,tel:D,users:U,offset:S,enable:A=!0,projection:c,isLoyaltyProgDistributor:_}=u;return void 0!==m&&(t=t.append("category",m)),h&&(t=t.append("address.city",h)),M&&(t=t.append("name",M)),R&&(t=t.append("erpSoldToId",R)),D&&(t=t.append("tel",`${D}`)),c&&(t=t.append("projection",`${c}`)),U&&(t=t.append("users",`${U}`)),k&&(t=t.append("address.commercialRegion",k)),_&&(t=t.append("isLoyaltyProgDistributor",_)),void 0!==b&&(t=t.append("limit",b)),void 0!==S&&(t=t.append("offset",S)),t=t.set("enable",A),yield(0,l.s)(s.http.get(s.base_url,{params:t}))}catch(t){const h={message:s.commonSrv.getError("",t).message,color:"danger"};return yield s.commonSrv.showToast(h),t}})()}getParticularCompanies(u){var s=this;return(0,f.A)(function*(){let t=new d.Nl;const{limit:m,offset:h,enable:b=!0,commercialRegion:M}=u;return void 0!==m&&(t=t.append("limit",m)),void 0!==h&&(t=t.append("offset",h)),M&&(t=t.append("address.commercialRegion",M)),t=t.set("enable",b),yield(0,l.s)(s.http.get(s.base_url+"/particular-suppliers",{params:t}))})()}find(u){var s=this;return(0,f.A)(function*(){try{return yield(0,l.s)(s.http.get(s.base_url+"/"+u))}catch{return s.commonSrv.initCompany()}})()}getBalance(u){var s=this;return(0,f.A)(function*(){try{let t=new d.Nl;const{company:m}=s.storageSrv.getUserConnected();return t=t.set("_id",m?m?._id:u?.companyId),yield(0,l.s)(s.http.get(`${s.base_url}/balance`,{params:t}))}catch(t){return yield s.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de votre solde",color:"danger"}),t}})()}getUsersCompany(u,s){var t=this;return(0,f.A)(function*(){try{let m=new d.Nl;const{email:h,enable:b=!0}=s;return h&&(m=m.append("email",h)),m=m.append("enable",b),yield(0,l.s)(t.http.get(`${t.base_url}/${u}/users`,{params:m}))}catch(m){return yield t.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de vos informations",color:"danger"}),m}})()}static{this.\u0275fac=function(s){return new(s||r)(p.KVO(n.K),p.KVO(d.Qq),p.KVO(E.h),p.KVO(y.n),p.KVO(T.c$))}}static{this.\u0275prov=p.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})()},44444:(x,O,a)=>{a.d(O,{PB:()=>y,cs:()=>F,iL:()=>I}),a(68953);class l{constructor(C){this.email="",this.firstName="",this.lastName="",this.tel="",this.password="",this.cni="",this.nui="",this.address={region:"",city:"",district:""},this.category=C}}class F extends l{}var y=function(r){return r[r.NORMAL=100]="NORMAL",r[r.CORDO_RH=101]="CORDO_RH",r[r.DRH=102]="DRH",r}(y||{}),I=function(r){return r[r.BHB=101]="BHB",r[r.BS=102]="BS",r[r.BPI=103]="BPI",r}(I||{})}}]);