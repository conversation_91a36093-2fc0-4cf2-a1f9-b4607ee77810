{"preposition": {"to": "to "}, "button": {"cancel": "Cancel", "download": "Download", "accept": "ACCEPT", "save-edit-carrier": "Save / Edit carrier"}, "reset-password-page": {"title": "Forgot your password?", "action-label": "Change the password", "back-link-label": "Back to the login page", "description": "By entering new information, you overwrite the previously entered information."}, "signin-page": {"title": "Log in", "phone": "Phone", "labelOtp": "Enter your phone munber  or your email", "mdp": "Enter password", "signin-button": "<PERSON><PERSON>", "signin-confirm": "Confirm", "show-password": "Show password", "reset-password-link": "Forgot your password ?", "new-account-button": "New account", "description": "If you already have a Click Cadyst account, please enter your phone number.", "text-message": "Enter your phone number", "verify-otp": "Enter the code that has just been sent to your telephone number", "change-language": "Change language", "modal-create-account": {"title": "Select the type of account", "next-button-label": "Next"}, "modal-otp-description": {"title": "OTP", "instruction": "Enter the 7 digit send to your media !!!", "label": "Enter the  code", "button-confirm": "LOGIN", "button-login": "<PERSON><PERSON>", "do-not-recieve-the-code": " don't recieve your code ? clic on the link to resend", "resend": "Resend", "resend-otp": "Resend OTP", "show-modal": "I have otp code", "placeholder": "Enter the temporary code", "change-phone": "Change the phone number"}}, "bottom-sheet-validation": {"validateOparation": "Validation of the operation", "accountInformation": "Account Information", "name": "Name", "lastName": "Last Name", "tel": "phone", "Quartier": "District", "socialReasean": "Company name", "company": "Company"}, "signup-page": {"first-step": {"description": "If you already have a customer account with Click Cadyst, please contact us at", "lastName": "Last Name", "firstName": "First name", "sponsor": "Sponsor's name", "sociale name": "Company name", "phone": "phone", "numberCni": "CNI number", "numberNui": "NIU number", "step-label": "step"}, "second-step": {"input-profession-label": "Enter your profession", "input-profession-placeholder": "Enter your profession", "input-serial-number-label": "Enter your registration number", "input-serial-number-placeholder": "Enter your registration number", "input-social-reason-label": "Company name", "input-social-reason-placeholder": "Enter your social reason", "input-district-label": "District", "input-password-label": "Password", "input-confirm-password-label": "Confirm the password", "select-company-label": "Select a company", "select-type-employee": "Type of employee", "select-status-employee": "Status of employee", "DRH": "DRH", "Cordo": "CORDO-RH", "normal": "Normal", "signup-page.second-step.retired": "Retired", "signup-page.second-step.not-retired": "Active", "select-direction-label": "Select a direction", "select-service-label": "Select a service", "select-position-label": "Select a position", "select-region-label": "Select a region", "select-commercialRegion-label": "Commercial region", "select-city-label": "Select a city", "validate-button-label": "Validate", "cancel-button-label": "Cancel", "back-link-label": "Back"}}, "profile": {"logout-button-label": "Logout", "save-profile-picture": "Save profil picture", "disable-button-label": "Delete my account", "title": "Account management", "retailer": {"edit": "Edit ", "cancel": "Cancel ", "input-lastName-label": "Name", "input-firstName-label": "First name", "input-name-label": "Full name", "input-phone-label": "Telephone", "input-company-label": "Company", "cni": "CNI", "nui": "NUI", "Rccm": "RCCM", "input-social-reason-label": "Raison social", "input-social-reason-placeholder": "Enter your company name", "select-region-label": "Select a region", "select-city-label": "Select a city", "input-district-label": "District", "input-password-label": "Password", "input-address-label": "Address", "tonnageLelft": "Remaining tonnage", "profession": "Occupation", "update-button-label": "Modify", "save-button-label": "Save"}, "client-account": "User profile", "edit-profile": "Edit profile", "cancel": "Annuler", "account-manage": "Account management"}, "reporting": {"Reseller-statistics": "Reseller statistics", "title": "Statistics", "quantities-ordered": "quantities ordered", "top-products": "Top products", "Purchasing No.-1": "Purchasing No. 1", "Top-suppliers": "Top suppliers", "#1-distributors-for-you": "#1-distributors for you", "quantities-purchased": "quantities purchased", "products": "Products", "quantity": "Qty", "name": "Name", "evolution_sale": "Evolution of purchases", "reporting": "Reporting", "month": "Month", "btn-filter": "Filter", "bags": "bags", "total-sales": "Total of purchases", "sales": "of sales", "avg": "Average", "avg-time-validation": "Average validation time", "clients": "Particular clients associated", "totalClientCount": "(Total clients)", "clientName": "Client name"}, "claim-form-page": {"title": "Make a complaint", "description": "You can also contact us at the following number:", "input-type-label": "type of claim", "input-motivation-label": "claim reason", "input-phone-label": "phone number", "input-message-label": "message", "submit-button-label": "Send", "add-piece-button-label": "Add Attachment(s)"}, "claims-page": {"title": "My Claims", "reference": "Reference:", "category": "Category:", "subcategory": "Subcategory:", "date": "Date:", "status": "Status:", "empty-claim": "You have no claims:", "add-claim": "Make a New Claim"}, "claim-page": {"tabs": {"in-progres": "In Progress", "treat": "Processed"}}, "claims-page-detail": {"title": "<PERSON><PERSON><PERSON>"}, "feedback-detail": {"message": "Message:", "download-attachment": "Download Attachment"}, "order-new-page": {"information-label": "Information about your order", "searchbar-label": "Search product", "new-order": "New order", "maintenance": {"first": "The Click Cadyst application is currently undergoing maintenance", "second": "Come back in a few moments", "third": "Thank you for your understanding."}, "first-step": {"capacityPerYear": "Remaining annual tonnage capacity", "capacityLeft": "Remaining tonnage capacity", "title": "Select the point of sale", "select-packaging-label": "Bagging", "select-distributor-label": "Choose a distributor", "select-city-label": "Choose the city", "location": "Enter a specific address.", "delivery-date": "Wished date of delivery", "input-phone-label": "Enter the telephone number", "input-phone-driver": "Driver phone number", "input-deliver-address-label": "Delivery to my addresses", "select-address-label": " Choose an address ", "select-company": "Select client", "next-button-label": "Next", "choose-store": "Choose your supplier", "choose": "CHOOSE", "supplier": "Other suppliers", "question-about-delivery": {"label": "Do you want to be delivered?", "yes-button": "Yes", "no-button": "No"}, "select-region": "select region", "select-city": "select city", "delivery-location": "Address delivery  expected", "destination-address": "Destination address delivery", "driver-info": "Carrier information", "driver-name": "Driver's name", "driver-id": "Driver ID-Card", "driver-vehicle": "License plate", "driver-category": "Truck category", "driver-license": "Driver License", "category": "Other categorie"}, "schedule-list": {"title": "My planning", "heure": "Hours", "camions": "Truck", "quantity": "Quantity", "new-line": "New removal", "add-line": "add quantity", "empty-list": "there'is any planing"}, "second-step": {"next-button-label": "NEXT", "title": "Select a product", "title-particular": "I choose my bags", "quantity": "quantity", "amount": "Cost", "bag-off": "Bags", "all": "All", "empty-offer-price": "You do not have a price quote"}, "third-step": {"title": "Summary of purchases", "title-shipping": "Delivery Summary", "carrier-title": "Carrier Informations", "amount": "Amount", "amount-ht": "Amount ht", "taxes": "Taxes", "cart-amount": "Cart amount", "discountCash": "Discount Option", "shipping-costs": "Shipping costs", "total-ttc": "Total TTC", "amount-of-delivery": "Shipping costs", "date-of-delivery": "Delivery date", "prepayment": "Prepayment", "purchased-product": "Purchased products", "swipe-to-show-element": "Slide to the right to see more", "payment-label": "Select the payment method ", "next-button-label": "ORDER", "modify-button-label": "MODIFY", "nbBag": "Nb Bag", "unitCost": "Unit cost", "unitPrice": "Unit price", "seeMore": "See More", "qte": "Qty", "product": "Product(s)", "total-tons": "Total Tons", "flour": "FLOUR", "parcel": "Bags", "deliveryDiscount": "Delivery Option", "apply-points": "APPLY MY POINTS"}, "planning-modal": {"title": "Command", "title-quantity": "Quantity to buy", "remainning-quantity": "Available quantities in stock", "tons": "Tons", "quantity": "Bag quantity", "label-quantity": "Manual entry", "quick-select": "Quick choice", "date": "Date of removal", "quart-time": "Choose a quarter-time", "hour": "Hour of Abduction", "indication-quantity": "Indicate the quantities to be removed", "remains": "You still have {{value1}} / {{value2}} tonnes", "truck": "Number of Trucks", "tonne-truck": "Total/trucks", "nb-tone": "Number of Tons", "form-quantity": "Removal by quantity", "form-date": "Removal by date", "select-schedule": "Type of removal", "schedule-button-label": "View Planning", "save-button-label": "Save"}, "last-step": {"back-button-label": "Back to home", "thanks": "CLICK CADYST WOULD LIKE TO THANK YOU", "for-your-orders": "for your order"}}, "order": {"detail": {"title": "Order Details", "reference": "Order N°", "name": "Name", "email": "Email", "created-at": "Creation date", "validated-at": "Validation date", "product-list": "List of products", "product-info-slide": "Slide to the right to see more", "order-recap": "Summary of the order", "motif-rejet": "Reject reason", "payment-mode": "Mode of payment", "transaction-ref": "Transaction Id", "total-ht": "Total HT", "total-ttc": "Total TTC", "delivery": {"title": "Delivery information", "mode": "Mode", "location": "Location", "amount": "Amount", "date": "Delivery wish"}, "update": {"title": "Update order information"}, "valid": {"title": "Update order information", "desc": "You are about to validate this order, confirm your action by entering a reference"}}, "rejection": {"description": "You are about to reject this order. Please provide a detailed reason for this action.", "reasonLabel": "Rejection Reason", "placeholder": "Enter the reason for rejection here..."}}, "bottom-sheet": {"other": {"title": "Others", "add-referral": "Add referral"}, "payment": {"label": "Confirm the payment of your order in the amount of", "mode": "Mode of payment", "reference": "Reference command", "walletNber": "Wallet number", "otpInsert": "insert otp", "account-ref": "Phone numbers", "limitation": "Your order cannot exceed the amount of", "specification": "Payment of your order will be made via", "plateform": "on this platform", "validate-button-label": "VALIDATE THE PAYMENT", "send-otp": "Send otp code", "send-wallet": "Check wallet number", "enter-otp": "Enter otp code", "ref-check": "Enter check code", "num-transaction": "Slip numbers", "amount-num": "Transaction amounts", "select-bank": "Select a bank", "send-validate-button-label": "SEND TO VALIDATION", "amount-per-month": "Amount per month", "monthlyPayment": "Number of monthly payments", "startDatePaiement": "Start date of payment", "send-in-validation-btn": "Send in validation", "error-balance-insufficient-amount": "The balance of your account is insufficient to carry out this order, you have the possibility of sending it in validation", "visa-label": "You must enter your card information.", "visa-name": "Name on card", "visa-mouth": "Month of expiry", "visa-Year": "Year of expiry", "visa-cart-number": "Card number", "visa-security-code": "Security code"}}, "popover": {"product-detail": {"title": "Others", "unit-price": "unit price", "quantity": "quantity", "amount": "Cost"}}, "tab-bar": {"scan": "Badge", "indirect-command": "Clients", "points": "My points", "account": "Account", "home": "Home", "purchases": "Purchases", "order": "Orders", "marketPlace": "Store", "order-retail": "Orders retail", "other": "Others", "orderHistory": "History", "reward-products": {"title": "Reward products", "available": "Available in", "countdown": {"days": "days", "hours": "hours", "minutes": "minutes", "seconds": "seconds"}, "deadline": "Deadline", "gifts": "Gifts", "point": "points", "empty": "No reward product available", "countdown-ended": "The countdown has ended! You can now claim your rewards."}}, "home-page-particular": {"title": "Cadyst Loyalty Program", "description": "Win many prizes by purchasing your flour in your mobile application", "button": "Start shopping", "product-description": "AMIGO Your only companion for making your exceptional donuts", "video-unvailable": "Your browser does not support the video.", "bag": "Bags", "welcome": "Welcome", "formats": "Amigo formats"}, "home-page": {"title": "Welcome", "country": "Cameroon", "product-title": "Best Sale", "product-sugestions": "Sugestions", "product-tendency": "Tendency", "offer-title": "Special offers", "see-all-button": "See all", "see-offer-button": "See offer ", "pub": {"cimencam": {"description": "Ready-mixed concrete", "do-order-button-label": "Order"}, "binastore": {"description": "Become a Binastore <PERSON>", "know-more-button": "READ MORE +"}}, "update-text": "To benefit from the latest features and security updates, please click the button below.", "uptodate": "UPDATE", "update-cancel": "CANCEL"}, "market-place": {"specical-section": "Special Mama Donuts", "see-more-button": "See more", "tendency": "tendency", "other-products": "Others products", "txt-made": "Made in Cameroun", "pay": "PAY", "reclaim": "RECLAMER", "fill-quantity": "Enter Quantity"}, "redirect-sabitou-modal": {"text": "The guide adapted to the Cameroonian context, for professionals and non-professionals, recommended by the ONIGC and the ONAC."}, "history-page": {"title": "My Orders", "purchase": "Purchase history", "startDate": "Start date", "endDate": "End date", "title-filter": "Filter by", "btn-filter": "Filter", "btn-reset": "Reset", "ref": "Reference", "tabs": {"in-progres": "In progress", "credit": "On credit", "prevalidate": "Prevalidated", "validate": "Validate", "Rejected": "Rejected"}, "reference": "Order N°", "qty": "Qty", "status": "Status", "distributor-name": "Distributor", "amount": "Cost", "empty-order": "You haven't order", "employee-name": "Name", "update-nber": "Number of modifications", "status-cancelled": "Cancellation Status", "cancellation": "Cancellation request", "client-name": "Client", "particular": "Particular Order"}, "all-orders-page": {"title": "Orders", "my-orders": "My Orders", "employees-orders": "Employees credit orders"}, "update-order": {"title": "Product", "packaging": "Packaging", "quantity": "Quantity in bag(s)", "unit-price": "Unit Price", "total-price": "Total Price"}, "retrievement-page": {"title": "Retrievements", "startDate": "Start date", "endDate": "End date", "title-filter": "Filter by", "btn-filter": "Filter", "btn-reset": "Reset", "ref": "Reference", "tabs": {"in-progres": "In progress", "validate": "Validate"}, "reference": "N°", "jde": " JDE N°", "ae": "Removal N°", "qtyReceved": "<PERSON><PERSON> recieved", "qtyOrdered": "<PERSON><PERSON> ordered", "qtyShipped": "Qty shipped", "product": "Product", "driverName": "DroverName", "back": "Cancel", "save": "Save", "Qty-recieved": "add quantity recieved (Tonne)", "primaryVehicleId": "Vehicle Id", "addElts": "add more informations", "imgLabel": "Insert retrievement picture", "editRef": "Edit retrievement", "empty-order": "You haven't retrievement"}, "refresher": {"refreshing": "Refreshing", "pull": "Pull to refresh"}, "reseller-new-page": {"bags": "bags", "first-step": {"select-region-label": "Select a region", "select-city-label": "Select a  city ", "select-distributors-label": "Select a Distributor", "select-packaging-label": "Select a packagins", "next-button-label": "Next", "enter-qdty": "Enter the quantity", "qdty-bag": "Quantity in bag", "qdty-tons": "Quanty in Tons(T)", "cancel": "Cancel", "valid": "Validate", "qdty-sheep": "Removed quantity"}, "second-step": {"title": "Select a product", "next-button-label": "Next"}, "third-step": {"title": "Summary of the order", "region": "Region", "city": "City", "distributors": "Distributor", "packaging": "Packaging", "product-order": "Product order", "label": "Enter your reference (optional)", "confirm the order": "confirm the order", "next-button-label": "ORDER"}, "history-page": {"title": "My Orders", "title-reseller": "Resellers Orders", "startDate": "Start date", "total-quantity": "Totals Quantities", "endDate": "End date", "points": "points", "seller": "<PERSON><PERSON>", "points-to-validate": "points to validate", "amount": "Amount", "title-filter": "Filter by", "btn-filter": "Filter", "btn-reset": "Reset", "ref": "Reference", "tabs": {"in-progres": "In progress", "validate": "Validated", "prevalidate": "Prevalidated", "rejected": "Rejected", "Waiting": "Waiting validation"}, "reference": "Order N°", "distributor-name": "Distributor", "empty-order": "You haven't order"}, "retrievement-page": {"title": "Retrievements", "startDate": "Start date", "endDate": "End date", "recap": "Details of the delivery ", "title-filter": "Filter by", "btn-filter": "Filter", "btn-reset": "Reset", "uploadText": "Take a picture", "ref": "Reference", "reference": "N°", "jde": "JDE N°", "ae": "Removal N°", "qtyReceved": "<PERSON><PERSON> recieved", "imgLabel": "Insert retrievement picture ", "qtyOrdered": "<PERSON><PERSON> ordered", "qtyShipped": "Quantity shipped", "product": "Product", "driverName": "DroverName", "back": "Cancel", "save": "Save", "primaryVehicleId": "Vehicle Id", "editRef": "Edit retrievement", "adds": "Add more informations", "empty-order": "You haven't retrievement"}, "detail": {"title": "Order Details", "reference": "Order N°", "created-at": "Creation date", "validated-at": "Validation date", "product-list": "List of products", "product-info-slide": "Slide to the right to see more", "order-recap": "Summary of the order ", "region": "Region", "city": "City", "distributors": "Distributor", "packaging": "Packaging", "point-unvalidate": "unvalidate", "point-validate": "Validated point", "bags": "bags", "congrat": "Your order has been sent for validation.", "produit": "Ordering product", "quantity": "Qty", "quantity-ship": "Qty shipped", "para": "Click on the products to modify their quantities", "modify": "Modify", "image": "Add an image", "piece-join": "Browse and select the files you want to upload from your device.", "upload-image": "Upload images", "valide": "Validated", "rejete": "Rejected", "UPLOAD-IMAGE": "UPLOAD A NEW PHOTO", "VIEW-IMAGE": "SEE PHOTOS", "valid-order": "VALID ORDER", "download-order": "DOWNLOAD THE ORDER FORM"}}, "account-balance-page": {"title": "My account", "description-balance": "Updated on", "at": "at", "creditLimit": "Credit limit available", "amountAvailable": "All removals already invoiced but not paid", "availableAmount": "Available balance", "bill-fail": "Invoice overdue", "outstanding-bill": "Accounting Balance", "others-bill": "Other debit notes", "open-order": "Opened Order", "payment": "Payments/Collections", "creditReturned": "Customer equity", "invoicedAmount": "Orders already invoiced", "openorderAmount": "Value of unbilled orders", "histogram": "Histogramme", "bill-fail-desc": "Unpaid invoices", "limit-credit-desc": "Maximum credit amount to use", "open-order-desc": "Orders not collected or collected but not invoiced", "others-bill-desc": "Other charges invoiced"}, "companie-account-page": {"title": "Company List", "btn-filter": "Filter", "empty-company": "No company found", "filter": {"title": "Filter", "name-label": "Name", "phone-label": "Phone Number", "region-label": "Commercial Region", "btn-submit": "Apply"}}, "company-detail-page": {"title": "Detail", "tabs-menu": {"information": "Information", "account": "Accounts", "address": "Addresses", "balance": "Balance"}, "empty-address": "No address found", "empty-account": "No account found", "information": {"name-label": "Company Name", "status-label": "Tax Status", "niu-label": "Taxpayer Number", "rccm-label": "Trade Registry", "phone-label": "Phone", "commercial-label": "Associated Salesperson", "regionCom-label": "Commercial Region", "region-label": "Region", "city-label": "City", "district-label": "District"}, "account": {"name-field": "Name:", "phone-field": "Phone Number:", "email-field": "Email:"}, "address": {"description": "Description:", "usine": "Factory:", "shipTo": "ShipTo ID:"}, "balance": {"title": "Account <PERSON><PERSON> on", "text": "Updated at", "limit-credit": "Credit Limit", "payment": "Payments/Receipts", "credit-returned": "Customer Credit", "invoiced-amount": "Invoiced Orders", "open-order-amount": "Uninvoiced Orders"}}, "reconnection": "Please log in again", "reprint-invoice-page": {"startDate": "Start date", "endDate": "End date", "tel": "Télephone", "title-filter": "Reprint invoice", "btn-filter": "<PERSON><PERSON>", "btn-reset": "Back"}, "jde-report-page": {"startDate": "Start date", "endDate": "End date", "title-filter": "Extract account", "btn-filter": "<PERSON><PERSON>", "btn-reset": "Back"}, "jde-loads-not-dispatched-page": {"startDate": "Start date", "endDate": "End date", "title-filter": "Loads not dispatch", "btn-filter": "<PERSON><PERSON>", "btn-reset": "Back"}, "reload-balance-page": {"title": "Account Recharge", "balance": "BALANCE AVAILABLE", "account-history": "Recharge history", "no-data-found": "No refills found", "make-recharge": "PERFORM A RECHARGE"}, "reload-balance-details": {"details": "Recharge details", "company-name": "Company name", "user-name": "Username", "transaction-id": "Transaction ID", "amount": "amount", "payment-mode": "Payment mode", "status": "status", "transaction-nbr": "Transaction number", "refill-date": "Recharge date", "close-btn": "CLOSE"}, "notification": {"your-notification": "Your notification", "notification-not-found": " Not found notification", "selected": "Selected"}, "removals": {"title": "Removal", "removal-name": "Name", "removal-status": " Status", "removal-date": "Date"}, "faq-page": {"title": "Frequently Asked Questions", "empty-faq": "No FAQ found"}, "contact-page": {"title": "Contacts", "call": "Call", "mail": "Email", "website": "Website", "message": "WhatsApp", "address": "Address"}, "item-details-page": {"product-information": "Product Information", "applications": "Applications", "benefits": "Benefits", "features": "Features", "others": "Others", "description": "Description", "reviews": "Reviews", "specifications": "Specifications", "camerounaise-description": "Standard range flour reserved for expert bakers and pastry chefs. Its flexible, malleable, and light dough offers opportunities for various applications that only artists know the secret to. Requires more technique and associated equipment.", "colombe-description": "Medium-range flour, it's the dream that feeds the desire for the baker's profession due to its level of security in production. Its baking strength and ability to resist various production imperfections make it the undisputed and infallible friend of bakers. Tolerates approximate technique and equipment.", "pelican-description": "Premium flour is a special and strong flour where all bakery and pastry recipes are possible. It offers a wide spectrum of tolerance and results in superior quality products. Tolerates approximate technique and equipment.", "amigo-description": "Special doughnut flour reserved for various doughnut applications. The lightness of its dough and its ability to produce many doughnuts make it an asset for users.", "default-description": "No description.", "amigo-applications": "Perfect for making various types of doughnuts, fritters, and other fried pastries.", "amigo-benefits": "Produces light and fluffy doughnuts with excellent oil absorption control.", "amigo-features": "Special blend of flours for optimal texture and taste in fried products.", "camerounaise-applications": "Ideal for artisanal bread making, pastries, and fine bakery products.", "camerounaise-benefits": "Produces bread with excellent crust and crumb structure.", "camerounaise-features": "High protein content for strong gluten development.", "colombe-applications": "Suitable for a wide range of bakery products, from bread to simple pastries.", "colombe-benefits": "Consistent results even with varying production conditions.", "colombe-features": "Balanced protein content for good dough stability.", "pelican-applications": "Perfect for high-end bakery and pastry products, including complex recipes.", "pelican-benefits": "Produces superior quality baked goods with excellent taste and texture.", "pelican-features": "Premium blend of flours with exceptional strength and stability.", "default-applications": "Applications not available for this product.", "default-benefits": "Benefits not available for this product.", "default-features": "Features not available for this product."}, "language": {"current": "(Current language)"}, "months": {"1": "january", "2": "february", "3": "march", "4": "april", "5": "may", "6": "june", "7": "july", "8": "august", "9": "september", "10": "october", "11": "november", "12": "december"}, "account-management": {"title": "Account management", "manager": "Manager", "status": "Online", "other": "Others accounts", "text": "Click on the arrows to change account", "title-account": "Select authentication account", "text-account": "If you have many accounts, please select an account", "btn": "Continue"}, "fidelity-page": {"title": "Fidelity", "points": "Validate points", "total-points": "Total cumulative points", "add-referral": "Add referral", "pts": "Pts", "my-points": "My points", "waiting": "Awaiting validation", "available-Gifts": "Available Gifts", "advantages-colombe": "Colombe advantages", "advantages-title": "Advantages", "description-title": "Description", "category": "Others categories", "no-gifts": "no gifts", "referral-list": "Referral list", "input-referral": "Enter the godchildren phone number", "see-more": "Learn more about sponsorship", "referal": "SPONSORSHIP", "sponsor-title": "Sponsor infos", "sponsor-desc": "You have been sponsored", "inactive-qr-code": "QR Code inactif", "isCheckingQrCode": "Checking QR Code"}, "indirect-user": {"title": "Indirects clients", "phone": "Phone", "address": "Address"}, "list-user": {"title": "Users list", "direct": "Directs clients", "indirect": "Indirects clients"}, "user-info": {"title": "User Information", "full-name": "Full Name:", "phone": "Phone:", "region": "Region:", "location": "Location:", "category": "Category:", "created-at": "Created On:", "enabled": "Enabled:", "company": "Company:", "particular": "Individual", "other": "Other", "yes": "Yes", "no": "No"}, "indirect-clients": {"title": "Indirect Clients", "name": "Full Name", "business-name": "Business Name", "phone": "Phone Number", "location": "Enter a precise address", "vendors": "Semi-wholesaler", "images": "Images", "order-new-page.planning-modal.save-button-label": "Save", "button.cancel": "Cancel", "reseller-new-page.first-step.select-region-label": "Select Region", "city": "Select city", "ville": "city", "Neighborhood": "Neighborhood", "enter-net": "set neighborhood", "Type-client": "Indirect client type", "delete": "Delete", "edit": "Edit", "save": "Save", "empty": "User not found", "localisation": "Localisation", "locations": "My locations", "reset": "Reset", "add": "Add user", "add-whole-sale": "Add whole saler", "whole-sale": "Whole sale", "whole-sale-success": "Whole sale successfully added", "whole-sale-info": "Whole sale information"}, "list-order": {"title": "List orders", "direct": "Directs orders", "indirect": "Indirects orders"}, "recap-scan": {"validate": "Validate", "supplier": "Supplier", "title": "Coupon Summary"}, "method-order": {"title": "Validate your points"}, "qr-orders": {"select-client": "Select indirect client", "text": "You are about to scan and automatically validate the client's points based on the following information:", "qr": "QR Code", "user-info": "USER INFORMATION", "scan": "Scan your purchase coupon", "title": "Purchased Bags", "scan-text": "Scan your purchase coupons and earn loyalty points", "text-button": "Scan your coupon"}}