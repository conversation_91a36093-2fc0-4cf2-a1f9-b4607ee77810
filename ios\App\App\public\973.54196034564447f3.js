"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[973],{12330:(b,C,r)=>{r.d(C,{f:()=>n});var g=r(73308),s=r(37222),t=r(2978),i=r(77897),m=r(28935),d=r(56610),f=r(74657);function h(e,_){if(1&e&&(t.j41(0,"ion-item",24)(1,"div"),t.EFF(2),t.k0s()()),2&e){const o=_.$implicit;t.R7$(2),t.JRh(o.label)}}function p(e,_){if(1&e&&(t.j41(0,"div")(1,"ion-list",5),t.DNE(2,h,3,1,"ion-item",23),t.k0s()()),2&e){const o=t.XpG();t.R7$(2),t.Y8G("ngForOf",o.dataName)}}function v(e,_){if(1&e){const o=t.RV6();t.j41(0,"ion-datetime",25,26),t.bIt("ionChange",function(){t.eBV(o);const a=t.sdS(1);return t.Njj(a.confirm(!0))}),t.k0s()}}function c(e,_){if(1&e){const o=t.RV6();t.j41(0,"ion-datetime",27,26),t.bIt("ionChange",function(){t.eBV(o);const a=t.sdS(1);return t.Njj(a.confirm(!0))}),t.k0s()}}let n=(()=>{class e{constructor(o,l){this.modalCtrl=o,this.wholeService=l}ngOnInit(){this.filterForm?.updateValueAndValidity(),this.isEdit=!1,this.filterForm=new s.gE({name:new s.MJ(this.filterData?.name||""),tel:new s.MJ(this.filterData?.tel||0),startDate:new s.MJ(this.filterData?.startDate||""),endDate:new s.MJ(this.filterData?.endDate||"")}),Array.isArray(this.filteredUsersNames)?this.dataName=[...this.filteredUsersNames]:(console.error("filteredUsersNames is not iterable:",this.filteredUsersNames),this.dataName=[])}handleChangeName(o){this.isEdit=!0;const l=o.target.value.toLowerCase();console.log(l)}closeModal(){var o=this;return(0,g.A)(function*(){o.modalCtrl.dismiss({...o.filterForm.value})})()}resetFilter(){this.modalCtrl.dismiss({})}static{this.\u0275fac=function(l){return new(l||e)(t.rXU(i.W3),t.rXU(m.G))}}static{this.\u0275cmp=t.VBU({type:e,selectors:[["app-filter-user"]],inputs:{filterData:"filterData",filteredUsersNames:"filteredUsersNames"},decls:51,vars:33,consts:[[1,"bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end"],["src","assets/icons/close.svg",3,"click"],["id","content",3,"formGroup"],[1,"filter-item"],["position","floating",1,"title"],["formControlName","name","type","text",3,"clearInput","value","input"],[4,"ngIf"],["formControlName","tel","type","tel",3,"clearInput","input"],[1,"form-group","padding-horizontal"],[1,"title"],[1,"mbottom"],[1,"date-time"],["slot","start","src","assets/icons/calendar.svg",1,"ion-text-center","ion-icon"],["id","date","placeholder","JJ/MM/AAAA",1,"ion-text-start","input-date",3,"value"],["trigger","date","size","cover","side","top","alignment","center"],["id","enddate","placeholder","JJ/MM/AAAA",1,"ion-text-start","input-date",3,"value"],["trigger","enddate","size","cover","side","top","alignment","center"],["expand","block","color","primary",1,"btn-submit",3,"disabled","click"],["name","search-sharp"],["expand","block","color","secondary",1,"btn-submit",3,"disabled","click"],["name","reset"],["lines","none",4,"ngFor","ngForOf"],["lines","none"],["formControlName","startDate","presentation","date","locale","fr-FR",3,"ionChange"],["popoverDatetime",""],["formControlName","endDate","presentation","date","locale","fr-FR",3,"ionChange"]],template:function(l,a){1&l&&(t.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2)(4,"ion-img",3),t.bIt("click",function(){return a.closeModal()}),t.k0s()(),t.j41(5,"ion-label"),t.EFF(6),t.nI1(7,"translate"),t.k0s()()(),t.j41(8,"ion-content")(9,"form",4)(10,"ion-list")(11,"ion-item",5)(12,"ion-label",6),t.EFF(13),t.nI1(14,"translate"),t.k0s(),t.j41(15,"ion-input",7),t.bIt("input",function(M){return a.handleChangeName(M)}),t.k0s()(),t.DNE(16,p,3,1,"div",8),t.j41(17,"ion-item",5)(18,"ion-label",6),t.EFF(19),t.nI1(20,"translate"),t.k0s(),t.j41(21,"ion-input",9),t.bIt("input",function(M){return a.handleChangeName(M)}),t.k0s()(),t.j41(22,"div",10)(23,"ion-label",11),t.EFF(24),t.nI1(25,"translate"),t.k0s(),t.j41(26,"div",12)(27,"ion-item",13),t.nrm(28,"ion-icon",14)(29,"ion-input",15),t.nI1(30,"date"),t.j41(31,"ion-popover",16),t.DNE(32,v,2,0,"ng-template"),t.k0s()()()(),t.j41(33,"div",10)(34,"ion-label",11),t.EFF(35),t.nI1(36,"translate"),t.k0s(),t.j41(37,"div",12)(38,"ion-item",13),t.nrm(39,"ion-icon",14)(40,"ion-input",17),t.nI1(41,"date"),t.j41(42,"ion-popover",18),t.DNE(43,c,2,0,"ng-template"),t.k0s()()()()(),t.j41(44,"ion-button",19),t.bIt("click",function(){return a.closeModal()}),t.nrm(45,"ion-icon",20),t.EFF(46),t.nI1(47,"translate"),t.k0s(),t.j41(48,"ion-button",21),t.bIt("click",function(){return a.resetFilter()}),t.nrm(49,"ion-icon",22),t.EFF(50," Reset "),t.k0s()()()()),2&l&&(t.R7$(6),t.JRh(t.bMT(7,15,"companie-account-page.filter.title")),t.R7$(3),t.Y8G("formGroup",a.filterForm),t.R7$(4),t.SpI(" ",t.bMT(14,17,"companie-account-page.filter.name-label")," "),t.R7$(2),t.Y8G("clearInput",!0)("value",a.filterForm.value.name),t.R7$(1),t.Y8G("ngIf",a.isEdit&&a.dataName.length>0),t.R7$(3),t.SpI(" ",t.bMT(20,19,"companie-account-page.filter.phone-label")," "),t.R7$(2),t.Y8G("clearInput",!0),t.R7$(3),t.SpI("",t.bMT(25,21,"history-page.startDate")," "),t.R7$(5),t.FS9("value",t.i5U(30,23,a.filterForm.get("startDate").value,"dd/MM/yyyy")),t.R7$(6),t.SpI("",t.bMT(36,26,"history-page.endDate")," "),t.R7$(5),t.FS9("value",t.i5U(41,28,a.filterForm.get("endDate").value,"dd/MM/yyyy")),t.R7$(4),t.Y8G("disabled",a.filterForm.invalid),t.R7$(2),t.SpI(" ",t.bMT(47,31,"companie-account-page.filter.btn-submit")," "),t.R7$(2),t.Y8G("disabled",a.filterForm.invalid))},dependencies:[s.qT,s.BC,s.cb,i.Jm,i.W9,i.A9,i.eU,i.iq,i.KW,i.$w,i.uz,i.he,i.nf,i.Zx,i.ai,i.CF,i.Je,i.Gw,d.Sq,d.bT,s.j4,s.JD,d.vh,f.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;color:#1e1e1e}#content[_ngcontent-%COMP%]{color:#000;height:100%;padding:calc(41 * var(--res)) 0}#content[_ngcontent-%COMP%]   .filter-item[_ngcontent-%COMP%]{max-width:95%;margin-bottom:calc(31.25 * var(--res))}#content[_ngcontent-%COMP%]   .filter-item[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(40 * var(--res));margin-bottom:calc(15 * var(--res))}#content[_ngcontent-%COMP%]   .filter-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]::part(icon){margin-bottom:calc(37.5 * var(--res))}#content[_ngcontent-%COMP%]   .btn-submit[_ngcontent-%COMP%]{margin:calc(50 * var(--res));--padding-top: calc(60 * var(--res));--padding-bottom: calc(60 * var(--res))}#content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(40 * var(--res));margin-bottom:calc(15 * var(--res));margin-left:1.3em}#content[_ngcontent-%COMP%]   .ion-icon[_ngcontent-%COMP%]{margin-left:-18px}#content[_ngcontent-%COMP%]   .input-date[_ngcontent-%COMP%]{margin-left:1rem}#content[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}#content[_ngcontent-%COMP%]   .no-mbottom[_ngcontent-%COMP%]{margin-bottom:0}#content[_ngcontent-%COMP%]   .mbottom[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res));width:100%}"]})}}return e})()},94440:(b,C,r)=>{r.d(C,{c:()=>s});var g=r(2978);let s=(()=>{class t{transform(m,...d){return m?m.length>d[0]?`${m.substring(0,d[0]-3)}...`:m:""}static{this.\u0275fac=function(d){return new(d||t)}}static{this.\u0275pipe=g.EJ8({name:"truncateString",type:t,pure:!0})}}return t})()},28935:(b,C,r)=>{r.d(C,{G:()=>h});var g=r(73308),s=r(26409),t=r(45312),i=r(56610),m=r(2978),d=r(82571),f=r(33607);let h=(()=>{class p{constructor(c,n,e){this.commonSrv=c,this.baseUrlService=n,this.http=e,this.url="",this.getWholeSaleBoolean=!1,this.url=this.baseUrlService.getOrigin()+t.c.basePath}createWholeSale(c){var n=this;return(0,g.A)(function*(){try{const e=yield n.http.post(`${n.url}whole-sale`,c).toPromise();return n.commonSrv.showToast({color:"success",message:"Demi gros cre\xe9 avec succ\xe8s"}),e}catch(e){const o={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(o),e}})()}getWholeSale(c){var n=this;return(0,g.A)(function*(){try{let e=new s.Nl;const{offset:_,limit:o,startDate:l,endDate:a,tel:u,name:M,commercialRegion:O,animateDonutId:P}=c;return l&&a&&(e=e.append("startDate",new i.vh("fr").transform(l,"YYYY-MM-dd")),e=e.append("endDate",new i.vh("fr").transform(a,"YYYY-MM-dd"))),_&&(e=e.append("offset",_)),o&&(e=e.append("limit",o)),M&&(e=e.append("firstName",M)),O&&(e=e.append("address.commercialRegion",O)),P&&(e=e.append("associatedDonutAnimator.id",P)),u&&(e=e.append("tel",u)),e=e.append("enable",!0),yield n.http.get(`${n.url}whole-sale`,{params:e}).toPromise()}catch(e){const o={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(o),e}})()}find(c){var n=this;return(0,g.A)(function*(){try{return yield n.http.get(`${n.url}whole-sale/${c}`).toPromise()}catch(e){const o={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(o),null}})()}updateWholeSale(c){var n=this;return(0,g.A)(function*(){try{const e=yield n.http.patch(`${n.url}whole-sale/${c._id}`,c).toPromise();return n.commonSrv.showToast({color:"success",message:"Demi gros modifi\xe9 avec succ\xe8s"}),e}catch(e){const o={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(o),e}})()}static{this.\u0275fac=function(n){return new(n||p)(m.KVO(d.h),m.KVO(f.K),m.KVO(s.Qq))}}static{this.\u0275prov=m.jDH({token:p,factory:p.\u0275fac,providedIn:"root"})}}return p})()}}]);