"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2037],{92037:(x,P,a)=>{a.r(P),a.d(P,{CreateWholeSalePageModule:()=>w});var d=a(56610),l=a(37222),c=a(77897),b=a(77575),p=a(73308),n=a(2978),M=a(74657),k=a(82571),h=a(28935),I=a(5141),g=a(99987),o=a(71333);function t(r,_){1&r&&n.nrm(0,"app-progress-spinner")}function u(r,_){if(1&r&&(n.j41(0,"ion-select-option",21),n.EFF(1),n.k0s()),2&r){const e=_.$implicit;n.Y8G("value",e),n.R7$(1),n.SpI(" ",e," ")}}function m(r,_){if(1&r&&(n.j41(0,"ion-select-option",21),n.EFF(1),n.k0s()),2&r){const e=_.$implicit;n.Y8G("value",e),n.R7$(1),n.SpI(" ",e," ")}}function O(r,_){1&r&&n.nrm(0,"ion-spinner",22)}const v=[{path:"",component:(()=>{class r{constructor(){this.commonSrv=(0,n.WQX)(k.h),this.translateSrv=(0,n.WQX)(M.c$),this.fb=(0,n.WQX)(l.ok),this.location=(0,n.WQX)(d.aZ),this.wholeService=(0,n.WQX)(h.G),this.route=(0,n.WQX)(b.Ix)}ngOnInit(){this.wholeSale=this.wholeService.wholeSaleDetail,this.initForm(),this.regions=this.commonSrv.getRegions(),this.wholeSale&&this.patchForm(this.wholeSale)}onSubmit(){var e=this;return(0,p.A)(function*(){e.isLoading=!0;try{if(e.wholeSale)e.wholeSale={_id:e.wholeSale._id,name:e.form.value.name??e.wholeSale.name,tel:e.form.value.tel??e.form.value.tel,address:{district:e.form.value.district??e.wholeSale.address?.district,city:e.form.value.city??e.wholeSale.address?.city,region:e.form.value.region??e.wholeSale.address?.region,commercialRegion:e.getCommercialRegion(e.form.value.region)??e.wholeSale.address?.commercialRegion},associatedDonutAnimator:e.wholeSale.associatedDonutAnimator},yield e.wholeService.updateWholeSale(e.wholeSale),e.route.navigateByUrl("/navigation/indirect-user"),e.isLoading=!0,e.wholeService.wholeSaleDetail=null,e.form.reset();else{const i={name:e.form.value.name,address:{district:e.form.value.district,city:e.form.value.city,region:e.form.value.region,commercialRegion:e.getCommercialRegion(e.form.value.region)},tel:""===e.form.value.tel?null:e.form.value.tel};if(i.tel&&!e.commonSrv.verifyPhoneNumber(i.tel))return e.commonSrv.showToast({color:"danger",message:e.translateSrv.currentLang===g.T.English?"Phone nu;ber is not correct":"Le num\xe9ro de t\xe9l\xe9phone est incorrect"});201==(yield e.wholeService.createWholeSale(i))?.status&&(e.wholeService.getWholeSaleBoolean=!0,e.route.navigateByUrl("/navigation/indirect-user"),e.isLoading=!1,e.form.reset())}}catch{}finally{e.isLoading=!1}})()}initForm(){this.form=this.fb.group({name:["",l.k0.required],tel:[""],region:["",l.k0.required],city:["",l.k0.required],district:[""]})}getCommercialRegion(e){const i=I.MJ,s=e.trim().toLowerCase();for(const[f,C]of Object.entries(i))if(console.log("Checking key:",f,"with regions:",C),C.some(T=>T.trim().toLowerCase()===s))return console.log("Match found:",f),f;return console.log("No match found"),null}patchForm(e){this.form.patchValue({name:e.name,tel:e.tel??null,region:e.address?.region,city:e.address?.city,district:e.address?.district})}getRegion(e){var i=this;return(0,p.A)(function*(){i.cities=i.commonSrv.getCities(e.detail.value)})()}getcities(e){}back(){this.wholeService.getWholeSaleBoolean&&(this.wholeService.getWholeSaleBoolean=!1),this.wholeService.wholeSaleDetail=null,this.form.reset(),this.location.back()}static{this.\u0275fac=function(i){return new(i||r)}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-create-whole-sale"]],decls:51,vars:35,consts:[[4,"ngIf"],[3,"translucent"],[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"buttons"],[1,"transparent"],["name","search"],["name","funnel-outline"],[3,"fullscreen"],[3,"formGroup","ngSubmit"],["position","floating"],["formControlName","name","clearInput",""],["type","number","clearInput","","formControlName","tel"],["mode","ios","formControlName","region","interface","action-sheet",3,"cancelText","ionChange"],["stores",""],[3,"value",4,"ngFor","ngForOf"],["mode","ios","formControlName","city","interface","action-sheet",3,"cancelText","ionChange"],["type","text","clearInput","","formControlName","district"],[1,"btn-validate"],["type","submit","color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled"],["name","bubbles",4,"ngIf"],[3,"value"],["name","bubbles"]],template:function(i,s){1&i&&(n.DNE(0,t,1,0,"app-progress-spinner",0),n.j41(1,"ion-header",1)(2,"ion-toolbar",2)(3,"ion-img",3),n.bIt("click",function(){return s.back()}),n.k0s(),n.j41(4,"ion-title"),n.EFF(5),n.nI1(6,"translate"),n.k0s()(),n.j41(7,"div",4)(8,"ion-button",5),n.nrm(9,"ion-icon",6),n.k0s(),n.j41(10,"ion-button",5),n.nrm(11,"ion-icon",7),n.k0s()()(),n.j41(12,"ion-content",8)(13,"form",9),n.bIt("ngSubmit",function(){return s.onSubmit()}),n.j41(14,"ion-item")(15,"ion-label",10),n.EFF(16),n.nI1(17,"translate"),n.k0s(),n.nrm(18,"ion-input",11),n.k0s(),n.j41(19,"ion-item")(20,"ion-label",10),n.EFF(21),n.nI1(22,"translate"),n.k0s(),n.nrm(23,"ion-input",12),n.k0s(),n.j41(24,"ion-item")(25,"ion-label",10),n.EFF(26),n.nI1(27,"translate"),n.k0s(),n.j41(28,"ion-select",13,14),n.bIt("ionChange",function(C){return s.getRegion(C)}),n.nI1(30,"translate"),n.DNE(31,u,2,2,"ion-select-option",15),n.k0s()(),n.j41(32,"ion-item")(33,"ion-label",10),n.EFF(34),n.nI1(35,"translate"),n.k0s(),n.j41(36,"ion-select",16,14),n.bIt("ionChange",function(C){return s.getcities(C)}),n.nI1(38,"translate"),n.DNE(39,m,2,2,"ion-select-option",15),n.k0s()(),n.j41(40,"ion-item")(41,"ion-label",10),n.EFF(42),n.nI1(43,"translate"),n.k0s(),n.nrm(44,"ion-input",17),n.k0s(),n.j41(45,"div",18)(46,"ion-button",19)(47,"ion-label"),n.EFF(48),n.nI1(49,"translate"),n.k0s(),n.DNE(50,O,1,0,"ion-spinner",20),n.k0s()()()()),2&i&&(n.Y8G("ngIf",s.isLoading),n.R7$(1),n.Y8G("translucent",!0),n.R7$(4),n.JRh(n.bMT(6,17,"indirect-clients.whole-sale")),n.R7$(7),n.Y8G("fullscreen",!0),n.R7$(1),n.Y8G("formGroup",s.form),n.R7$(3),n.JRh(n.bMT(17,19,"indirect-clients.name")),n.R7$(5),n.JRh(n.bMT(22,21,"indirect-clients.phone")),n.R7$(5),n.JRh(n.bMT(27,23,"indirect-clients.reseller-new-page.first-step.select-region-label")),n.R7$(2),n.FS9("cancelText",n.bMT(30,25,"indirect-clients.button.cancel")),n.R7$(3),n.Y8G("ngForOf",s.regions),n.R7$(3),n.JRh(n.bMT(35,27,"indirect-clients.city")),n.R7$(2),n.FS9("cancelText",n.bMT(38,29,"indirect-clients.button.cancel")),n.R7$(3),n.Y8G("ngForOf",s.cities),n.R7$(3),n.JRh(n.bMT(43,31,"indirect-clients.location")),n.R7$(4),n.Y8G("disabled",!s.form.valid),n.R7$(2),n.JRh(n.bMT(49,33,"indirect-clients.save")),n.R7$(2),n.Y8G("ngIf",s.isLoading))},dependencies:[d.Sq,d.bT,l.qT,l.BC,l.cb,c.Jm,c.W9,c.eU,c.iq,c.KW,c.$w,c.uz,c.he,c.Nm,c.Ip,c.w2,c.BC,c.ai,c.su,c.Je,c.Gw,l.j4,l.JD,o._,M.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{display:flex;padding-top:13px;align-items:center}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700!important;margin:auto}ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res));color:#0b305c}.header[_ngcontent-%COMP%]{--background: #f1f2f4;--border-color: transparent;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.buttons[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent !important;--box-shadow: none;color:#0b305c;border:none;--padding: auto}.buttons[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#419cfb}ion-content[_ngcontent-%COMP%]{--background: #f1f2f4}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{width:calc(100% - 48px);margin:2em auto auto;padding:auto;--background: #f1f2f4}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:1rem;color:#0b305c;font-family:var(--mont-regular)}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0px;margin-bottom:calc(41 * var(--res));--background: $color-nineteen;--ripple-color: transparent;--background-activated: transparent;--background-activated-opacity: transparent;--background-focused: transparent;--background-focused-opacity: transparent;--background-hover: transparent;--background-hover-opacity: transparent}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:var(--mont-regular);color:#0b305c;font-size:1rem}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#fff}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .autocomplete-list[_ngcontent-%COMP%]{width:100%;background:#f1f2f4}ion-text[_ngcontent-%COMP%]{font-size:1rem}.submit[_ngcontent-%COMP%]{width:calc(100% - 48px);margin-left:24px}.add[_ngcontent-%COMP%]{font-size:.6rem;font-weight:700;--color: #0b305c;--background: #419cfb2b;--padding: 5px 10px;--border-radius: 30px;margin:1rem auto}.iconImage[_ngcontent-%COMP%]{display:flex;width:calc(100% - 48px);margin-bottom:1rem}.iconImage[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{border:1.12px solid #d5dfeb;border-radius:6px;padding:.5rem}.iconImage[_ngcontent-%COMP%]   .image.localisation[_ngcontent-%COMP%]{position:relative}.iconImage[_ngcontent-%COMP%]   .image.localisation[_ngcontent-%COMP%]   .has-image[_ngcontent-%COMP%]{color:var(--ion-color-primary);text-decoration:underline;cursor:pointer}.iconImage[_ngcontent-%COMP%]   .image.localisation[_ngcontent-%COMP%]   .preview-container[_ngcontent-%COMP%]{position:fixed;inset:0;background:rgba(0,0,0,.8);z-index:999;display:flex;justify-content:center;align-items:center}.iconImage[_ngcontent-%COMP%]   .image.localisation[_ngcontent-%COMP%]   .preview-container[_ngcontent-%COMP%]   .preview-image[_ngcontent-%COMP%]{max-width:90%;max-height:90vh;object-fit:contain}.iconImage[_ngcontent-%COMP%]   .localisation[_ngcontent-%COMP%]{margin-left:.2rem}"]})}}return r})()}];let y=(()=>{class r{static{this.\u0275fac=function(i){return new(i||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[b.iI.forChild(v),b.iI]})}}return r})();var S=a(93887);let w=(()=>{class r{static{this.\u0275fac=function(i){return new(i||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[d.MD,l.YN,c.bv,M.h,l.X1,y,S.G]})}}return r})()},28935:(x,P,a)=>{a.d(P,{G:()=>k});var d=a(73308),l=a(26409),c=a(45312),b=a(56610),p=a(2978),n=a(82571),M=a(33607);let k=(()=>{class h{constructor(g,o,t){this.commonSrv=g,this.baseUrlService=o,this.http=t,this.url="",this.getWholeSaleBoolean=!1,this.url=this.baseUrlService.getOrigin()+c.c.basePath}createWholeSale(g){var o=this;return(0,d.A)(function*(){try{const t=yield o.http.post(`${o.url}whole-sale`,g).toPromise();return o.commonSrv.showToast({color:"success",message:"Demi gros cre\xe9 avec succ\xe8s"}),t}catch(t){const m={message:o.commonSrv.getError("",t).message,color:"danger"};return yield o.commonSrv.showToast(m),t}})()}getWholeSale(g){var o=this;return(0,d.A)(function*(){try{let t=new l.Nl;const{offset:u,limit:m,startDate:O,endDate:R,tel:v,name:y,commercialRegion:S,animateDonutId:w}=g;return O&&R&&(t=t.append("startDate",new b.vh("fr").transform(O,"YYYY-MM-dd")),t=t.append("endDate",new b.vh("fr").transform(R,"YYYY-MM-dd"))),u&&(t=t.append("offset",u)),m&&(t=t.append("limit",m)),y&&(t=t.append("firstName",y)),S&&(t=t.append("address.commercialRegion",S)),w&&(t=t.append("associatedDonutAnimator._id",w)),v&&(t=t.append("tel",v)),t=t.append("enable",!0),yield o.http.get(`${o.url}whole-sale`,{params:t}).toPromise()}catch(t){const m={message:o.commonSrv.getError("",t).message,color:"danger"};return yield o.commonSrv.showToast(m),t}})()}find(g){var o=this;return(0,d.A)(function*(){try{return yield o.http.get(`${o.url}whole-sale/${g}`).toPromise()}catch(t){const m={message:o.commonSrv.getError("",t).message,color:"danger"};return yield o.commonSrv.showToast(m),null}})()}updateWholeSale(g){var o=this;return(0,d.A)(function*(){try{const t=yield o.http.patch(`${o.url}whole-sale/${g._id}`,g).toPromise();return o.commonSrv.showToast({color:"success",message:"Demi gros modifi\xe9 avec succ\xe8s"}),t}catch(t){const m={message:o.commonSrv.getError("",t).message,color:"danger"};return yield o.commonSrv.showToast(m),t}})()}static{this.\u0275fac=function(o){return new(o||h)(p.KVO(n.h),p.KVO(M.K),p.KVO(l.Qq))}}static{this.\u0275prov=p.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})()}}]);