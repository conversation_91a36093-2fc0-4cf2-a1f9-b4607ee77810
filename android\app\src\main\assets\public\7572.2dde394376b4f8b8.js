"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7572],{17572:(A,y,s)=>{s.r(y),s.d(y,{ChoiceSuppliersPageModule:()=>x});var _=s(56610),g=s(37222),u=s(77897),f=s(77575),p=s(73308),h=s(58133),e=s(2978),D=s(14599),C=s(82571),M=s(43556),O=s(93387),a=s(95908),o=s(39316),t=s(62049),i=s(28935),n=s(71333),d=s(94440),v=s(74657);function b(m,U){1&m&&e.nrm(0,"app-progress-spinner")}function E(m,U){if(1&m){const r=e.RV6();e.j41(0,"div",16),e.bIt("click",function(l){const S=e.eBV(r).$implicit;return e.XpG(2).selectSupplier(S),e.Njj(l.stopPropagation())}),e.EFF(1),e.k0s()}if(2&m){const r=U.$implicit;e.R7$(1),e.SpI(" ",r.name," ")}}function T(m,U){if(1&m&&(e.j41(0,"div",14),e.DNE(1,E,2,1,"div",15),e.k0s()),2&m){const r=e.XpG();e.R7$(1),e.Y8G("ngForOf",r.filteredSuppliers)}}function I(m,U){if(1&m){const r=e.RV6();e.j41(0,"ion-card",17),e.bIt("click",function(){const P=e.eBV(r).$implicit,S=e.XpG();return e.Njj(S.nextStep(P))}),e.j41(1,"div",6)(2,"div",7),e.nrm(3,"ion-img",8),e.k0s(),e.j41(4,"div",18)(5,"label",19),e.EFF(6),e.nI1(7,"truncateString"),e.k0s(),e.j41(8,"p"),e.EFF(9),e.nI1(10,"truncateString"),e.k0s(),e.j41(11,"ion-button",20),e.EFF(12),e.nI1(13,"translate"),e.k0s()(),e.j41(14,"div",21),e.nrm(15,"ion-icon",22),e.k0s()()()}if(2&m){const r=U.$implicit,c=e.XpG();e.R7$(6),e.JRh(e.i5U(7,3,null==r?null:r.name,20)),e.R7$(3),e.JRh(e.i5U(10,6,null==c.commonService||null==c.commonService.user||null==c.commonService.user.address?null:c.commonService.user.address.region,28)),e.R7$(3),e.SpI(" ",e.bMT(13,9,"order-new-page.first-step.choose")," ")}}const L=[{path:"",component:(()=>{class m{constructor(r,c,l,P,S,K,$,W,j,F){this.router=r,this.location=c,this.storageSrv=l,this.commonService=P,this.companySrv=S,this.priceSrv=K,this.packagingSrv=$,this.productSrv=W,this.translateService=j,this.wholeSaleService=F,this.isLoading=!0,this.isSelectedCompany=!1,this.filteredSuppliers=[],this.showSuggestions=!1,this.offset=0,this.limit=20,this.skeletons=[1,2,3,4,5,6],this.filterData={name:""},this.otherSupplier={id:"otherId",name:"Autre grossiste"},this.filterForm=new g.gE({name:new g.MJ("")}),this.user=this.storageSrv.getUserConnected()}ngOnInit(){var r=this;return(0,p.A)(function*(){r.isLoading=!0;try{const c=[r.getDistributorLoyalityProgram(),r.getSuppliers()];r.user.category!==h.s.Commercial&&c.push(r.getStore()),yield Promise.all(c)}catch(c){console.error("Error initializing component:",c)}finally{r.isLoading=!1}})()}getSuppliers(){var r=this;return(0,p.A)(function*(){r.skeletons=[1,2,3,4,5,6],r.isLoading=!0;const c={...r.filterData,offset:r.offset,limit:r.limit},l=yield r.wholeSaleService.getWholeSale({...c,commercialRegion:r.commonService.user?.address?.commercialRegion}),P=r.commonService.user?.associatedSuppliers;if(P&&Array.isArray(P)&&P.length>0)r.suppliers=P;else{const S=yield r.companySrv.getParticularCompanies(c);r.suppliers=S.data,r.suppliers.unshift(r.otherSupplier),r.offset=r.offset+r.limit}r.suppliers.unshift(...l.data),console.log("suppliers",r.suppliers,P,l.data),r.isLoading=!1,r.skeletons=[]})()}getDistributorLoyalityProgram(){var r=this;return(0,p.A)(function*(){r.isLoading=!0;const c={...r.filterData,offset:r.offset,limit:r.limit,isLoyaltyProgDistributor:!0},l=yield r.companySrv.getCompanies(c);r.loyaltyProgramDistributor=l.data,r.filteredSuppliers=[],r.offset=r.offset+r.limit,r.isLoading=!1})()}selectCompany(r){this.selectedCompany=r,this.isSelectedCompany=!0}ionViewWillEnter(){return(0,p.A)(function*(){})()}getStore(){var r=this;return(0,p.A)(function*(){r.isLoading=!0;const c={companyId:r.companySrv.selectedCompanyForSalesOrderProcess?._id},l=yield r.priceSrv.getStores(c);l&&(r.initData=l,r.storageSrv.store("stores",JSON.stringify(l))),r.isLoading=!1})()}nextStep(r){var c=this;return(0,p.A)(function*(){const l=c.filterForm.value.name?.trim();if(l&&(!r||r.name!==l)){r={name:l,address:{district:c.commonService.user.address.district,city:c.commonService.user.address.city,region:c.commonService.user.address.region,commercialRegion:c.commonService.getCommercialRegion(c.commonService.user.address.region)},tel:null};try{c.wholeSaleService.createWholeSale(r)}catch(S){c.commonService.showToast({message:S.error?.message||"Error creating new company",color:"danger"})}}r||(r={...c.otherSupplier});const P={company:r._id,items:[],amount:null};yield c.storageSrv.store("cart",JSON.stringify(P)),yield c.storageSrv.store("supplier",JSON.stringify(r)),c.router.navigate(["order/recap-scan"]),c.productSrv.currentDataProductScan=[]})()}filterSuppliers(r){const c=r.target.value.toLowerCase();this.filterForm.patchValue({name:c}),""===c.trim()?this.showSuggestions=!1:(this.filteredSuppliers=this.loyaltyProgramDistributor.filter(l=>l.name.toLowerCase().includes(c)),this.showSuggestions=!0)}selectSupplier(r){this.selectedSupplier=r,this.filterForm.patchValue({name:r.name}),this.filteredSuppliers=[],this.showSuggestions=!1}onDocumentClick(r){this.showSuggestions=!1}back(){this.location.back()}static{this.\u0275fac=function(c){return new(c||m)(e.rXU(f.Ix),e.rXU(_.aZ),e.rXU(D.n),e.rXU(C.h),e.rXU(M.B),e.rXU(O.A),e.rXU(a.L),e.rXU(o.b),e.rXU(t.E),e.rXU(i.G))}}static{this.\u0275cmp=e.VBU({type:m,selectors:[["app-choice-suppliers"]],hostBindings:function(c,l){1&c&&e.bIt("click",function(S){return l.onDocumentClick(S)},!1,e.EBC)},decls:20,vars:14,consts:[[4,"ngIf"],[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"title"],["id","container"],[1,"custom-card",3,"formGroup"],[1,"container"],[1,"image"],["src","../../../../assets/icons/man.png"],[1,"company","relative"],["formControlName","name","type","text",3,"clearInput","placeholder","input","focus"],["class","autocomplete-list",4,"ngIf"],["expand","block",3,"click"],["class","custom-card",3,"click",4,"ngFor","ngForOf"],[1,"autocomplete-list"],["class","autocomplete-item",3,"click",4,"ngFor","ngForOf"],[1,"autocomplete-item",3,"click"],[1,"custom-card",3,"click"],[1,"company"],["for",""],["expand","block"],[1,"chevron"],["slot","end","name","chevron-forward-outline","color","primary"]],template:function(c,l){1&c&&(e.DNE(0,b,1,0,"app-progress-spinner",0),e.j41(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-img",2),e.bIt("click",function(){return l.back()}),e.k0s(),e.j41(4,"ion-title",3),e.EFF(5),e.nI1(6,"translate"),e.k0s()()(),e.j41(7,"div",4)(8,"ion-card",5)(9,"div",6)(10,"div",7),e.nrm(11,"ion-img",8),e.k0s(),e.j41(12,"div",9)(13,"ion-input",10),e.bIt("input",function(S){return l.filterSuppliers(S)})("focus",function(){return l.showSuggestions=!0}),e.nI1(14,"translate"),e.DNE(15,T,2,1,"div",11),e.k0s(),e.j41(16,"ion-button",12),e.bIt("click",function(){return l.nextStep(l.selectedSupplier||l.otherSupplier)}),e.EFF(17),e.nI1(18,"translate"),e.k0s()()()(),e.DNE(19,I,16,11,"ion-card",13),e.k0s()),2&c&&(e.Y8G("ngIf",l.isLoading),e.R7$(5),e.JRh(e.bMT(6,8,"order-new-page.first-step.choose-store")),e.R7$(3),e.Y8G("formGroup",l.filterForm),e.R7$(5),e.FS9("placeholder",e.bMT(14,10,"order-new-page.first-step.supplier")),e.Y8G("clearInput",!0),e.R7$(2),e.Y8G("ngIf",l.filteredSuppliers.length>0&&l.showSuggestions),e.R7$(2),e.SpI(" ",e.bMT(18,12,"order-new-page.first-step.choose")," "),e.R7$(2),e.Y8G("ngForOf",l.suppliers))},dependencies:[_.Sq,_.bT,g.BC,g.cb,u.Jm,u.b_,u.eU,u.iq,u.KW,u.$w,u.BC,u.ai,u.Gw,n._,g.j4,g.JD,d.c,v.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding-inline:calc(41 * var(--res));margin:1rem;--border-color: transparent;--background: transparent;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(46 * var(--res));text-align:start;margin-bottom:0!important;color:var(--clr-primary-700);font-family:var(--mont-regular)}#container[_ngcontent-%COMP%]{padding:calc(41 * var(--res));overflow:auto;height:100%}.title[_ngcontent-%COMP%]{text-align:center;color:#143c5d;font-size:22px}.custom-card[_ngcontent-%COMP%]{margin:12px 0;border:1px solid #dcdcdc;border-radius:8px}.custom-card[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:126px;padding:calc(41 * var(--res))}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center;gap:1em;padding:1em}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;border:2px solid #419CFB;border-radius:50%;padding:10px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:43px;width:43px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5em;align-items:baseline;color:#143c5d;width:75%}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:20px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{color:#fff;border-radius:4px;width:100%;background-color:#143c5d}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .chevron[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{height:20px;width:20px}ion-segment[_ngcontent-%COMP%]{--indicator-color: #0078d4;--background: transparent}ion-segment-button[checked][_ngcontent-%COMP%]{background-color:#0078d4;color:#fff}ion-input[_ngcontent-%COMP%]{font-family:Mont Regular;position:relative;--padding-start: 0;font-size:calc(36 * var(--res));border-bottom:1px solid rgb(92,92,92)}.relative[_ngcontent-%COMP%]{position:relative}.autocomplete-list[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;right:0;border:1px solid #ccc;height:auto!important;overflow-y:auto;background:white;z-index:1;border-radius:4px;border:10px;box-shadow:0 4px 6px #0000001a}.autocomplete-item[_ngcontent-%COMP%]{padding:8px;cursor:pointer}.autocomplete-item[_ngcontent-%COMP%]:hover{background-color:#f0f0f0}"]})}}return m})()}];let R=(()=>{class m{static{this.\u0275fac=function(c){return new(c||m)}}static{this.\u0275mod=e.$C({type:m})}static{this.\u0275inj=e.G2t({imports:[f.iI.forChild(L),f.iI]})}}return m})();var w=s(93887);let x=(()=>{class m{static{this.\u0275fac=function(c){return new(c||m)}}static{this.\u0275mod=e.$C({type:m})}static{this.\u0275inj=e.G2t({imports:[_.MD,g.YN,u.bv,w.G,v.h,g.X1,g.YN,R]})}}return m})()},43556:(A,y,s)=>{s.d(y,{B:()=>O});var _=s(73308),g=s(94934),u=s(45312),f=s(26409),h=(s(99987),s(2978)),e=s(33607),D=s(82571),C=s(14599),M=s(74657);let O=(()=>{class a{constructor(t,i,n,d,v){this.baseUrl=t,this.http=i,this.commonSrv=n,this.storageSrv=d,this.translateService=v,this.base_url=`${this.baseUrl.getOrigin()}${u.c.basePath}`,this.base_url+="companies"}create(t){var i=this;return(0,_.A)(function*(){try{return delete t._id,yield(0,g.s)(i.http.post(i.base_url,t))}catch(n){return i.commonSrv.getError("Echec de cr\xe9ation de la compagnie",n)}})()}getCompanies(t){var i=this;return(0,_.A)(function*(){try{let n=new f.Nl;const{category:d,city:v,limit:b,name:E,regionCom:T,solToId:I,tel:B,users:L,offset:R,enable:w=!0,projection:x,isLoyaltyProgDistributor:m}=t;return void 0!==d&&(n=n.append("category",d)),v&&(n=n.append("address.city",v)),E&&(n=n.append("name",E)),I&&(n=n.append("erpSoldToId",I)),B&&(n=n.append("tel",`${B}`)),x&&(n=n.append("projection",`${x}`)),L&&(n=n.append("users",`${L}`)),T&&(n=n.append("address.commercialRegion",T)),m&&(n=n.append("isLoyaltyProgDistributor",m)),void 0!==b&&(n=n.append("limit",b)),void 0!==R&&(n=n.append("offset",R)),n=n.set("enable",w),yield(0,g.s)(i.http.get(i.base_url,{params:n}))}catch(n){const v={message:i.commonSrv.getError("",n).message,color:"danger"};return yield i.commonSrv.showToast(v),n}})()}getParticularCompanies(t){var i=this;return(0,_.A)(function*(){let n=new f.Nl;const{limit:d,offset:v,enable:b=!0,commercialRegion:E}=t;return void 0!==d&&(n=n.append("limit",d)),void 0!==v&&(n=n.append("offset",v)),E&&(n=n.append("address.commercialRegion",E)),n=n.set("enable",b),yield(0,g.s)(i.http.get(i.base_url+"/particular-suppliers",{params:n}))})()}find(t){var i=this;return(0,_.A)(function*(){try{return yield(0,g.s)(i.http.get(i.base_url+"/"+t))}catch{return i.commonSrv.initCompany()}})()}getBalance(t){var i=this;return(0,_.A)(function*(){try{let n=new f.Nl;const{company:d}=i.storageSrv.getUserConnected();return n=n.set("_id",d?d?._id:t?.companyId),yield(0,g.s)(i.http.get(`${i.base_url}/balance`,{params:n}))}catch(n){return yield i.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de votre solde",color:"danger"}),n}})()}getUsersCompany(t,i){var n=this;return(0,_.A)(function*(){try{let d=new f.Nl;const{email:v,enable:b=!0}=i;return v&&(d=d.append("email",v)),d=d.append("enable",b),yield(0,g.s)(n.http.get(`${n.base_url}/${t}/users`,{params:d}))}catch(d){return yield n.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de vos informations",color:"danger"}),d}})()}static{this.\u0275fac=function(i){return new(i||a)(h.KVO(e.K),h.KVO(f.Qq),h.KVO(D.h),h.KVO(C.n),h.KVO(M.c$))}}static{this.\u0275prov=h.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})()},93387:(A,y,s)=>{s.d(y,{A:()=>C});var _=s(73308),g=s(26409),u=s(94934),f=s(58133),p=s(45312),h=s(2978),e=s(82571),D=s(33607);let C=(()=>{class M{constructor(a,o,t){this.http=a,this.commonSrv=o,this.baseUrlService=t,this.url=this.baseUrlService.getOrigin()+p.c.basePath+"prices"}getStores(a){var o=this;return(0,_.A)(function*(){try{let t=new g.Nl;const{companyId:i}=a;return i&&(t=t.append("company._id",i)),t=t.append("enable",!0),yield(0,u.s)(o.http.get(`${o.url}/${o.commonSrv.user.category===f.s.Commercial?"stores-sales":"stores"}`,{params:t}))}catch(t){const n={message:o.commonSrv.getError("",t).message,color:"danger"};return yield o.commonSrv.showToast(n),t}})()}getPricesForOrder(a){var o=this;return(0,_.A)(function*(){try{let t=new g.Nl;const{store:i,packaging:n,companyId:d,enable:v=!0}=a;return t=t.append("enable",v),i&&(t=t.append("store",i)),n&&(t=t.append("packaging",n)),d&&(t=t.append("company._id",d)),yield(0,u.s)(o.http.get(`${o.url}/${o.commonSrv.user.category===f.s.Commercial?"products-sales":"products"}`,{headers:{Authorization:`Bearer ${o.commonSrv?.user?.accessToken}`},params:t}))}catch(t){const n={message:o.commonSrv.getError("",t).message,color:"danger"};return yield o.commonSrv.showToast(n),t}})()}getPricesForCurrentOrder(a){var o=this;return(0,_.A)(function*(){try{let t=new g.Nl;const{storeId:i,packagingId:n,userId:d,enable:v=!0}=a;return t=t.append("enable",v),i&&(t=t.append("store",i)),n&&(t=t.append("packaging",n)),d&&(t=t.append("user",d)),yield(0,u.s)(o.http.get(`${o.url}/products-change`,{params:t}))}catch(t){return t}})()}getShippingAddress(a){var o=this;return(0,_.A)(function*(){try{let t=new g.Nl;const{offset:i,limit:n,enable:d=!0}=a;return i&&(t=t.append("offset",i)),n&&(t=t.append("limit",n)),t=t.append("enable",d),yield(0,u.s)(o.http.get(`${o.url}`,{params:t}))}catch(t){return t}})()}static{this.\u0275fac=function(o){return new(o||M)(h.KVO(g.Qq),h.KVO(e.h),h.KVO(D.K))}}static{this.\u0275prov=h.jDH({token:M,factory:M.\u0275fac,providedIn:"root"})}}return M})()},39316:(A,y,s)=>{s.d(y,{b:()=>C});var _=s(73308),g=s(26409),u=s(94934),f=s(45312),p=s(2978),h=s(82571),e=s(33607),D=s(77897);let C=(()=>{class M{constructor(a,o,t,i){this.http=a,this.commonSrv=o,this.baseUrlService=t,this.toastController=i,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+f.c.basePath+"products"}getProducts(a){var o=this;return(0,_.A)(function*(){try{let t=new g.Nl;return a?.limit&&(t=t.append("limit",a?.limit)),yield(0,u.s)(o.http.get(o.url,{params:t}))}catch(t){const n={message:o.commonSrv.getError("",t).message,color:"danger"};return yield o.commonSrv.showToast(n),t}})()}getProduct(a){var o=this;return(0,_.A)(function*(){try{return yield(0,u.s)(o.http.get(`${o.url}/${a}`))}catch(t){const n={message:o.commonSrv.getError("",t).message,color:"danger"};return yield o.commonSrv.showToast(n),t}})()}static{this.\u0275fac=function(o){return new(o||M)(p.KVO(g.Qq),p.KVO(h.h),p.KVO(e.K),p.KVO(D.K_))}}static{this.\u0275prov=p.jDH({token:M,factory:M.\u0275fac,providedIn:"root"})}}return M})()},94440:(A,y,s)=>{s.d(y,{c:()=>g});var _=s(2978);let g=(()=>{class u{transform(p,...h){return p?p.length>h[0]?`${p.substring(0,h[0]-3)}...`:p:""}static{this.\u0275fac=function(h){return new(h||u)}}static{this.\u0275pipe=_.EJ8({name:"truncateString",type:u,pure:!0})}}return u})()},28935:(A,y,s)=>{s.d(y,{G:()=>D});var _=s(73308),g=s(26409),u=s(45312),f=s(56610),p=s(2978),h=s(82571),e=s(33607);let D=(()=>{class C{constructor(O,a,o){this.commonSrv=O,this.baseUrlService=a,this.http=o,this.url="",this.getWholeSaleBoolean=!1,this.url=this.baseUrlService.getOrigin()+u.c.basePath}createWholeSale(O){var a=this;return(0,_.A)(function*(){try{const o=yield a.http.post(`${a.url}whole-sale`,O).toPromise();return a.commonSrv.showToast({color:"success",message:"Demi gros cre\xe9 avec succ\xe8s"}),o}catch(o){const i={message:a.commonSrv.getError("",o).message,color:"danger"};return yield a.commonSrv.showToast(i),o}})()}getWholeSale(O){var a=this;return(0,_.A)(function*(){try{let o=new g.Nl;const{offset:t,limit:i,startDate:n,endDate:d,tel:v,name:b,commercialRegion:E,animateDonutId:T}=O;return n&&d&&(o=o.append("startDate",new f.vh("fr").transform(n,"YYYY-MM-dd")),o=o.append("endDate",new f.vh("fr").transform(d,"YYYY-MM-dd"))),t&&(o=o.append("offset",t)),i&&(o=o.append("limit",i)),b&&(o=o.append("firstName",b)),E&&(o=o.append("address.commercialRegion",E)),T&&(o=o.append("associatedDonutAnimator._id",T)),v&&(o=o.append("tel",v)),o=o.append("enable",!0),yield a.http.get(`${a.url}whole-sale`,{params:o}).toPromise()}catch(o){const i={message:a.commonSrv.getError("",o).message,color:"danger"};return yield a.commonSrv.showToast(i),o}})()}find(O){var a=this;return(0,_.A)(function*(){try{return yield a.http.get(`${a.url}whole-sale/${O}`).toPromise()}catch(o){const i={message:a.commonSrv.getError("",o).message,color:"danger"};return yield a.commonSrv.showToast(i),null}})()}updateWholeSale(O){var a=this;return(0,_.A)(function*(){try{const o=yield a.http.patch(`${a.url}whole-sale/${O._id}`,O).toPromise();return a.commonSrv.showToast({color:"success",message:"Demi gros modifi\xe9 avec succ\xe8s"}),o}catch(o){const i={message:a.commonSrv.getError("",o).message,color:"danger"};return yield a.commonSrv.showToast(i),o}})()}static{this.\u0275fac=function(a){return new(a||C)(p.KVO(h.h),p.KVO(e.K),p.KVO(g.Qq))}}static{this.\u0275prov=p.jDH({token:C,factory:C.\u0275fac,providedIn:"root"})}}return C})()}}]);