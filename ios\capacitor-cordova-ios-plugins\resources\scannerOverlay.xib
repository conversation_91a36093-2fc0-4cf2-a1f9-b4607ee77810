<?xml version="1.0" encoding="UTF-8"?>
<archive type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="8.00">
	<data>
		<int key="IBDocument.SystemTarget">1280</int>
		<string key="IBDocument.SystemVersion">11C74</string>
		<string key="IBDocument.InterfaceBuilderVersion">1938</string>
		<string key="IBDocument.AppKitVersion">1138.23</string>
		<string key="IBDocument.HIToolboxVersion">567.00</string>
		<object class="NSMutableDictionary" key="IBDocument.PluginVersions">
			<string key="NS.key.0">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
			<string key="NS.object.0">933</string>
		</object>
		<array key="IBDocument.IntegratedClassDependencies">
			<string>IBUINavigationItem</string>
			<string>IBUIBarButtonItem</string>
			<string>IBUIView</string>
			<string>IBUINavigationBar</string>
			<string>IBProxyObject</string>
		</array>
		<array key="IBDocument.PluginDependencies">
			<string>com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
		</array>
		<object class="NSMutableDictionary" key="IBDocument.Metadata">
			<string key="NS.key.0">PluginDependencyRecalculationVersion</string>
			<integer value="1" key="NS.object.0"/>
		</object>
		<array class="NSMutableArray" key="IBDocument.RootObjects" id="1000">
			<object class="IBProxyObject" id="372490531">
				<string key="IBProxiedObjectIdentifier">IBFilesOwner</string>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
			<object class="IBProxyObject" id="975951072">
				<string key="IBProxiedObjectIdentifier">IBFirstResponder</string>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
			<object class="IBUIView" id="191373211">
				<reference key="NSNextResponder"/>
				<int key="NSvFlags">274</int>
				<array class="NSMutableArray" key="NSSubviews">
					<object class="IBUINavigationBar" id="1064216609">
						<reference key="NSNextResponder" ref="191373211"/>
						<int key="NSvFlags">290</int>
						<string key="NSFrameSize">{320, 44}</string>
						<reference key="NSSuperview" ref="191373211"/>
						<reference key="NSWindow"/>
						<reference key="NSNextKeyView"/>
						<string key="NSReuseIdentifierKey">_NS:260</string>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
						<int key="IBUIBarStyle">1</int>
						<array class="NSMutableArray" key="IBUIItems">
							<object class="IBUINavigationItem" id="240626599">
								<reference key="IBUINavigationBar" ref="1064216609"/>
								<string key="IBUITitle">Barcode Scanner</string>
								<object class="IBUIBarButtonItem" key="IBUILeftBarButtonItem" id="1053701234">
									<string key="IBUITitle">Cancel</string>
									<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
									<int key="IBUIStyle">1</int>
									<reference key="IBUINavigationItem" ref="240626599"/>
								</object>
								<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
							</object>
						</array>
					</object>
				</array>
				<string key="NSFrameSize">{320, 460}</string>
				<reference key="NSSuperview"/>
				<reference key="NSWindow"/>
				<reference key="NSNextKeyView" ref="1064216609"/>
				<object class="NSColor" key="IBUIBackgroundColor">
					<int key="NSColorSpace">3</int>
					<bytes key="NSWhite">MSAwAA</bytes>
					<object class="NSColorSpace" key="NSCustomColorSpace">
						<int key="NSID">2</int>
					</object>
				</object>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
		</array>
		<object class="IBObjectContainer" key="IBDocument.Objects">
			<array class="NSMutableArray" key="connectionRecords">
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">overlayView</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="191373211"/>
					</object>
					<int key="connectionID">9</int>
				</object>
			</array>
			<object class="IBMutableOrderedSet" key="objectRecords">
				<array key="orderedObjects">
					<object class="IBObjectRecord">
						<int key="objectID">0</int>
						<array key="object" id="0"/>
						<reference key="children" ref="1000"/>
						<nil key="parent"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">1</int>
						<reference key="object" ref="191373211"/>
						<array class="NSMutableArray" key="children">
							<reference ref="1064216609"/>
						</array>
						<reference key="parent" ref="0"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-1</int>
						<reference key="object" ref="372490531"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">File's Owner</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-2</int>
						<reference key="object" ref="975951072"/>
						<reference key="parent" ref="0"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">3</int>
						<reference key="object" ref="1064216609"/>
						<array class="NSMutableArray" key="children">
							<reference ref="240626599"/>
						</array>
						<reference key="parent" ref="191373211"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">4</int>
						<reference key="object" ref="240626599"/>
						<array class="NSMutableArray" key="children">
							<reference ref="1053701234"/>
						</array>
						<reference key="parent" ref="1064216609"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">10</int>
						<reference key="object" ref="1053701234"/>
						<reference key="parent" ref="240626599"/>
					</object>
				</array>
			</object>
			<dictionary class="NSMutableDictionary" key="flattenedProperties">
				<string key="-1.CustomClassName">PGbcsViewController</string>
				<string key="-1.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="-2.CustomClassName">UIResponder</string>
				<string key="-2.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="1.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="10.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="3.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="4.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
			</dictionary>
			<dictionary class="NSMutableDictionary" key="unlocalizedProperties"/>
			<nil key="activeLocalization"/>
			<dictionary class="NSMutableDictionary" key="localizations"/>
			<nil key="sourceID"/>
			<int key="maxID">11</int>
		</object>
		<object class="IBClassDescriber" key="IBDocument.Classes">
			<array class="NSMutableArray" key="referencedPartialClassDescriptions">
				<object class="IBPartialClassDescription">
					<string key="className">PGbcsViewController</string>
					<string key="superclassName">UIViewController</string>
					<object class="NSMutableDictionary" key="outlets">
						<string key="NS.key.0">overlayView</string>
						<string key="NS.object.0">UIView</string>
					</object>
					<object class="NSMutableDictionary" key="toOneOutletInfosByName">
						<string key="NS.key.0">overlayView</string>
						<object class="IBToOneOutletInfo" key="NS.object.0">
							<string key="name">overlayView</string>
							<string key="candidateClassName">UIView</string>
						</object>
					</object>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBProjectSource</string>
						<string key="minorKey">./Classes/PGbcsViewController.h</string>
					</object>
				</object>
			</array>
		</object>
		<int key="IBDocument.localizationMode">0</int>
		<string key="IBDocument.TargetRuntimeIdentifier">IBCocoaTouchFramework</string>
		<bool key="IBDocument.PluginDeclaredDependenciesTrackSystemTargetVersion">YES</bool>
		<int key="IBDocument.defaultPropertyAccessControl">3</int>
		<string key="IBCocoaTouchPluginVersion">933</string>
	</data>
</archive>
