"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2391],{22391:(S,v,a)=>{a.r(v),a.d(v,{IndirectUserPageModule:()=>X});var M=a(37222),e=a(77897),u=a(56610),f=a(77575),g=a(73308),n=a(2978),b=a(74657),y=a(91285),P=a(99987),w=a(12330),O=a(58133),k=a(23985),d=a(82571),I=a(28935),l=a(94440),_=a(51591);const s=["popover"];function x(r,p){1&r&&n.nrm(0,"ion-progress-bar",33)}function F(r,p){if(1&r){const t=n.RV6();n.j41(0,"div",34)(1,"ion-searchbar",35),n.bIt("ngModelChange",function(i){n.eBV(t);const c=n.XpG();return n.Njj(c.searchQuery=i)})("ngModelChange",function(){n.eBV(t);const i=n.XpG();return n.Njj(i.filterUsers())}),n.k0s()()}if(2&r){const t=n.XpG();n.R7$(1),n.Y8G("ngModel",t.searchQuery)}}function T(r,p){1&r&&(n.j41(0,"ion-cart")(1,"ion-thumbnail",36),n.nrm(2,"ion-skeleton-text",37),n.k0s()()),2&r&&(n.R7$(2),n.Y8G("animated",!0))}const j=function(r){return{"empty-state":r}},D=function(r,p){return[r,p]},E=function(r){return{"background-color":r}};function B(r,p){if(1&r){const t=n.RV6();n.j41(0,"div",40)(1,"ion-label",41)(2,"h2"),n.EFF(3),n.nI1(4,"truncateString"),n.k0s(),n.j41(5,"span"),n.EFF(6),n.nI1(7,"translate"),n.k0s(),n.j41(8,"span"),n.EFF(9),n.nI1(10,"translate"),n.k0s()(),n.j41(11,"span",42),n.nI1(12,"loyaltyProgramClassColor"),n.EFF(13),n.nI1(14,"loyaltyProgramLevelLabel"),n.k0s(),n.j41(15,"div",43)(16,"ion-img",44),n.bIt("click",function(i){const m=n.eBV(t).$implicit,h=n.XpG(2);return n.Njj(h.presentPopover(i,m))}),n.k0s()()()}if(2&r){const t=p.$implicit,o=n.XpG(2);let i;n.Y8G("ngClass",n.eq3(21,j,0===o.filteredUsers.length)),n.R7$(1),n.Y8G("routerLink",n.l_i(23,D,null!=o.wholeService&&o.wholeService.getWholeSaleBoolean?"/navigation/indirect-user/whole-sale-detail":"/navigation/indirect-user/detail",null==t?null:t._id)),n.R7$(2),n.JRh(n.i5U(4,10,(null==t?null:t.firstName)||(null==t?null:t.name),11)),n.R7$(3),n.Lme("",n.bMT(7,13,"indirect-user.phone")," : ",null==t?null:t.tel,""),n.R7$(3),n.E5c("",n.bMT(10,15,"indirect-user.address")," : ",null==t||null==t.address?null:t.address.region," ",null==t||null==t.address?null:t.address.city,""),n.R7$(2),n.Y8G("ngStyle",n.eq3(26,E,n.bMT(12,17,null!==(i=null==t||null==t.points?null:t.points.status)&&void 0!==i?i:1))),n.R7$(2),n.JRh(n.bMT(14,19,null==t||null==t.points?null:t.points.status))}}function $(r,p){if(1&r&&(n.j41(0,"div",38),n.DNE(1,B,17,28,"div",39),n.k0s()),2&r){const t=n.XpG();n.R7$(1),n.Y8G("ngForOf",t.filteredUsers)}}function z(r,p){1&r&&(n.j41(0,"div",45),n.nrm(1,"ion-img",46),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()),2&r&&(n.R7$(3),n.JRh(n.bMT(4,1,"indirect-clients.empty")))}function G(r,p){if(1&r){const t=n.RV6();n.j41(0,"ion-item",48),n.bIt("click",function(){n.eBV(t);const i=n.XpG(2);return n.Njj(i.localisation())}),n.nrm(1,"ion-icon",54),n.j41(2,"ion-label",52),n.EFF(3),n.nI1(4,"translate"),n.k0s()()}2&r&&(n.R7$(3),n.JRh(n.bMT(4,1,"indirect-clients.localisation")))}function L(r,p){if(1&r){const t=n.RV6();n.j41(0,"ion-list",47)(1,"ion-item",48),n.bIt("click",function(){n.eBV(t);const i=n.XpG();return n.Njj(i.openModalDeletingUser())}),n.nrm(2,"ion-icon",49),n.j41(3,"ion-label",50),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"ion-item",48),n.bIt("click",function(){n.eBV(t);const i=n.XpG();return n.Njj(i.editProspect())}),n.nrm(7,"ion-icon",51),n.j41(8,"ion-label",52),n.EFF(9),n.nI1(10,"translate"),n.k0s()(),n.DNE(11,G,5,3,"ion-item",53),n.k0s()}if(2&r){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,3,"indirect-clients.delete")),n.R7$(5),n.JRh(n.bMT(10,5,"indirect-clients.edit")),n.R7$(2),n.Y8G("ngIf",null==t.currentUser?null:t.currentUser.localisation)}}const R=function(r){return{active:r}};let A=(()=>{class r{constructor(t,o,i){this.userSrv=t,this.commonSrv=o,this.wholeService=i,this.location=(0,n.WQX)(u.aZ),this.translateService=(0,n.WQX)(b.c$),this.modalCtrl=(0,n.WQX)(e.W3),this.router=(0,n.WQX)(f.Ix),this.isOpen=!1,this.users=[],this.wholeSale=[],this.skeletons=[1,2,3,4,5,6],this.filterData={category:0,limit:50,offset:0},this.filteredUsers=[],this.searchQuery="",this.isSearchVisible=!1,this.isMenuOpen={},this.userCategory=O.s,this.totalUsers=0}toggleSearch(){this.isSearchVisible=!this.isSearchVisible}ngOnInit(){var t=this;return(0,g.A)(function*(){t.isLoading=!0,yield t.getUsers(),t.router.events.subscribe(o=>{o instanceof f.wF&&"/navigation/indirect-user"===o.url&&t.getUsers()})})()}reset(){this.filterData.commercialRegion=null,this.ngOnInit()}presentPopover(t,o){this.popover?(this.popover.event=t,this.isOpen=!0,this.currentUser=o):console.error("Popover is not defined")}editProspect(){var t=this;return(0,g.A)(function*(){t.popover&&(yield t.popover.dismiss(),t.isOpen=!1),t.wholeService.getWholeSaleBoolean?t.wholeService.wholeSaleDetail=t.currentUser:t.userSrv.currentUserParticular=t.currentUser,t.router.navigate([t.wholeService.getWholeSaleBoolean?"/navigation/manage-user/create-whole-sale":"/navigation/manage-user/create-indirect-user"])})()}onAddUser(){var t=this;return(0,g.A)(function*(){t.userSrv.currentUserParticular=null,t.router.navigate(["/navigation/manage-user/create-indirect-user"])})()}OnAddWholeSale(){var t=this;return(0,g.A)(function*(){t.router.navigate(["/navigation/manage-user/create-whole-sale"])})()}getUserCommercial(){var t=this;return(0,g.A)(function*(){t.filterData.commercialRegion=t.commonSrv.user?.address.commercialRegion,t.ngOnInit()})()}localisation(){var t=this;return(0,g.A)(function*(){t.popover&&(yield t.popover.dismiss(),t.isOpen=!1),t.userSrv.currentUserParticular=t.currentUser,t.router.navigate(["/navigation/indirect-user/location-view"])})()}localisations(){var t=this;return(0,g.A)(function*(){t.isLoading=!0;const o=t.commonSrv.user?.address.commercialRegion,i={...t.filterData,commercialRegion:o};try{const h=((yield t.userSrv.getUsers(i)).data||[]).filter(C=>C.localisation&&C?.localisation?.latitude&&C?.localisation?.longitude).map(C=>({latitude:C?.localisation?.latitude,longitude:C?.localisation?.longitude}));t.userSrv.currentUserLocalisation=h,t.router.navigate(["/navigation/indirect-user/location-view"])}catch(c){throw yield t.commonSrv.showToast({color:"danger",message:"Erreur lors de la r\xe9cup\xe9ration des utilisateurs"+c?.error?.message}),c}finally{t.isLoading=!1}})()}preventAlert(){var t=this;return(0,g.A)(function*(){t.isOpen=!1})()}back(){[O.s.DonutAnimator,O.s.Particular].includes(this.commonSrv.user?.category)?this.router.navigate(["/navigation/home-alt"]):this.router.navigate(["/navigation/home"])}openModalDeletingUser(){var t=this;return(0,g.A)(function*(){try{t.popover&&(yield t.popover.dismiss(),t.isOpen=!1);const o=yield t.modalCtrl.create({component:y.y,cssClass:"modalClass",componentProps:{dataModal:{confirmButton:t.translateService.currentLang===P.T.French?"Supprimer":"Delete",cancelButton:t.translateService.currentLang===P.T.French?"Annuler":"Cancel",text:t.translateService.currentLang===P.T.French?`Vous \xeates sur le point de supprimer le compte ${t.currentUser?.firstName} dans l'application Clic Cadyst.\nConfirmez-vous cette action ?`:`You are about to delete the account ${t.currentUser?.firstName} from the Clic Cadyst application.\nDo you confirm this action?`,cssClass:"custom-loading",handler:(c=(0,g.A)(function*(){return yield t.deleteCurrentUser(t.currentUser)}),function(){return c.apply(this,arguments)})}}});yield o.present();const{role:i}=yield o.onDidDismiss();console.log("Modal dismissed with role:",i)}catch(o){console.error("Error opening delete user modal:",o)}finally{t.isOpen=!1}var c})()}deleteCurrentUser(t){var o=this;return(0,g.A)(function*(){try{t.enable=!1,yield o.userSrv.updateUserParticular(t),yield o.getUsers()}catch{}})()}showFilter(){var t=this;return(0,g.A)(function*(){const o=yield t.modalCtrl.create({component:w.f,initialBreakpoint:.7,cssClass:"modal",breakpoints:[0,.5,.7,1],mode:"ios",componentProps:{filterData:t.filterData,filteredUsersNames:t.filteredUsersNames}});o.present(),t.filterData=(yield o.onWillDismiss()).data,console.log("filerData:",t.filterData),t.filterData&&(t.users=[],t.filterData.offset=0,yield t.getUsers())})()}getUsers(){var t=this;return(0,g.A)(function*(){try{if(t.skeletons=[1,2,3,4,5,6,7,8],t.wholeService.getWholeSaleBoolean){const o={...t.filterData,animateDonutId:t.commonSrv?.user?._id};t.filterData.commercialRegion=t.commonSrv.user.address?.commercialRegion;const i=yield t.wholeService.getWholeSale(o),c=yield t.wholeService.getWholeSale(t.filterData);t.wholeSale=0!==i?.count?i?.data:c?.data,t.filteredUsers=[...t.wholeSale],t.totalUsers=i?.count||0}else{const o=t.commonSrv.user?._id,i={...t.filterData,associatedCommercialId:o},c=yield t.userSrv.getUsers(i);t.users=c?.data||[],t.filteredUsers=[...t.users],t.totalUsers=c?.count||0}t.isLoading=!1,t.skeletons=[]}catch(o){console.error("Error fetching users:",o),t.users=[],t.filteredUsers=[],t.isLoading=!1,t.skeletons=[]}})()}filterUsers(){if(!this.searchQuery)return void(this.filteredUsers=this.wholeService.getWholeSaleBoolean?[...this.wholeSale]:[...this.users]);let t=[];t=this.wholeService.getWholeSaleBoolean?this.wholeSale:this.users;const o=this.searchQuery.toLowerCase().split("");this.filteredUsers=t.filter(i=>{const c=i.firstName.toLowerCase(),m=String(i.tel);return o.every(h=>c.includes(h)||m.includes(h))})}doRefresh(t){var o=this;return(0,g.A)(function*(){o.filterData={category:0,limit:50,offset:0},o.isLoading=!0,yield o.getUsers(),t?.target?.complete instanceof Function&&t.target.complete()})()}getFlowUsers(t){var o=this;return(0,g.A)(function*(){const i=o.filterData.offset+o.filterData.limit+1;o.filterData.offset=i<o.totalUsers&&!o.totalUsers?i:o.totalUsers,yield o.getUsers(),t.target.complete()})()}static{this.\u0275fac=function(o){return new(o||r)(n.rXU(k.D),n.rXU(d.h),n.rXU(I.G))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-indirect-user"]],viewQuery:function(o,i){if(1&o&&n.GBs(s,5),2&o){let c;n.mGM(c=n.lsd())&&(i.popover=c.first)}},decls:71,vars:41,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",1,"img",3,"click"],[1,"buttons"],[1,"transparent",3,"click"],["name","search"],["name","funnel-outline"],[3,"fullscreen"],[1,"tab-container","ion-no-border"],[3,"ngClass","click"],[1,"title"],["type","indeterminate",4,"ngIf"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],["class","search",4,"ngIf"],[4,"ngFor","ngForOf"],["class","container",4,"ngIf"],["class","empty-list",4,"ngIf"],[3,"isOpen","didDismiss"],["popover",""],["vertical","bottom","horizontal","start","slot","fixed"],["name","menu"],["side","top"],[1,"fab-content",3,"click"],[1,"elts"],["color","secondary",3,"click"],["name","location-outline"],[1,"fab-tooltip"],["color","danger",3,"click"],["name","refresh-outline"],["color","warning",3,"click"],["name","add"],["color","primary",3,"click"],[3,"ionInfinite"],["type","indeterminate"],[1,"search"],[3,"ngModel","ngModelChange"],[1,"skeleton"],[3,"animated"],[1,"container"],["class","item",3,"ngClass",4,"ngFor","ngForOf"],[1,"item",3,"ngClass"],[3,"routerLink"],[1,"function",3,"ngStyle"],[1,"icon-container"],["src","/assets/icons/info.svg",3,"click"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"],[1,"popover-list"],["button","",3,"click"],["slot","start","name","trash","color","danger"],["color","danger"],["slot","start","name","pencil","color","success"],["color","success"],["button","",3,"click",4,"ngIf"],["slot","start","name","home-outline","color","success",1,"blue"]],template:function(o,i){1&o&&(n.j41(0,"ion-header")(1,"div",0)(2,"ion-img",1),n.bIt("click",function(){return i.back()}),n.k0s(),n.j41(3,"ion-title"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"div",2)(7,"ion-button",3),n.bIt("click",function(){return i.toggleSearch()}),n.nrm(8,"ion-icon",4),n.k0s(),n.j41(9,"ion-button",3),n.bIt("click",function(){return i.showFilter()}),n.nrm(10,"ion-icon",5),n.k0s()()(),n.j41(11,"ion-content",6)(12,"ion-tab-bar",7)(13,"ion-tab-button",8),n.bIt("click",function(m){return i.wholeService.getWholeSaleBoolean=!1,i.doRefresh(m)}),n.j41(14,"ion-title",9),n.EFF(15),n.nI1(16,"translate"),n.k0s()(),n.j41(17,"ion-tab-button",8),n.bIt("click",function(m){return i.wholeService.getWholeSaleBoolean=!0,i.doRefresh(m)}),n.j41(18,"ion-title",9),n.EFF(19),n.nI1(20,"translate"),n.k0s()()(),n.DNE(21,x,1,0,"ion-progress-bar",10),n.j41(22,"ion-refresher",11),n.bIt("ionRefresh",function(m){return i.doRefresh(m)}),n.nrm(23,"ion-refresher-content",12),n.nI1(24,"translate"),n.nI1(25,"translate"),n.k0s(),n.DNE(26,F,2,1,"div",13),n.DNE(27,T,3,1,"ion-cart",14),n.DNE(28,$,2,1,"div",15),n.DNE(29,z,5,3,"div",16),n.j41(30,"ion-popover",17,18),n.bIt("didDismiss",function(){return i.isOpen=!1}),n.DNE(32,L,12,7,"ng-template"),n.k0s(),n.j41(33,"ion-fab",19)(34,"ion-fab-button"),n.nrm(35,"ion-icon",20),n.k0s(),n.j41(36,"ion-fab-list",21)(37,"div",22),n.bIt("click",function(){return i.localisations()}),n.j41(38,"div",23)(39,"ion-fab-button",24),n.bIt("click",function(){return i.localisations()}),n.nrm(40,"ion-icon",25),n.k0s()(),n.j41(41,"div",23)(42,"h2",26),n.EFF(43),n.nI1(44,"translate"),n.k0s()()(),n.j41(45,"div",22),n.bIt("click",function(){return i.reset()}),n.j41(46,"div",23)(47,"ion-fab-button",27),n.bIt("click",function(){return i.reset()}),n.nrm(48,"ion-icon",28),n.k0s()(),n.j41(49,"div",23)(50,"h2",26),n.EFF(51),n.nI1(52,"translate"),n.k0s()()(),n.j41(53,"div",22),n.bIt("click",function(){return i.OnAddWholeSale()}),n.j41(54,"div",23)(55,"ion-fab-button",29),n.bIt("click",function(){return i.OnAddWholeSale()}),n.nrm(56,"ion-icon",30),n.k0s()(),n.j41(57,"div",23)(58,"h2",26),n.EFF(59),n.nI1(60,"translate"),n.k0s()()(),n.j41(61,"div",22),n.bIt("click",function(){return i.onAddUser()}),n.j41(62,"div",23)(63,"ion-fab-button",31),n.bIt("click",function(){return i.onAddUser()}),n.nrm(64,"ion-icon",30),n.k0s()(),n.j41(65,"div",23)(66,"h2"),n.EFF(67),n.nI1(68,"translate"),n.k0s()()()()(),n.j41(69,"ion-infinite-scroll",32),n.bIt("ionInfinite",function(m){return i.getFlowUsers(m)}),n.nrm(70,"ion-infinite-scroll-content"),n.k0s()()),2&o&&(n.R7$(4),n.Lme("",n.bMT(5,19,"indirect-user.title")," (",i.totalUsers,")"),n.R7$(7),n.Y8G("fullscreen",!0),n.R7$(2),n.Y8G("ngClass",n.eq3(37,R,!i.wholeService.getWholeSaleBoolean)),n.R7$(2),n.SpI("",n.bMT(16,21,"indirect-user.title")," "),n.R7$(2),n.Y8G("ngClass",n.eq3(39,R,i.wholeService.getWholeSaleBoolean)),n.R7$(2),n.SpI(" ",n.bMT(20,23,"indirect-clients.whole-sale")," "),n.R7$(2),n.Y8G("ngIf",i.isLoading),n.R7$(2),n.FS9("pullingText",n.bMT(24,25,"refresher.pull")),n.Mz_("refreshingText","",n.bMT(25,27,"refresher.refreshing"),"..."),n.R7$(3),n.Y8G("ngIf",i.isSearchVisible),n.R7$(1),n.Y8G("ngForOf",i.skeletons),n.R7$(1),n.Y8G("ngIf",!i.isLoading),n.R7$(1),n.Y8G("ngIf",!i.isLoading&&0===i.filteredUsers.length),n.R7$(1),n.Y8G("isOpen",i.isOpen),n.R7$(13),n.JRh(n.bMT(44,29,"indirect-clients.locations")),n.R7$(8),n.JRh(n.bMT(52,31,"indirect-clients.reset")),n.R7$(8),n.JRh(n.bMT(60,33,"indirect-clients.add-whole-sale")),n.R7$(8),n.JRh(n.bMT(68,35,"indirect-clients.add")))},dependencies:[M.BC,M.vS,e.Jm,e.W9,e.Q8,e.YW,e.OL,e.eU,e.iq,e.KW,e.Ax,e.Hp,e.uz,e.he,e.nf,e.FH,e.To,e.Ki,e.S1,e.ds,e.Jq,e.qW,e.Zx,e.BC,e.CF,e.Gw,e.N7,u.YU,u.Sq,u.bT,u.B3,f.Wk,b.D9,l.c,_.j8,_.i8],styles:['@charset "UTF-8";*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{display:flex;align-items:center;background:var(--clr-white)}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;color:#0b305c}.active[_ngcontent-%COMP%]{color:#6d839d;border-bottom:3px solid #419CFB}.active[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#0b305c}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700!important;margin:auto}.title[_ngcontent-%COMP%]{font-size:calc(41 * var(--res));color:#6d839d;font-family:Mont SemiBold}.header[_ngcontent-%COMP%]{--background: #D5DFEB;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.header[_ngcontent-%COMP%]   .img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-label[_ngcontent-%COMP%]{display:grid!important}ion-label[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1rem;color:#0b305c;font-weight:800}ion-label[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.75rem;color:#8597ad;margin-top:8px}ion-content[_ngcontent-%COMP%]{--background: #D5DFEB}ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]{background:white;border-radius:5px;box-shadow:0 4px 4px #00000017;width:calc(100% - 48px);margin:13px auto;padding:10px 20px;display:flex;justify-content:space-between}ion-content[_ngcontent-%COMP%]   .function[_ngcontent-%COMP%]{display:inline-block;width:100px;text-align:center;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:700;color:#0b305c;padding:5px 10px;border-radius:30px;margin:auto;font-size:14px;font-weight:500}ion-content[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{margin-top:10vh;display:flex;flex-direction:column;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]{margin-bottom:3.8rem;gap:.8rem;align-items:flex-start;background:#ffffff;width:8.6em;border-radius:5px;padding-left:10px;padding-bottom:10px;padding-top:10px}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]{display:flex;gap:5px;align-items:center}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#fff}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.5rem;color:#0b305c;text-align:center;font-size:14px;font-weight:800!important}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]{position:relative;width:50px;height:50px}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-fab-button[color=primary][_ngcontent-%COMP%]{--background: #4caf50;--color: white}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-fab-button[color=secondary][_ngcontent-%COMP%]{--background: #2196f3;--color: white}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-fab-button[color=danger][_ngcontent-%COMP%]{--background: #f44336;--color: white}.buttons[_ngcontent-%COMP%]{display:flex;margin:6px 24px 6px 0;justify-content:space-between}.buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent !important;--box-shadow: none;color:#0b305c;border:none;--padding: auto}.buttons[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#419cfb}.dropdown-menu[_ngcontent-%COMP%]{position:absolute;background:#F1F2F4;--text-align: left;right:-6px;box-shadow:0 2px 10px #0003;z-index:10}.dropdown-menu[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--background: transparent;text-align:left}.dropdown-menu[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{padding:auto;margin:auto}.dropdown-menu[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border: 1px solid #D2DBE7;--background: #F1F2F4}.dropdown-menu[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:hover{background:#f0f0f0}.dropdown-menu[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{align-items:center;justify-content:flex-start;--background: #F1F2F4;--border: 1px solid #D2DBE7;text-align:left;--border-radius: none}.dropdown-menu[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px}ion-list[_ngcontent-%COMP%]{display:grid;margin-right:24px}ion-list[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: #F1F2F4;--border: 1px solid #D2DBE7;text-align:left;--border-radius: none;width:100%}ion-list[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{text-align:left;padding:2px}.icon-container[_ngcontent-%COMP%]{position:relative;margin:auto 0}.custom-back-button[_ngcontent-%COMP%]::part(back-button-text){display:none}.success[_ngcontent-%COMP%]{color:green;text-transform:capitalize}.danger[_ngcontent-%COMP%]{color:red;text-transform:capitalize}.blue[_ngcontent-%COMP%]{color:#419cfb;text-transform:capitalize}.skeleton[_ngcontent-%COMP%]{height:3.5em;width:100%;margin-bottom:1rem;display:flex;align-items:center;justify-content:center}.skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:90%;height:100%}.custom-popover[_ngcontent-%COMP%]{--width: 150px}.small-icon[_ngcontent-%COMP%]{font-size:18px}.popover-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 8px}.popover[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}.popover[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{margin-top:4px;font-size:14px;color:var(--ion-color-medium)}ion-searchbar[_ngcontent-%COMP%]{width:calc(100% - 48px);margin:auto}']})}}return r})();var U=a(36594);function W(r,p){1&r&&n.nrm(0,"map-marker",5),2&r&&n.Y8G("position",p.$implicit)}const Y=[{path:"",component:A},{path:"detail/:id",loadChildren:()=>a.e(8206).then(a.bind(a,68206)).then(r=>r.UserDetailPageModule)},{path:"location-view",component:(()=>{class r{constructor(t){this.userSrv=t,this.location=(0,n.WQX)(u.aZ),this.userLocations=[],this.center={lat:0,lng:0},this.markers=[]}ngOnInit(){var t=this;return(0,g.A)(function*(){t.userLocations=t.userSrv.currentUserLocalisation,t.userLocations.length>0?(t.markers=t.userLocations.map(o=>({lat:o.latitude,lng:o.longitude})),t.center={lat:t.markers[0].lat,lng:t.markers[0].lng}):(t.latitude=t.userSrv.currentUserParticular?.localisation?.latitude,t.longitude=t.userSrv.currentUserParticular?.localisation?.longitude,t.latitude&&t.longitude&&(t.center={lat:t.latitude,lng:t.longitude},t.markers.push({lat:t.latitude,lng:t.longitude})))})()}closeModal(){this.userSrv.currentUserLocalisation=[],this.location.back()}static{this.\u0275fac=function(o){return new(o||r)(n.rXU(k.D))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-location-view"]],decls:9,vars:3,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",1,"img",3,"click"],[2,"height","100%","width","100%"],["height","100%","width","100%",3,"center","zoom"],[3,"position",4,"ngFor","ngForOf"],[3,"position"]],template:function(o,i){1&o&&(n.j41(0,"ion-header")(1,"div",0)(2,"ion-img",1),n.bIt("click",function(){return i.closeModal()}),n.k0s(),n.j41(3,"ion-title"),n.EFF(4,"Localisation de l'utilisateur"),n.k0s()()(),n.j41(5,"ion-content")(6,"div",2)(7,"google-map",3),n.DNE(8,W,1,1,"map-marker",4),n.k0s()()()),2&o&&(n.R7$(7),n.Y8G("center",i.center)("zoom",15),n.R7$(1),n.Y8G("ngForOf",i.markers))},dependencies:[e.W9,e.eU,e.KW,e.BC,u.Sq,U.u6,U.fU],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{display:flex;align-items:center;background:var(--clr-white);height:2em}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;color:#0b305c}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700!important;margin:auto}.header[_ngcontent-%COMP%]{--background: #D5DFEB;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.header[_ngcontent-%COMP%]   .img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}"]})}}return r})()},{path:"whole-sale-detail/:id",loadChildren:()=>a.e(8489).then(a.bind(a,18489)).then(r=>r.WholeSaleDetailPageModule)}];let N=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[f.iI.forChild(Y),f.iI]})}}return r})();var V=a(93887);let X=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[M.YN,e.bv,u.MD,M.X1,b.h,N,U.rJ,V.G]})}}return r})()},91285:(S,v,a)=>{a.d(v,{y:()=>k});var M=a(99987),e=a(2978),u=a(77897),f=a(62049),g=a(82571),n=a(37222),b=a(56610);function y(d,I){if(1&d){const l=e.RV6();e.j41(0,"div",6)(1,"label",7),e.EFF(2),e.k0s(),e.j41(3,"ion-item",8)(4,"ion-textarea",9),e.bIt("ngModelChange",function(s){e.eBV(l);const x=e.XpG();return e.Njj(x.annulationMessage=s)}),e.k0s()()()}if(2&d){const l=e.XpG();e.R7$(2),e.JRh(null==l.dataModal?null:l.dataModal.message),e.R7$(2),e.Y8G("ngModel",l.annulationMessage)}}function P(d,I){if(1&d){const l=e.RV6();e.j41(0,"ion-button",10),e.bIt("click",function(){e.eBV(l);const s=e.XpG();return e.Njj(s.cancel())}),e.j41(1,"ion-label"),e.EFF(2),e.nI1(3,"titlecase"),e.k0s()()}if(2&d){const l=e.XpG();e.R7$(2),e.JRh(e.bMT(3,1,null==l.dataModal?null:l.dataModal.cancelButton))}}const w=function(d){return{"annulation-mode":d}},O=function(d){return{"single-button":d}};let k=(()=>{class d{constructor(l,_,s){this.modalCtrl=l,this.translateService=_,this.commonSrv=s,this.annulationMessage=""}ngOnInit(){}cancel(){return this.modalCtrl.dismiss(null,"annuler")}confirm(){let l=this.annulationMessage;this.dataModal.isAnnulation&&!this.annulationMessage.trim()&&(l=this.translateService.currentLang===M.T.French?"\xc0 la demande du client":"At the customers request"),this.dataModal.handler(l),this.modalCtrl.dismiss(this.dataModal.isAnnulation?{message:l}:null,"confirm")}static{this.\u0275fac=function(_){return new(_||d)(e.rXU(u.W3),e.rXU(f.E),e.rXU(g.h))}}static{this.\u0275cmp=e.VBU({type:d,selectors:[["app-base-modal"]],inputs:{dataModal:"dataModal"},decls:12,vars:12,consts:[["id","container",1,"scroller-container",3,"ngClass"],[1,"contain-text"],["class","message-container",4,"ngIf"],[1,"btn-validate",3,"ngClass"],["fill","solid","class","cancel",3,"click",4,"ngIf"],["fill","solid","color","primary",1,"yes",3,"click"],[1,"message-container"],[1,"message"],[1,"message-input"],["rows","4",1,"custom-textarea",3,"ngModel","ngModelChange"],["fill","solid",1,"cancel",3,"click"]],template:function(_,s){1&_&&(e.j41(0,"section",0)(1,"div",1)(2,"label"),e.EFF(3),e.nrm(4,"span"),e.k0s()(),e.DNE(5,y,5,2,"div",2),e.j41(6,"div",3),e.DNE(7,P,4,3,"ion-button",4),e.j41(8,"ion-button",5),e.bIt("click",function(){return s.confirm()}),e.j41(9,"ion-label"),e.EFF(10),e.nI1(11,"titlecase"),e.k0s()()()()),2&_&&(e.Y8G("ngClass",e.eq3(8,w,null==s.dataModal?null:s.dataModal.isAnnulation)),e.R7$(3),e.SpI(" ",null==s.dataModal?null:s.dataModal.text," "),e.R7$(2),e.Y8G("ngIf",null==s.dataModal?null:s.dataModal.isAnnulation),e.R7$(1),e.Y8G("ngClass",e.eq3(10,O,null==s.dataModal?null:s.dataModal.isAnnulation)),e.R7$(1),e.Y8G("ngIf",!(null!=s.dataModal&&s.dataModal.isAnnulation)),e.R7$(3),e.JRh(e.bMT(11,6,null==s.dataModal?null:s.dataModal.confirmButton)))},dependencies:[n.BC,n.vS,u.Jm,u.uz,u.he,u.nc,u.Gw,b.YU,b.bT,b.PV],styles:['*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{height:100%;padding:var(--container-padding);background-color:#fff;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));display:flex;flex-direction:column}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]{text-align:center;font-family:var(--mont-regular);color:#143c5d;line-height:1;margin-bottom:var(--container-margin)}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:"#0B305C";font-size:20px;text-align:center;display:block;margin-bottom:16px;font-family:var(--mont-bold)}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#143c5d}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]{margin:14px 0}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{font-size:14px;font-weight:400;display:block;margin-bottom:8px;text-align:center;font-family:var(--mont-regular)}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]{--background: #f8f9fa;--border-radius: 8px;--padding-start: 12px;--padding-end: 12px;border:1px solid rgba(20,60,93,.5);border-radius:8px}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]   .custom-textarea[_ngcontent-%COMP%]{--padding-top: 8px;--padding-bottom: 8px;min-height:100px;font-size:14px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));display:flex;flex-direction:row;justify-content:center;margin-top:13px;gap:1em}#container[_ngcontent-%COMP%]   .btn-validate.single-button[_ngcontent-%COMP%]{gap:0}#container[_ngcontent-%COMP%]   .btn-validate.single-button[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:100%;max-width:none}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]{--background: #fff;width:120px;border-radius:10px;border:1px solid #143c5d;font-family:var(--mont-semibold);color:#143c5d;font-size:10px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:120px;border-radius:10px;border:1px solid #143c5d;font-family:var(--mont-semibold);font-size:10px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[disabled][_ngcontent-%COMP%]{opacity:.5}#container.annulation-mode[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 16px}#container.annulation-mode[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:100%;font-size:16px}']})}}return d})()}}]);