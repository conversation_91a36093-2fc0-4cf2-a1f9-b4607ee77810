// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-barcode-scanner')
    implementation project(':capacitor-mlkit-barcode-scanning')
    implementation project(':capacitor-app')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-geolocation')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-status-bar')
    implementation "com.android.support:support-v4:27.+"
}
apply from: "../../node_modules/phonegap-plugin-barcodescanner/src/android/barcodescanner.gradle"

if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
