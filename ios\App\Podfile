require_relative '../../node_modules/@capacitor/ios/scripts/pods_helpers'

platform :ios, '15.5'
use_frameworks!

# workaround to avoid Xcode caching of Pods that requires
# Product -> Clean Build Folder after new Cordova plugins installed
# Requires CocoaPods 1.6 or newer
install! 'cocoapods', :disable_input_output_paths => true

def capacitor_pods
  pod 'Capacitor', :path => '../../node_modules/@capacitor/ios'
  pod 'CapacitorCordova', :path => '../../node_modules/@capacitor/ios'
  pod 'CapacitorMlkitBarcodeScanning', :path => '../../node_modules/@capacitor-mlkit/barcode-scanning'
  pod 'CapacitorApp', :path => '../../node_modules/@capacitor/app'
  pod 'CapacitorCamera', :path => '../../node_modules/@capacitor/camera'
  pod 'CapacitorGeolocation', :path => '../../node_modules/@capacitor/geolocation'
end

target 'App' do
  capacitor_pods
  # Add your Pods here
end

post_install do |installer|
  assertDeploymentTarget(installer)
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      config.build_settings['ENABLE_USER_SCRIPT_SANDBOXING'] = 'NO'
    end
  end
  # Correction plus complète pour DT_TOOLCHAIN_DIR
  Dir.glob('Pods/Target Support Files/**/*.{xcconfig,sh}') do |file|
    text = File.read(file)
    new_text = text.gsub('${DT_TOOLCHAIN_DIR}', '${TOOLCHAIN_DIR}')
    new_text = new_text.gsub('"${DT_TOOLCHAIN_DIR}', '"${TOOLCHAIN_DIR}')
    new_text = new_text.gsub('$(DT_TOOLCHAIN_DIR)', '$(TOOLCHAIN_DIR)')
    if text != new_text
      File.write(file, new_text)
      puts "Fixed DT_TOOLCHAIN_DIR in #{file}"
    end
  end
  # Correction des permissions
  system("find 'Pods/Target Support Files' -name '*.sh' -exec chmod +x {} \\;")
end
